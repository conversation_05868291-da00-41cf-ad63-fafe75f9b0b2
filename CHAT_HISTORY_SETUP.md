# 聊天历史功能设置指南

## 功能概述

本次更新为智能对话助手添加了以下功能：

1. **历史聊天记录** - 自动保存用户的聊天历史
2. **聊天会话管理** - 查看和管理历史聊天会话
3. **继续聊天** - 从历史记录中继续之前的对话
4. **新建聊天** - 开始新的聊天会话
5. **聊天记录持久化** - 页面刷新后保持聊天内容

## 数据库设置

### 1. 执行SQL脚本

在你的MySQL数据库中执行以下SQL脚本来创建必要的表：

```sql
-- 聊天会话表
CREATE TABLE IF NOT EXISTS `chat_session` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `session_id` varchar(255) NOT NULL COMMENT 'DashScope会话ID',
  `title` varchar(500) DEFAULT NULL COMMENT '会话标题',
  `last_message` text COMMENT '最后一条消息',
  `message_count` int DEFAULT 0 COMMENT '消息数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 聊天历史记录表
CREATE TABLE IF NOT EXISTS `chat_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `question` text NOT NULL COMMENT '用户输入的问题',
  `answer` longtext COMMENT 'AI返回的答案',
  `session_id` varchar(255) NOT NULL COMMENT 'DashScope会话ID',
  `title` varchar(500) DEFAULT NULL COMMENT '聊天标题',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天历史记录表';
```

或者直接执行项目中的SQL文件：
```bash
mysql -u your_username -p your_database < src/main/resources/sql/chat_tables.sql
```

## 功能使用说明

### 1. 历史聊天

- 在聊天页面顶部点击 **"📋 历史聊天"** 按钮
- 查看所有历史聊天会话列表
- 每个会话显示标题、最后消息、消息数量和更新时间

### 2. 继续聊天

- 在历史聊天列表中点击 **"继续聊天"** 按钮
- 系统会加载该会话的完整聊天历史
- 可以基于历史上下文继续对话

### 3. 新建聊天

- 点击 **"➕ 新建聊天"** 按钮
- 清空当前聊天界面
- 开始全新的对话会话

### 4. 删除聊天会话

- 在历史聊天列表中点击 **"删除"** 按钮
- 确认后将删除该会话及其所有聊天记录

### 5. 自动保存功能

- 聊天记录自动保存到数据库
- 页面刷新后自动恢复当前聊天内容
- 使用localStorage实现本地持久化

## 技术实现

### 后端新增组件

1. **实体类**
   - `ChatHistory` - 聊天历史记录
   - `ChatSession` - 聊天会话

2. **数据访问层**
   - `ChatHistoryMapper` - 聊天历史数据访问
   - `ChatSessionMapper` - 聊天会话数据访问

3. **业务逻辑层**
   - `ChatHistoryService` - 聊天历史业务逻辑
   - `ChatHistoryServiceImpl` - 业务逻辑实现

4. **控制器接口**
   - `GET /api/chat/sessions` - 获取聊天会话列表
   - `GET /api/chat/history/{sessionId}` - 获取指定会话历史
   - `DELETE /api/chat/sessions/{sessionId}` - 删除聊天会话

### 前端新增功能

1. **UI组件**
   - 历史聊天模态框
   - 新建聊天按钮
   - 历史聊天按钮

2. **JavaScript功能**
   - 聊天记录本地存储
   - 页面刷新恢复
   - 历史记录加载和管理

## 启动应用

1. 确保数据库表已创建
2. 启动应用：
   ```bash
   ./start.sh
   ```
3. 访问：`http://localhost:7011/aitool/`

## 注意事项

1. 确保数据库连接配置正确
2. 聊天历史功能需要用户登录后才能使用
3. localStorage有存储大小限制，建议定期清理
4. 删除聊天会话操作不可恢复，请谨慎操作

## 故障排除

1. **历史记录不显示**
   - 检查数据库表是否创建成功
   - 确认用户已登录
   - 查看浏览器控制台错误信息

2. **聊天记录不保存**
   - 检查数据库连接
   - 确认ChatHistoryService注入正常
   - 查看后端日志错误信息

3. **页面刷新后聊天记录丢失**
   - 检查浏览器是否支持localStorage
   - 确认没有禁用本地存储
   - 清除浏览器缓存后重试