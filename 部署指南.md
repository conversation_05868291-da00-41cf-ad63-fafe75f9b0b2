# zjzh-agent 部署指南

## 项目概述

**zjzh-agent** 是一个基于 Spring Boot 的智能代理应用，集成了阿里云 DashScope AI 服务。

## 项目信息

- **项目名称**: zjzh-agent (智能代理应用)
- **技术栈**: Spring Boot 3.0.0 + Java 17 + MySQL + MyBatis Plus
- **服务端口**: 7011
- **数据库**: MySQL (ai_tools)

## 系统要求

### 必需环境
- **Java**: JDK 17 或更高版本
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **构建工具**: Maven 3.6+

### 可选环境
- **缓存**: Redis 6.0+ (配置中有 Redis 相关设置)

## 部署步骤

### 1. 环境准备

#### 1.1 检查 Java 版本
```bash
java -version
```
确保版本为 JDK 17 或更高。

#### 1.2 准备 MySQL 数据库
```bash
# 连接 MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE ai_tools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户（可选）
CREATE USER 'zjzh_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ai_tools.* TO 'zjzh_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 配置文件设置

项目配置文件位于 `src/main/resources/application.yml`，主要配置项：

```yaml
server:
  port: 7011

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: root
    password: root111111

# DashScope AI 配置
dashscope:
  api-key: sk-4dc7b44c2e6c4f88b9f5664d5a81708c
  app-id: 3a1d03791d204389b76431c36cbc5dff
```

**注意**: 根据实际环境修改数据库连接信息和 API 密钥。

### 3. 编译和打包

#### 3.1 使用 Maven Wrapper 打包
```bash
# 在项目根目录执行
./mvnw clean package -DskipTests
```

#### 3.2 如果遇到权限问题
```bash
# macOS/Linux 下给执行权限
chmod +x mvnw
```

### 4. 运行应用

#### 方式一：运行 JAR 文件
```bash
java -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar
```

#### 方式二：使用 Maven 直接运行
```bash
./mvnw spring-boot:run
```

#### 方式三：指定配置文件运行
```bash
java -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar --spring.config.location=classpath:/application.yml
```

### 5. 部署验证

#### 5.1 健康检查
访问健康检查接口验证应用是否正常启动：
```bash
curl http://localhost:7011/inner/health/alive
```
正常返回：`ok`

#### 5.2 检查日志
观察控制台输出，确认：
- 数据库连接成功
- 端口 7011 监听正常
- 应用启动完成

## 生产环境部署建议

### 1. 服务配置
```bash
# 后台运行
nohup java -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar > app.log 2>&1 &

# 或使用 systemd 服务
sudo systemctl enable zjzh-agent
sudo systemctl start zjzh-agent
```

### 2. 性能调优
```bash
# JVM 参数示例
java -Xms512m -Xmx2g -XX:+UseG1GC \
     -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar
```

### 3. 安全配置
- 修改默认数据库密码
- 配置防火墙规则
- 使用 HTTPS（如需要）
- 保护 API 密钥安全

## 常见问题

### 1. 端口被占用
```bash
# 查看端口占用
lsof -i :7011

# 修改端口
--server.port=8080
```

### 2. 数据库连接失败
- 检查 MySQL 服务是否启动
- 验证数据库连接信息
- 确认数据库用户权限

### 3. Maven 依赖下载失败
```bash
# 清理并重新下载依赖
./mvnw clean
./mvnw dependency:resolve
```

## 开发环境

### 开发模式运行
```bash
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### 热重载
项目已集成 Spring Boot DevTools，支持代码热重载。

## 项目结构

```
zjzh-agent/
├── src/
│   ├── main/
│   │   ├── java/com/zjch/agent/
│   │   │   ├── config/          # 配置类
│   │   │   ├── controller/      # 控制器
│   │   │   ├── service/         # 服务层
│   │   │   ├── mapper/          # 数据访问层
│   │   │   └── ZjzhAgentApplication.java
│   │   └── resources/
│   │       └── application.yml
│   └── test/
├── target/                      # 编译输出目录
├── pom.xml                     # Maven 配置
└── mvnw                        # Maven Wrapper
```

## 技术支持

如遇到部署问题，请检查：
1. Java 版本兼容性
2. 数据库连接配置
3. 端口占用情况
4. 防火墙设置
5. 日志错误信息