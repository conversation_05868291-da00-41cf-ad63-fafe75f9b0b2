<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cascader 级联选择器组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 级联选择器基础样式 */
        .form-cascader-container {
            position: relative;
            width: 100%;
            margin-bottom: 20px;
        }

        .form-cascader-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-cascader-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-cascader-input:hover {
            border-color: #3b82f6;
        }

        .form-cascader-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-cascader-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            transition: transform 0.3s ease;
            pointer-events: none;
        }

        .form-cascader-arrow::before {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            border: 2px solid #6b7280;
            border-left: none;
            border-top: none;
            transform: rotate(45deg);
            top: 2px;
            left: 4px;
        }

        .form-cascader-container.open .form-cascader-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        /* 下拉面板 */
        .form-cascader-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 300px;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .form-cascader-container.open .form-cascader-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .form-cascader-panels {
            display: flex;
            min-height: 200px;
        }

        .form-cascader-panel {
            flex: 1;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            min-width: 150px;
        }

        .form-cascader-panel:last-child {
            border-right: none;
        }

        .form-cascader-option {
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
        }

        .form-cascader-option:hover {
            background-color: #f8fafc;
        }

        .form-cascader-option.active {
            background-color: #eff6ff;
            color: #3b82f6;
            font-weight: 500;
        }

        .form-cascader-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .form-cascader-option-arrow {
            width: 12px;
            height: 12px;
            opacity: 0.6;
        }

        .form-cascader-option-arrow::before {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            border: 1px solid currentColor;
            border-left: none;
            border-top: none;
            transform: rotate(-45deg);
        }

        /* 搜索框 */
        .form-cascader-search {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .form-cascader-search input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-cascader-search input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        /* 多选模式 */
        .form-cascader-multiple .form-cascader-input {
            min-height: 44px;
            padding: 8px 40px 8px 8px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 4px;
        }

        .form-cascader-tag {
            background-color: #eff6ff;
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-cascader-tag-close {
            cursor: pointer;
            font-weight: bold;
            opacity: 0.7;
        }

        .form-cascader-tag-close:hover {
            opacity: 1;
        }

        .form-cascader-placeholder {
            color: #9ca3af;
            flex: 1;
            min-width: 100px;
        }

        /* 尺寸变体 */
        .form-cascader-small .form-cascader-input {
            padding: 8px 32px 8px 12px;
            font-size: 14px;
        }

        .form-cascader-large .form-cascader-input {
            padding: 16px 48px 16px 20px;
            font-size: 18px;
        }

        /* 状态样式 */
        .form-cascader-disabled .form-cascader-input {
            background-color: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
        }

        .form-cascader-error .form-cascader-input {
            border-color: #ef4444;
        }

        .form-cascader-error .form-cascader-input:focus {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-cascader-panels {
                flex-direction: column;
            }
            
            .form-cascader-panel {
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
                max-height: 150px;
            }
            
            .form-cascader-panel:last-child {
                border-bottom: none;
            }
        }

        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-cascader-input {
                font-size: 16px;
                padding: 12px 36px 12px 14px;
            }
        }

        /* 演示样式 */
        .demo-result {
            margin-top: 16px;
            padding: 12px;
            background-color: #f8fafc;
            border-radius: 6px;
            font-size: 14px;
            color: #4b5563;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Cascader 级联选择器</h1>
        <p class="demo-description">多级联动选择组件，适用于地区、分类等层级数据选择</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="form-cascader-container" id="basicCascader">
                <label class="form-cascader-label">选择地区</label>
                <div class="form-cascader-input" onclick="toggleCascader('basicCascader')">
                    <span class="form-cascader-placeholder">请选择地区</span>
                </div>
                <div class="form-cascader-arrow"></div>
                <div class="form-cascader-dropdown">
                    <div class="form-cascader-panels">
                        <div class="form-cascader-panel" id="panel-0"></div>
                        <div class="form-cascader-panel" id="panel-1"></div>
                        <div class="form-cascader-panel" id="panel-2"></div>
                    </div>
                </div>
            </div>
            <div class="demo-result" id="basicResult">选择结果：未选择</div>
        </div>

        <div class="demo-section">
            <h3>带搜索功能</h3>
            <div class="form-cascader-container" id="searchCascader">
                <label class="form-cascader-label">选择分类</label>
                <div class="form-cascader-input" onclick="toggleCascader('searchCascader')">
                    <span class="form-cascader-placeholder">请选择分类</span>
                </div>
                <div class="form-cascader-arrow"></div>
                <div class="form-cascader-dropdown">
                    <div class="form-cascader-search">
                        <input type="text" placeholder="搜索分类..." oninput="searchOptions(this.value)">
                    </div>
                    <div class="form-cascader-panels">
                        <div class="form-cascader-panel" id="search-panel-0"></div>
                        <div class="form-cascader-panel" id="search-panel-1"></div>
                        <div class="form-cascader-panel" id="search-panel-2"></div>
                    </div>
                </div>
            </div>
            <div class="demo-result" id="searchResult">选择结果：未选择</div>
        </div>

        <div class="demo-grid">
            <div>
                <h3>多选模式</h3>
                <div class="form-cascader-container form-cascader-multiple" id="multipleCascader">
                    <label class="form-cascader-label">选择技能</label>
                    <div class="form-cascader-input" onclick="toggleCascader('multipleCascader')">
                        <span class="form-cascader-placeholder">请选择技能</span>
                    </div>
                    <div class="form-cascader-arrow"></div>
                    <div class="form-cascader-dropdown">
                        <div class="form-cascader-panels">
                            <div class="form-cascader-panel" id="multi-panel-0"></div>
                            <div class="form-cascader-panel" id="multi-panel-1"></div>
                        </div>
                    </div>
                </div>
                <div class="demo-result" id="multipleResult">选择结果：未选择</div>
            </div>

            <div>
                <h3>不同尺寸</h3>
                <div class="form-cascader-container form-cascader-small" id="smallCascader">
                    <label class="form-cascader-label">小尺寸</label>
                    <div class="form-cascader-input" onclick="toggleCascader('smallCascader')">
                        <span class="form-cascader-placeholder">请选择</span>
                    </div>
                    <div class="form-cascader-arrow"></div>
                    <div class="form-cascader-dropdown">
                        <div class="form-cascader-panels">
                            <div class="form-cascader-panel" id="small-panel-0"></div>
                        </div>
                    </div>
                </div>

                <div class="form-cascader-container form-cascader-large" style="margin-top: 16px;">
                    <label class="form-cascader-label">大尺寸</label>
                    <div class="form-cascader-input" onclick="toggleCascader(this.parentElement.id)">
                        <span class="form-cascader-placeholder">请选择</span>
                    </div>
                    <div class="form-cascader-arrow"></div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>禁用状态</h3>
            <div class="form-cascader-container form-cascader-disabled">
                <label class="form-cascader-label">禁用状态</label>
                <div class="form-cascader-input">
                    <span class="form-cascader-placeholder">已禁用</span>
                </div>
                <div class="form-cascader-arrow"></div>
            </div>
        </div>

        <div class="demo-section">
            <h3>错误状态</h3>
            <div class="form-cascader-container form-cascader-error">
                <label class="form-cascader-label">错误状态</label>
                <div class="form-cascader-input" onclick="toggleCascader(this.parentElement.id)">
                    <span class="form-cascader-placeholder">请选择正确的选项</span>
                </div>
                <div class="form-cascader-arrow"></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const regionData = [
            {
                label: '北京市',
                value: 'beijing',
                children: [
                    {
                        label: '东城区',
                        value: 'dongcheng',
                        children: [
                            { label: '王府井街道', value: 'wangfujing' },
                            { label: '东华门街道', value: 'donghuamen' }
                        ]
                    },
                    {
                        label: '西城区',
                        value: 'xicheng',
                        children: [
                            { label: '西长安街街道', value: 'xichanganjie' },
                            { label: '新街口街道', value: 'xinjiekou' }
                        ]
                    }
                ]
            },
            {
                label: '上海市',
                value: 'shanghai',
                children: [
                    {
                        label: '黄浦区',
                        value: 'huangpu',
                        children: [
                            { label: '南京东路街道', value: 'nanjingdonglu' },
                            { label: '外滩街道', value: 'waitan' }
                        ]
                    }
                ]
            }
        ];

        const categoryData = [
            {
                label: '电子产品',
                value: 'electronics',
                children: [
                    {
                        label: '手机',
                        value: 'phone',
                        children: [
                            { label: 'iPhone', value: 'iphone' },
                            { label: 'Android', value: 'android' }
                        ]
                    },
                    {
                        label: '电脑',
                        value: 'computer',
                        children: [
                            { label: '笔记本', value: 'laptop' },
                            { label: '台式机', value: 'desktop' }
                        ]
                    }
                ]
            },
            {
                label: '服装',
                value: 'clothing',
                children: [
                    {
                        label: '男装',
                        value: 'mens',
                        children: [
                            { label: '衬衫', value: 'shirt' },
                            { label: '裤子', value: 'pants' }
                        ]
                    }
                ]
            }
        ];

        const skillData = [
            {
                label: '前端开发',
                value: 'frontend',
                children: [
                    { label: 'JavaScript', value: 'javascript' },
                    { label: 'Vue.js', value: 'vue' },
                    { label: 'React', value: 'react' }
                ]
            },
            {
                label: '后端开发',
                value: 'backend',
                children: [
                    { label: 'Node.js', value: 'nodejs' },
                    { label: 'Python', value: 'python' },
                    { label: 'Java', value: 'java' }
                ]
            }
        ];

        // 当前选择状态
        const cascaderStates = {
            basicCascader: { data: regionData, selected: [], multiple: false },
            searchCascader: { data: categoryData, selected: [], multiple: false },
            multipleCascader: { data: skillData, selected: [], multiple: true },
            smallCascader: { data: [{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }], selected: [], multiple: false }
        };

        // 初始化级联选择器
        function initCascader() {
            Object.keys(cascaderStates).forEach(id => {
                const container = document.getElementById(id);
                if (container) {
                    renderPanel(id, 0, cascaderStates[id].data);
                }
            });
        }

        // 切换下拉面板
        function toggleCascader(id) {
            const container = document.getElementById(id);
            if (!container || container.classList.contains('form-cascader-disabled')) return;

            // 关闭其他打开的级联选择器
            document.querySelectorAll('.form-cascader-container.open').forEach(el => {
                if (el.id !== id) {
                    el.classList.remove('open');
                }
            });

            container.classList.toggle('open');
        }

        // 渲染面板选项
        function renderPanel(cascaderId, panelIndex, data, parentPath = []) {
            const panelId = `${cascaderId.includes('search') ? 'search-' : cascaderId.includes('multi') ? 'multi-' : cascaderId.includes('small') ? 'small-' : ''}panel-${panelIndex}`;
            const panel = document.getElementById(panelId);
            if (!panel) return;

            panel.innerHTML = '';

            data.forEach(item => {
                const option = document.createElement('div');
                option.className = 'form-cascader-option';
                option.innerHTML = `
                    <span>${item.label}</span>
                    ${item.children ? '<div class="form-cascader-option-arrow"></div>' : ''}
                `;

                option.onclick = () => selectOption(cascaderId, panelIndex, item, parentPath);
                panel.appendChild(option);
            });
        }

        // 选择选项
        function selectOption(cascaderId, panelIndex, item, parentPath) {
            const state = cascaderStates[cascaderId];
            const currentPath = [...parentPath, item];

            // 清除后续面板
            for (let i = panelIndex + 1; i < 3; i++) {
                const nextPanelId = `${cascaderId.includes('search') ? 'search-' : cascaderId.includes('multi') ? 'multi-' : cascaderId.includes('small') ? 'small-' : ''}panel-${i}`;
                const nextPanel = document.getElementById(nextPanelId);
                if (nextPanel) {
                    nextPanel.innerHTML = '';
                }
            }

            // 更新当前面板选中状态
            const currentPanelId = `${cascaderId.includes('search') ? 'search-' : cascaderId.includes('multi') ? 'multi-' : cascaderId.includes('small') ? 'small-' : ''}panel-${panelIndex}`;
            const currentPanel = document.getElementById(currentPanelId);
            if (currentPanel) {
                currentPanel.querySelectorAll('.form-cascader-option').forEach(opt => {
                    opt.classList.remove('active', 'selected');
                });
                event.currentTarget.classList.add('active');
            }

            if (item.children && item.children.length > 0) {
                // 有子选项，渲染下一级
                renderPanel(cascaderId, panelIndex + 1, item.children, currentPath);
            } else {
                // 叶子节点，完成选择
                if (state.multiple) {
                    // 多选模式
                    const existingIndex = state.selected.findIndex(s => s.value === item.value);
                    if (existingIndex > -1) {
                        state.selected.splice(existingIndex, 1);
                    } else {
                        state.selected.push({ path: currentPath, value: item.value, label: currentPath.map(p => p.label).join(' / ') });
                    }
                    updateMultipleDisplay(cascaderId);
                } else {
                    // 单选模式
                    state.selected = [{ path: currentPath, value: item.value, label: currentPath.map(p => p.label).join(' / ') }];
                    updateSingleDisplay(cascaderId);
                    toggleCascader(cascaderId); // 关闭下拉面板
                }
                
                updateResult(cascaderId);
            }
        }

        // 更新单选显示
        function updateSingleDisplay(cascaderId) {
            const container = document.getElementById(cascaderId);
            const input = container.querySelector('.form-cascader-input span');
            const state = cascaderStates[cascaderId];

            if (state.selected.length > 0) {
                input.textContent = state.selected[0].label;
                input.classList.remove('form-cascader-placeholder');
            } else {
                input.textContent = '请选择';
                input.classList.add('form-cascader-placeholder');
            }
        }

        // 更新多选显示
        function updateMultipleDisplay(cascaderId) {
            const container = document.getElementById(cascaderId);
            const input = container.querySelector('.form-cascader-input');
            const state = cascaderStates[cascaderId];

            input.innerHTML = '';

            state.selected.forEach(item => {
                const tag = document.createElement('div');
                tag.className = 'form-cascader-tag';
                tag.innerHTML = `
                    <span>${item.label}</span>
                    <span class="form-cascader-tag-close" onclick="removeTag('${cascaderId}', '${item.value}')">&times;</span>
                `;
                input.appendChild(tag);
            });

            const placeholder = document.createElement('span');
            placeholder.className = 'form-cascader-placeholder';
            placeholder.textContent = state.selected.length > 0 ? '' : '请选择技能';
            input.appendChild(placeholder);
        }

        // 移除标签
        function removeTag(cascaderId, value) {
            event.stopPropagation();
            const state = cascaderStates[cascaderId];
            state.selected = state.selected.filter(item => item.value !== value);
            updateMultipleDisplay(cascaderId);
            updateResult(cascaderId);
        }

        // 更新结果显示
        function updateResult(cascaderId) {
            const resultId = cascaderId.replace('Cascader', 'Result');
            const resultElement = document.getElementById(resultId);
            const state = cascaderStates[cascaderId];

            if (resultElement) {
                if (state.selected.length > 0) {
                    const labels = state.selected.map(item => item.label).join(', ');
                    resultElement.textContent = `选择结果：${labels}`;
                } else {
                    resultElement.textContent = '选择结果：未选择';
                }
            }
        }

        // 搜索功能
        function searchOptions(keyword) {
            // 这里可以实现搜索逻辑
            console.log('搜索关键词:', keyword);
        }

        // 点击外部关闭下拉面板
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.form-cascader-container')) {
                document.querySelectorAll('.form-cascader-container.open').forEach(container => {
                    container.classList.remove('open');
                });
            }
        });

        // 初始化
        initCascader();
    </script>
</body>
</html>
