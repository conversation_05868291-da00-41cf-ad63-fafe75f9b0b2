<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistic 统计数值组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 统计数值基础样式 */
        .form-statistic-container {
            margin-bottom: 24px;
        }

        .form-statistic-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .form-statistic-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .form-statistic-title {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-statistic-value {
            font-size: 32px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .form-statistic-prefix {
            font-size: 20px;
            margin-right: 4px;
        }

        .form-statistic-suffix {
            font-size: 20px;
            margin-left: 4px;
        }

        .form-statistic-trend {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            margin-top: 8px;
        }

        .form-statistic-trend-icon {
            width: 12px;
            height: 12px;
        }

        .form-statistic-trend.up {
            color: #10b981;
        }

        .form-statistic-trend.down {
            color: #ef4444;
        }

        .form-statistic-trend.neutral {
            color: #6b7280;
        }

        /* 颜色主题 */
        .form-statistic-primary .form-statistic-value {
            color: #3b82f6;
        }

        .form-statistic-success .form-statistic-value {
            color: #10b981;
        }

        .form-statistic-warning .form-statistic-value {
            color: #f59e0b;
        }

        .form-statistic-danger .form-statistic-value {
            color: #ef4444;
        }

        .form-statistic-purple .form-statistic-value {
            color: #8b5cf6;
        }

        /* 卡片样式 */
        .form-statistic-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .form-statistic-card .form-statistic-title {
            color: rgba(255, 255, 255, 0.8);
        }

        .form-statistic-card .form-statistic-value {
            color: white;
        }

        .form-statistic-card .form-statistic-trend {
            color: rgba(255, 255, 255, 0.9);
        }

        /* 带图标的统计 */
        .form-statistic-with-icon {
            display: flex;
            align-items: center;
            text-align: left;
            padding: 20px;
        }

        .form-statistic-icon {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .form-statistic-icon.primary {
            background-color: #eff6ff;
            color: #3b82f6;
        }

        .form-statistic-icon.success {
            background-color: #ecfdf5;
            color: #10b981;
        }

        .form-statistic-icon.warning {
            background-color: #fffbeb;
            color: #f59e0b;
        }

        .form-statistic-icon.danger {
            background-color: #fef2f2;
            color: #ef4444;
        }

        .form-statistic-content {
            flex: 1;
        }

        /* 进度条样式 */
        .form-statistic-progress {
            margin-top: 12px;
        }

        .form-statistic-progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .form-statistic-progress-fill {
            height: 100%;
            background-color: #3b82f6;
            border-radius: 3px;
            transition: width 0.8s ease;
        }

        .form-statistic-progress-text {
            display: flex;
            justify-content: space-between;
            margin-top: 4px;
            font-size: 12px;
            color: #6b7280;
        }

        /* 响应式网格 */
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .demo-grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .demo-grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        /* 动画效果 */
        .form-statistic-animated .form-statistic-value {
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
            }
            
            .form-statistic-item {
                padding: 20px 16px;
            }
            
            .form-statistic-value {
                font-size: 28px;
            }
        }

        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .form-statistic-item {
                padding: 16px;
            }
            
            .form-statistic-value {
                font-size: 24px;
            }
            
            .form-statistic-with-icon {
                padding: 16px;
            }
            
            .form-statistic-icon {
                width: 40px;
                height: 40px;
                margin-right: 12px;
                font-size: 20px;
            }
        }

        /* 演示样式 */
        .demo-controls {
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-button {
            padding: 8px 16px;
            margin: 0 8px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .demo-button:hover {
            background-color: #2563eb;
        }

        .demo-button:active {
            background-color: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Statistic 统计数值</h1>
        <p class="demo-description">数值统计展示组件，支持动画效果、趋势指示和多种样式</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="demo-grid demo-grid-3">
                <div class="form-statistic-item">
                    <div class="form-statistic-title">总用户数</div>
                    <div class="form-statistic-value" id="userCount">1,234</div>
                    <div class="form-statistic-trend up">
                        <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7,14L12,9L17,14H7Z"/>
                        </svg>
                        <span>12.5%</span>
                    </div>
                </div>

                <div class="form-statistic-item">
                    <div class="form-statistic-title">今日访问</div>
                    <div class="form-statistic-value" id="visitCount">5,678</div>
                    <div class="form-statistic-trend down">
                        <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7,10L12,15L17,10H7Z"/>
                        </svg>
                        <span>3.2%</span>
                    </div>
                </div>

                <div class="form-statistic-item">
                    <div class="form-statistic-title">转化率</div>
                    <div class="form-statistic-value" id="conversionRate">
                        <span class="form-statistic-value">23.5</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-trend neutral">
                        <span>持平</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>不同颜色主题</h3>
            <div class="demo-grid demo-grid-3">
                <div class="form-statistic-item form-statistic-primary">
                    <div class="form-statistic-title">活跃用户</div>
                    <div class="form-statistic-value">2,468</div>
                </div>

                <div class="form-statistic-item form-statistic-success">
                    <div class="form-statistic-title">成功订单</div>
                    <div class="form-statistic-value">1,357</div>
                </div>

                <div class="form-statistic-item form-statistic-warning">
                    <div class="form-statistic-title">待处理</div>
                    <div class="form-statistic-value">246</div>
                </div>

                <div class="form-statistic-item form-statistic-danger">
                    <div class="form-statistic-title">失败订单</div>
                    <div class="form-statistic-value">89</div>
                </div>

                <div class="form-statistic-item form-statistic-purple">
                    <div class="form-statistic-title">新增关注</div>
                    <div class="form-statistic-value">1,024</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>带图标的统计</h3>
            <div class="demo-grid demo-grid-2">
                <div class="form-statistic-item form-statistic-with-icon">
                    <div class="form-statistic-icon primary">👥</div>
                    <div class="form-statistic-content">
                        <div class="form-statistic-title">团队成员</div>
                        <div class="form-statistic-value">156</div>
                        <div class="form-statistic-trend up">
                            <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7,14L12,9L17,14H7Z"/>
                            </svg>
                            <span>新增 8 人</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item form-statistic-with-icon">
                    <div class="form-statistic-icon success">💰</div>
                    <div class="form-statistic-content">
                        <div class="form-statistic-title">总收入</div>
                        <div class="form-statistic-value">
                            <span class="form-statistic-prefix">¥</span>
                            <span>128,456</span>
                        </div>
                        <div class="form-statistic-trend up">
                            <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7,14L12,9L17,14H7Z"/>
                            </svg>
                            <span>环比增长 15.3%</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item form-statistic-with-icon">
                    <div class="form-statistic-icon warning">📦</div>
                    <div class="form-statistic-content">
                        <div class="form-statistic-title">库存商品</div>
                        <div class="form-statistic-value">2,847</div>
                        <div class="form-statistic-trend down">
                            <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7,10L12,15L17,10H7Z"/>
                            </svg>
                            <span>库存不足 23 件</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item form-statistic-with-icon">
                    <div class="form-statistic-icon danger">⚠️</div>
                    <div class="form-statistic-content">
                        <div class="form-statistic-title">系统告警</div>
                        <div class="form-statistic-value">12</div>
                        <div class="form-statistic-trend up">
                            <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7,14L12,9L17,14H7Z"/>
                            </svg>
                            <span>需要处理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>带进度条的统计</h3>
            <div class="demo-grid demo-grid-2">
                <div class="form-statistic-item">
                    <div class="form-statistic-title">项目进度</div>
                    <div class="form-statistic-value">
                        <span>75</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-progress">
                        <div class="form-statistic-progress-bar">
                            <div class="form-statistic-progress-fill" style="width: 75%; background-color: #3b82f6;"></div>
                        </div>
                        <div class="form-statistic-progress-text">
                            <span>已完成</span>
                            <span>75/100</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item">
                    <div class="form-statistic-title">存储使用率</div>
                    <div class="form-statistic-value">
                        <span>68.5</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-progress">
                        <div class="form-statistic-progress-bar">
                            <div class="form-statistic-progress-fill" style="width: 68.5%; background-color: #f59e0b;"></div>
                        </div>
                        <div class="form-statistic-progress-text">
                            <span>已使用 6.85 GB</span>
                            <span>总计 10 GB</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item">
                    <div class="form-statistic-title">CPU 使用率</div>
                    <div class="form-statistic-value">
                        <span>45.2</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-progress">
                        <div class="form-statistic-progress-bar">
                            <div class="form-statistic-progress-fill" style="width: 45.2%; background-color: #10b981;"></div>
                        </div>
                        <div class="form-statistic-progress-text">
                            <span>正常</span>
                            <span>4 核心</span>
                        </div>
                    </div>
                </div>

                <div class="form-statistic-item">
                    <div class="form-statistic-title">内存使用率</div>
                    <div class="form-statistic-value">
                        <span>89.7</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-progress">
                        <div class="form-statistic-progress-bar">
                            <div class="form-statistic-progress-fill" style="width: 89.7%; background-color: #ef4444;"></div>
                        </div>
                        <div class="form-statistic-progress-text">
                            <span>已使用 14.35 GB</span>
                            <span>总计 16 GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>卡片样式</h3>
            <div class="demo-grid demo-grid-3">
                <div class="form-statistic-item form-statistic-card">
                    <div class="form-statistic-title">月度销售额</div>
                    <div class="form-statistic-value">
                        <span class="form-statistic-prefix">¥</span>
                        <span>456,789</span>
                    </div>
                    <div class="form-statistic-trend">
                        <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7,14L12,9L17,14H7Z"/>
                        </svg>
                        <span>较上月增长 28.5%</span>
                    </div>
                </div>

                <div class="form-statistic-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                    <div class="form-statistic-title" style="color: rgba(255, 255, 255, 0.8);">新用户注册</div>
                    <div class="form-statistic-value" style="color: white;">3,247</div>
                    <div class="form-statistic-trend" style="color: rgba(255, 255, 255, 0.9);">
                        <svg class="form-statistic-trend-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7,14L12,9L17,14H7Z"/>
                        </svg>
                        <span>本周新增 1,024 人</span>
                    </div>
                </div>

                <div class="form-statistic-item" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none;">
                    <div class="form-statistic-title" style="color: rgba(255, 255, 255, 0.8);">活跃度</div>
                    <div class="form-statistic-value" style="color: white;">
                        <span>92.3</span>
                        <span class="form-statistic-suffix">%</span>
                    </div>
                    <div class="form-statistic-trend" style="color: rgba(255, 255, 255, 0.9);">
                        <span>用户活跃度良好</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>动画演示</h3>
            <div class="demo-controls">
                <button class="demo-button" onclick="animateStatistics()">播放动画</button>
                <button class="demo-button" onclick="updateStatistics()">更新数据</button>
            </div>
            <div class="demo-grid demo-grid-3">
                <div class="form-statistic-item form-statistic-animated" id="animatedStat1">
                    <div class="form-statistic-title">实时在线</div>
                    <div class="form-statistic-value" id="onlineUsers">1,234</div>
                </div>

                <div class="form-statistic-item form-statistic-animated" id="animatedStat2">
                    <div class="form-statistic-title">今日订单</div>
                    <div class="form-statistic-value" id="todayOrders">567</div>
                </div>

                <div class="form-statistic-item form-statistic-animated" id="animatedStat3">
                    <div class="form-statistic-title">收入</div>
                    <div class="form-statistic-value" id="revenue">
                        <span class="form-statistic-prefix">¥</span>
                        <span id="revenueValue">89,012</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数字动画函数
        function animateNumber(element, start, end, duration = 2000) {
            const startTime = performance.now();
            const difference = end - start;

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const current = Math.floor(start + difference * easeOutQuart);
                
                element.textContent = current.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            requestAnimationFrame(updateNumber);
        }

        // 播放动画
        function animateStatistics() {
            const onlineUsers = document.getElementById('onlineUsers');
            const todayOrders = document.getElementById('todayOrders');
            const revenueValue = document.getElementById('revenueValue');

            // 重置动画类
            document.querySelectorAll('.form-statistic-animated').forEach(el => {
                el.classList.remove('form-statistic-animated');
                setTimeout(() => el.classList.add('form-statistic-animated'), 10);
            });

            // 数字动画
            animateNumber(onlineUsers, 0, 1234, 2000);
            animateNumber(todayOrders, 0, 567, 2000);
            animateNumber(revenueValue, 0, 89012, 2000);
        }

        // 更新统计数据
        function updateStatistics() {
            const onlineUsers = document.getElementById('onlineUsers');
            const todayOrders = document.getElementById('todayOrders');
            const revenueValue = document.getElementById('revenueValue');

            // 生成随机数据
            const newOnlineUsers = Math.floor(Math.random() * 2000) + 1000;
            const newTodayOrders = Math.floor(Math.random() * 800) + 400;
            const newRevenue = Math.floor(Math.random() * 50000) + 50000;

            // 获取当前值
            const currentOnlineUsers = parseInt(onlineUsers.textContent.replace(/,/g, ''));
            const currentTodayOrders = parseInt(todayOrders.textContent.replace(/,/g, ''));
            const currentRevenue = parseInt(revenueValue.textContent.replace(/,/g, ''));

            // 动画更新
            animateNumber(onlineUsers, currentOnlineUsers, newOnlineUsers, 1500);
            animateNumber(todayOrders, currentTodayOrders, newTodayOrders, 1500);
            animateNumber(revenueValue, currentRevenue, newRevenue, 1500);
        }

        // 实时更新示例（模拟）
        function startRealTimeUpdate() {
            setInterval(() => {
                const userCount = document.getElementById('userCount');
                const visitCount = document.getElementById('visitCount');
                
                if (userCount && visitCount) {
                    const currentUsers = parseInt(userCount.textContent.replace(/,/g, ''));
                    const currentVisits = parseInt(visitCount.textContent.replace(/,/g, ''));
                    
                    // 小幅度随机变化
                    const newUsers = currentUsers + Math.floor(Math.random() * 10) - 5;
                    const newVisits = currentVisits + Math.floor(Math.random() * 100) - 50;
                    
                    userCount.textContent = Math.max(0, newUsers).toLocaleString();
                    visitCount.textContent = Math.max(0, newVisits).toLocaleString();
                }
            }, 5000);
        }

        // 页面加载完成后启动实时更新
        document.addEventListener('DOMContentLoaded', () => {
            startRealTimeUpdate();
        });
    </script>
</body>
</html>
