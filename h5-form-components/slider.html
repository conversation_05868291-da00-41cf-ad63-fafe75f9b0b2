<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider 滑块组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 滑块基础样式 */
        .form-slider-container {
            margin-bottom: 24px;
        }

        .form-slider-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .form-slider-wrapper {
            position: relative;
            padding: 20px 0;
        }

        .form-slider-track {
            position: relative;
            height: 6px;
            background-color: #e5e7eb;
            border-radius: 3px;
            cursor: pointer;
        }

        .form-slider-fill {
            position: absolute;
            height: 100%;
            background-color: #3b82f6;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .form-slider-handle {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #3b82f6;
            border: 2px solid #fff;
            border-radius: 50%;
            cursor: grab;
            top: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .form-slider-handle:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .form-slider-handle:active {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .form-slider-handle.dragging {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.2);
        }

        /* 数值显示 */
        .form-slider-value {
            margin-top: 12px;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }

        .form-slider-value-current {
            font-weight: 600;
            color: #3b82f6;
        }

        /* 标记点 */
        .form-slider-marks {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            height: 20px;
        }

        .form-slider-mark {
            position: absolute;
            top: 8px;
            transform: translateX(-50%);
            font-size: 12px;
            color: #9ca3af;
            white-space: nowrap;
        }

        .form-slider-mark::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 6px;
            background-color: #d1d5db;
        }

        /* 范围滑块 */
        .form-slider-range .form-slider-fill {
            left: 0;
            right: auto;
        }

        .form-slider-range .form-slider-handle:first-of-type {
            z-index: 2;
        }

        .form-slider-range .form-slider-handle:last-of-type {
            z-index: 1;
        }

        /* 垂直滑块 */
        .form-slider-vertical {
            height: 200px;
            width: 40px;
            margin: 0 auto;
        }

        .form-slider-vertical .form-slider-track {
            width: 6px;
            height: 100%;
            margin: 0 auto;
        }

        .form-slider-vertical .form-slider-fill {
            width: 100%;
            height: auto;
            bottom: 0;
            top: auto;
        }

        .form-slider-vertical .form-slider-handle {
            left: 50%;
            top: auto;
            transform: translate(-50%, 50%);
        }

        .form-slider-vertical .form-slider-handle:hover {
            transform: translate(-50%, 50%) scale(1.1);
        }

        .form-slider-vertical .form-slider-handle:active,
        .form-slider-vertical .form-slider-handle.dragging {
            transform: translate(-50%, 50%) scale(1.2);
        }

        /* 不同尺寸 */
        .form-slider-small .form-slider-track {
            height: 4px;
        }

        .form-slider-small .form-slider-handle {
            width: 16px;
            height: 16px;
        }

        .form-slider-large .form-slider-track {
            height: 8px;
        }

        .form-slider-large .form-slider-handle {
            width: 24px;
            height: 24px;
        }

        /* 不同颜色主题 */
        .form-slider-success .form-slider-fill,
        .form-slider-success .form-slider-handle {
            background-color: #10b981;
        }

        .form-slider-warning .form-slider-fill,
        .form-slider-warning .form-slider-handle {
            background-color: #f59e0b;
        }

        .form-slider-danger .form-slider-fill,
        .form-slider-danger .form-slider-handle {
            background-color: #ef4444;
        }

        .form-slider-purple .form-slider-fill,
        .form-slider-purple .form-slider-handle {
            background-color: #8b5cf6;
        }

        /* 禁用状态 */
        .form-slider-disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        .form-slider-disabled .form-slider-track {
            background-color: #f3f4f6;
        }

        .form-slider-disabled .form-slider-fill {
            background-color: #d1d5db;
        }

        .form-slider-disabled .form-slider-handle {
            background-color: #d1d5db;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-slider-wrapper {
                padding: 16px 0;
            }
            
            .form-slider-handle {
                width: 24px;
                height: 24px;
            }
        }

        /* 演示样式 */
        .demo-result {
            margin-top: 16px;
            padding: 12px;
            background-color: #f8fafc;
            border-radius: 6px;
            font-size: 14px;
            color: #4b5563;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .demo-vertical-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            min-height: 250px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Slider 滑块组件</h1>
        <p class="demo-description">滑块选择组件，支持单值和范围选择，适用于数值范围输入</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="form-slider-container">
                <label class="form-slider-label">音量控制</label>
                <div class="form-slider-wrapper">
                    <div class="form-slider-track" onclick="handleTrackClick(event, 'basic')">
                        <div class="form-slider-fill" id="basic-fill"></div>
                        <div class="form-slider-handle" id="basic-handle" 
                             onmousedown="startDrag(event, 'basic')" 
                             ontouchstart="startDrag(event, 'basic')"></div>
                    </div>
                </div>
                <div class="form-slider-value">
                    当前值: <span class="form-slider-value-current" id="basic-value">50</span>
                </div>
            </div>
            <div class="demo-result" id="basic-result">滑块值：50</div>
        </div>

        <div class="demo-section">
            <h3>范围滑块</h3>
            <div class="form-slider-container">
                <label class="form-slider-label">价格区间</label>
                <div class="form-slider-wrapper">
                    <div class="form-slider-track form-slider-range" onclick="handleRangeTrackClick(event, 'range')">
                        <div class="form-slider-fill" id="range-fill"></div>
                        <div class="form-slider-handle" id="range-handle-min" 
                             onmousedown="startDrag(event, 'range', 'min')" 
                             ontouchstart="startDrag(event, 'range', 'min')"></div>
                        <div class="form-slider-handle" id="range-handle-max" 
                             onmousedown="startDrag(event, 'range', 'max')" 
                             ontouchstart="startDrag(event, 'range', 'max')"></div>
                    </div>
                </div>
                <div class="form-slider-value">
                    范围: <span class="form-slider-value-current" id="range-value">20 - 80</span>
                </div>
            </div>
            <div class="demo-result" id="range-result">价格区间：¥20 - ¥80</div>
        </div>

        <div class="demo-section">
            <h3>带标记点</h3>
            <div class="form-slider-container">
                <label class="form-slider-label">评分</label>
                <div class="form-slider-wrapper">
                    <div class="form-slider-track" onclick="handleTrackClick(event, 'marks')">
                        <div class="form-slider-fill" id="marks-fill"></div>
                        <div class="form-slider-handle" id="marks-handle" 
                             onmousedown="startDrag(event, 'marks')" 
                             ontouchstart="startDrag(event, 'marks')"></div>
                    </div>
                    <div class="form-slider-marks">
                        <div class="form-slider-mark" style="left: 0%;">1</div>
                        <div class="form-slider-mark" style="left: 25%;">2</div>
                        <div class="form-slider-mark" style="left: 50%;">3</div>
                        <div class="form-slider-mark" style="left: 75%;">4</div>
                        <div class="form-slider-mark" style="left: 100%;">5</div>
                    </div>
                </div>
                <div class="form-slider-value">
                    评分: <span class="form-slider-value-current" id="marks-value">3</span> 星
                </div>
            </div>
            <div class="demo-result" id="marks-result">评分：3 星</div>
        </div>

        <div class="demo-grid">
            <div>
                <h3>垂直滑块</h3>
                <div class="demo-vertical-container">
                    <div class="form-slider-container">
                        <label class="form-slider-label">温度</label>
                        <div class="form-slider-wrapper form-slider-vertical">
                            <div class="form-slider-track" onclick="handleVerticalTrackClick(event, 'vertical')">
                                <div class="form-slider-fill" id="vertical-fill"></div>
                                <div class="form-slider-handle" id="vertical-handle" 
                                     onmousedown="startDrag(event, 'vertical')" 
                                     ontouchstart="startDrag(event, 'vertical')"></div>
                            </div>
                        </div>
                        <div class="form-slider-value">
                            <span class="form-slider-value-current" id="vertical-value">25</span>°C
                        </div>
                    </div>
                </div>
                <div class="demo-result" id="vertical-result">温度：25°C</div>
            </div>

            <div>
                <h3>不同尺寸</h3>
                <div class="form-slider-container">
                    <label class="form-slider-label">小尺寸</label>
                    <div class="form-slider-wrapper form-slider-small">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'small')">
                            <div class="form-slider-fill" id="small-fill"></div>
                            <div class="form-slider-handle" id="small-handle" 
                                 onmousedown="startDrag(event, 'small')" 
                                 ontouchstart="startDrag(event, 'small')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="small-value">30</span>%
                    </div>
                </div>

                <div class="form-slider-container">
                    <label class="form-slider-label">大尺寸</label>
                    <div class="form-slider-wrapper form-slider-large">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'large')">
                            <div class="form-slider-fill" id="large-fill"></div>
                            <div class="form-slider-handle" id="large-handle" 
                                 onmousedown="startDrag(event, 'large')" 
                                 ontouchstart="startDrag(event, 'large')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="large-value">70</span>%
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>不同颜色主题</h3>
            <div class="demo-grid">
                <div class="form-slider-container">
                    <label class="form-slider-label">成功主题</label>
                    <div class="form-slider-wrapper form-slider-success">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'success')">
                            <div class="form-slider-fill" id="success-fill"></div>
                            <div class="form-slider-handle" id="success-handle" 
                                 onmousedown="startDrag(event, 'success')" 
                                 ontouchstart="startDrag(event, 'success')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="success-value">60</span>%
                    </div>
                </div>

                <div class="form-slider-container">
                    <label class="form-slider-label">警告主题</label>
                    <div class="form-slider-wrapper form-slider-warning">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'warning')">
                            <div class="form-slider-fill" id="warning-fill"></div>
                            <div class="form-slider-handle" id="warning-handle" 
                                 onmousedown="startDrag(event, 'warning')" 
                                 ontouchstart="startDrag(event, 'warning')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="warning-value">40</span>%
                    </div>
                </div>

                <div class="form-slider-container">
                    <label class="form-slider-label">危险主题</label>
                    <div class="form-slider-wrapper form-slider-danger">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'danger')">
                            <div class="form-slider-fill" id="danger-fill"></div>
                            <div class="form-slider-handle" id="danger-handle" 
                                 onmousedown="startDrag(event, 'danger')" 
                                 ontouchstart="startDrag(event, 'danger')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="danger-value">80</span>%
                    </div>
                </div>

                <div class="form-slider-container">
                    <label class="form-slider-label">紫色主题</label>
                    <div class="form-slider-wrapper form-slider-purple">
                        <div class="form-slider-track" onclick="handleTrackClick(event, 'purple')">
                            <div class="form-slider-fill" id="purple-fill"></div>
                            <div class="form-slider-handle" id="purple-handle" 
                                 onmousedown="startDrag(event, 'purple')" 
                                 ontouchstart="startDrag(event, 'purple')"></div>
                        </div>
                    </div>
                    <div class="form-slider-value">
                        <span class="form-slider-value-current" id="purple-value">90</span>%
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>禁用状态</h3>
            <div class="form-slider-container form-slider-disabled">
                <label class="form-slider-label">禁用滑块</label>
                <div class="form-slider-wrapper">
                    <div class="form-slider-track">
                        <div class="form-slider-fill" style="width: 45%;"></div>
                        <div class="form-slider-handle" style="left: 45%;"></div>
                    </div>
                </div>
                <div class="form-slider-value">
                    <span class="form-slider-value-current">45</span>%
                </div>
            </div>
        </div>
    </div>

    <script>
        // 滑块状态管理
        const sliderStates = {
            basic: { value: 50, min: 0, max: 100, step: 1 },
            range: { min: 20, max: 80, minValue: 0, maxValue: 100, step: 1 },
            marks: { value: 3, min: 1, max: 5, step: 1 },
            vertical: { value: 25, min: 0, max: 50, step: 1 },
            small: { value: 30, min: 0, max: 100, step: 1 },
            large: { value: 70, min: 0, max: 100, step: 1 },
            success: { value: 60, min: 0, max: 100, step: 1 },
            warning: { value: 40, min: 0, max: 100, step: 1 },
            danger: { value: 80, min: 0, max: 100, step: 1 },
            purple: { value: 90, min: 0, max: 100, step: 1 }
        };

        let isDragging = false;
        let currentSlider = null;
        let currentHandle = null;

        // 初始化所有滑块
        function initSliders() {
            Object.keys(sliderStates).forEach(id => {
                if (id === 'range') {
                    updateRangeSlider(id);
                } else if (id === 'vertical') {
                    updateVerticalSlider(id);
                } else {
                    updateSlider(id);
                }
            });
        }

        // 更新单值滑块
        function updateSlider(id) {
            const state = sliderStates[id];
            const percentage = ((state.value - state.min) / (state.max - state.min)) * 100;
            
            const fill = document.getElementById(`${id}-fill`);
            const handle = document.getElementById(`${id}-handle`);
            const valueDisplay = document.getElementById(`${id}-value`);
            
            if (fill) fill.style.width = `${percentage}%`;
            if (handle) handle.style.left = `${percentage}%`;
            if (valueDisplay) valueDisplay.textContent = state.value;
            
            updateResult(id);
        }

        // 更新范围滑块
        function updateRangeSlider(id) {
            const state = sliderStates[id];
            const minPercentage = ((state.min - state.minValue) / (state.maxValue - state.minValue)) * 100;
            const maxPercentage = ((state.max - state.minValue) / (state.maxValue - state.minValue)) * 100;
            
            const fill = document.getElementById(`${id}-fill`);
            const minHandle = document.getElementById(`${id}-handle-min`);
            const maxHandle = document.getElementById(`${id}-handle-max`);
            const valueDisplay = document.getElementById(`${id}-value`);
            
            if (fill) {
                fill.style.left = `${minPercentage}%`;
                fill.style.width = `${maxPercentage - minPercentage}%`;
            }
            if (minHandle) minHandle.style.left = `${minPercentage}%`;
            if (maxHandle) maxHandle.style.left = `${maxPercentage}%`;
            if (valueDisplay) valueDisplay.textContent = `${state.min} - ${state.max}`;
            
            updateResult(id);
        }

        // 更新垂直滑块
        function updateVerticalSlider(id) {
            const state = sliderStates[id];
            const percentage = ((state.value - state.min) / (state.max - state.min)) * 100;
            
            const fill = document.getElementById(`${id}-fill`);
            const handle = document.getElementById(`${id}-handle`);
            const valueDisplay = document.getElementById(`${id}-value`);
            
            if (fill) fill.style.height = `${percentage}%`;
            if (handle) handle.style.bottom = `${percentage}%`;
            if (valueDisplay) valueDisplay.textContent = state.value;
            
            updateResult(id);
        }

        // 开始拖拽
        function startDrag(event, sliderId, handleType = 'single') {
            event.preventDefault();
            isDragging = true;
            currentSlider = sliderId;
            currentHandle = handleType;
            
            const handle = event.target;
            handle.classList.add('dragging');
            
            // 添加全局事件监听
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
            document.addEventListener('touchmove', handleDrag);
            document.addEventListener('touchend', stopDrag);
        }

        // 处理拖拽
        function handleDrag(event) {
            if (!isDragging) return;
            
            event.preventDefault();
            const clientX = event.clientX || (event.touches && event.touches[0].clientX);
            const clientY = event.clientY || (event.touches && event.touches[0].clientY);
            
            if (currentSlider === 'vertical') {
                handleVerticalDrag(clientY);
            } else {
                handleHorizontalDrag(clientX);
            }
        }

        // 处理水平拖拽
        function handleHorizontalDrag(clientX) {
            const track = document.querySelector(`#${currentSlider === 'range' ? 'range' : currentSlider}-fill`).parentElement;
            const rect = track.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
            
            const state = sliderStates[currentSlider];
            
            if (currentSlider === 'range') {
                const value = state.minValue + (percentage / 100) * (state.maxValue - state.minValue);
                const roundedValue = Math.round(value / state.step) * state.step;
                
                if (currentHandle === 'min') {
                    state.min = Math.min(roundedValue, state.max - state.step);
                } else {
                    state.max = Math.max(roundedValue, state.min + state.step);
                }
                updateRangeSlider(currentSlider);
            } else {
                const value = state.min + (percentage / 100) * (state.max - state.min);
                state.value = Math.round(value / state.step) * state.step;
                
                if (currentSlider === 'vertical') {
                    updateVerticalSlider(currentSlider);
                } else {
                    updateSlider(currentSlider);
                }
            }
        }

        // 处理垂直拖拽
        function handleVerticalDrag(clientY) {
            const track = document.querySelector(`#${currentSlider}-fill`).parentElement;
            const rect = track.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((rect.bottom - clientY) / rect.height) * 100));
            
            const state = sliderStates[currentSlider];
            const value = state.min + (percentage / 100) * (state.max - state.min);
            state.value = Math.round(value / state.step) * state.step;
            
            updateVerticalSlider(currentSlider);
        }

        // 停止拖拽
        function stopDrag() {
            if (!isDragging) return;
            
            isDragging = false;
            
            // 移除拖拽样式
            document.querySelectorAll('.form-slider-handle.dragging').forEach(handle => {
                handle.classList.remove('dragging');
            });
            
            // 移除全局事件监听
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('touchmove', handleDrag);
            document.removeEventListener('touchend', stopDrag);
            
            currentSlider = null;
            currentHandle = null;
        }

        // 点击轨道
        function handleTrackClick(event, sliderId) {
            if (isDragging) return;
            
            const track = event.currentTarget;
            const rect = track.getBoundingClientRect();
            const percentage = ((event.clientX - rect.left) / rect.width) * 100;
            
            const state = sliderStates[sliderId];
            const value = state.min + (percentage / 100) * (state.max - state.min);
            state.value = Math.round(value / state.step) * state.step;
            
            updateSlider(sliderId);
        }

        // 点击范围轨道
        function handleRangeTrackClick(event, sliderId) {
            if (isDragging) return;
            
            const track = event.currentTarget;
            const rect = track.getBoundingClientRect();
            const percentage = ((event.clientX - rect.left) / rect.width) * 100;
            
            const state = sliderStates[sliderId];
            const value = state.minValue + (percentage / 100) * (state.maxValue - state.minValue);
            const roundedValue = Math.round(value / state.step) * state.step;
            
            // 判断更接近哪个手柄
            const minDistance = Math.abs(roundedValue - state.min);
            const maxDistance = Math.abs(roundedValue - state.max);
            
            if (minDistance < maxDistance) {
                state.min = Math.min(roundedValue, state.max - state.step);
            } else {
                state.max = Math.max(roundedValue, state.min + state.step);
            }
            
            updateRangeSlider(sliderId);
        }

        // 点击垂直轨道
        function handleVerticalTrackClick(event, sliderId) {
            if (isDragging) return;
            
            const track = event.currentTarget;
            const rect = track.getBoundingClientRect();
            const percentage = ((rect.bottom - event.clientY) / rect.height) * 100;
            
            const state = sliderStates[sliderId];
            const value = state.min + (percentage / 100) * (state.max - state.min);
            state.value = Math.round(value / state.step) * state.step;
            
            updateVerticalSlider(sliderId);
        }

        // 更新结果显示
        function updateResult(sliderId) {
            const resultElement = document.getElementById(`${sliderId}-result`);
            if (!resultElement) return;
            
            const state = sliderStates[sliderId];
            let resultText = '';
            
            switch (sliderId) {
                case 'basic':
                    resultText = `滑块值：${state.value}`;
                    break;
                case 'range':
                    resultText = `价格区间：¥${state.min} - ¥${state.max}`;
                    break;
                case 'marks':
                    resultText = `评分：${state.value} 星`;
                    break;
                case 'vertical':
                    resultText = `温度：${state.value}°C`;
                    break;
                default:
                    resultText = `${sliderId}：${state.value}${sliderId.includes('temperature') ? '°C' : '%'}`;
            }
            
            resultElement.textContent = resultText;
        }

        // 初始化
        initSliders();
    </script>
</body>
</html>
