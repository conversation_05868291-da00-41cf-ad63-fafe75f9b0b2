<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tree 树形控件组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 树形控件基础样式 */
        .form-tree-container {
            margin-bottom: 24px;
        }

        .form-tree-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .form-tree-wrapper {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #fff;
            max-height: 400px;
            overflow-y: auto;
        }

        .form-tree-search {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            background-color: #f9fafb;
        }

        .form-tree-search input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-tree-search input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-tree-list {
            padding: 8px 0;
        }

        .form-tree-node {
            position: relative;
        }

        .form-tree-node-content {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            user-select: none;
        }

        .form-tree-node-content:hover {
            background-color: #f8fafc;
        }

        .form-tree-node-content.selected {
            background-color: #eff6ff;
            color: #3b82f6;
        }

        .form-tree-node-content.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .form-tree-node-indent {
            width: 20px;
            flex-shrink: 0;
        }

        .form-tree-node-expand {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-right: 4px;
            transition: transform 0.2s ease;
        }

        .form-tree-node-expand.expanded {
            transform: rotate(90deg);
        }

        .form-tree-node-expand-icon {
            width: 12px;
            height: 12px;
        }

        .form-tree-node-checkbox {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .form-tree-node-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            opacity: 0.7;
        }

        .form-tree-node-text {
            flex: 1;
            font-size: 14px;
        }

        .form-tree-node-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .form-tree-node-content:hover .form-tree-node-actions {
            opacity: 1;
        }

        .form-tree-node-action {
            width: 20px;
            height: 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .form-tree-node-action:hover {
            background-color: #e5e7eb;
        }

        .form-tree-node-action.delete:hover {
            background-color: #fef2f2;
            color: #ef4444;
        }

        .form-tree-children {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .form-tree-children.collapsed {
            max-height: 0;
        }

        .form-tree-children.expanded {
            max-height: 1000px;
        }

        /* 连接线样式 */
        .form-tree-with-lines .form-tree-node {
            position: relative;
        }

        .form-tree-with-lines .form-tree-node::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 50%;
            width: 1px;
            background-color: #d1d5db;
        }

        .form-tree-with-lines .form-tree-node::after {
            content: '';
            position: absolute;
            left: 20px;
            top: 50%;
            width: 16px;
            height: 1px;
            background-color: #d1d5db;
        }

        .form-tree-with-lines .form-tree-node:last-child::before {
            bottom: 50%;
        }

        /* 空状态 */
        .form-tree-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            color: #9ca3af;
            font-size: 14px;
        }

        .form-tree-empty-icon {
            width: 48px;
            height: 48px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        /* 加载状态 */
        .form-tree-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
        }

        .form-tree-loading-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-tree-wrapper {
                max-height: 300px;
            }
            
            .form-tree-node-content {
                padding: 8px 10px;
            }
            
            .form-tree-node-text {
                font-size: 13px;
            }
        }

        /* 演示样式 */
        .demo-result {
            margin-top: 16px;
            padding: 12px;
            background-color: #f8fafc;
            border-radius: 6px;
            font-size: 14px;
            color: #4b5563;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        /* 不同样式变体 */
        .form-tree-compact .form-tree-wrapper {
            max-height: 250px;
        }

        .form-tree-compact .form-tree-node-content {
            padding: 4px 8px;
        }

        .form-tree-large .form-tree-wrapper {
            max-height: 500px;
        }

        .form-tree-large .form-tree-node-content {
            padding: 10px 16px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Tree 树形控件</h1>
        <p class="demo-description">树形结构展示组件，支持展开折叠、选择、搜索等功能</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="form-tree-container">
                <label class="form-tree-label">组织架构</label>
                <div class="form-tree-wrapper">
                    <div class="form-tree-search">
                        <input type="text" placeholder="搜索组织..." oninput="searchTree('basic', this.value)">
                    </div>
                    <div class="form-tree-list" id="basicTree"></div>
                </div>
            </div>
            <div class="demo-result" id="basicResult">选中节点：无</div>
        </div>

        <div class="demo-section">
            <h3>可选择树形控件</h3>
            <div class="form-tree-container">
                <label class="form-tree-label">权限分配</label>
                <div class="form-tree-wrapper">
                    <div class="form-tree-search">
                        <input type="text" placeholder="搜索权限..." oninput="searchTree('checkbox', this.value)">
                    </div>
                    <div class="form-tree-list" id="checkboxTree"></div>
                </div>
            </div>
            <div class="demo-result" id="checkboxResult">已选择：0 项</div>
        </div>

        <div class="demo-grid">
            <div>
                <h3>带连接线</h3>
                <div class="form-tree-container">
                    <label class="form-tree-label">文件目录</label>
                    <div class="form-tree-wrapper form-tree-with-lines">
                        <div class="form-tree-list" id="linesTree"></div>
                    </div>
                </div>
                <div class="demo-result" id="linesResult">选中文件：无</div>
            </div>

            <div>
                <h3>紧凑模式</h3>
                <div class="form-tree-container form-tree-compact">
                    <label class="form-tree-label">菜单结构</label>
                    <div class="form-tree-wrapper">
                        <div class="form-tree-list" id="compactTree"></div>
                    </div>
                </div>
                <div class="demo-result" id="compactResult">选中菜单：无</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>异步加载</h3>
            <div class="form-tree-container">
                <label class="form-tree-label">动态加载节点</label>
                <div class="form-tree-wrapper">
                    <div class="form-tree-list" id="asyncTree"></div>
                </div>
            </div>
            <div class="demo-result" id="asyncResult">选中节点：无</div>
        </div>
    </div>

    <script>
        // 树形数据
        const treeData = {
            basic: [
                {
                    id: 1,
                    text: '总公司',
                    icon: '🏢',
                    expanded: true,
                    children: [
                        {
                            id: 2,
                            text: '技术部',
                            icon: '💻',
                            children: [
                                { id: 3, text: '前端组', icon: '🎨' },
                                { id: 4, text: '后端组', icon: '⚙️' },
                                { id: 5, text: '测试组', icon: '🔍' }
                            ]
                        },
                        {
                            id: 6,
                            text: '市场部',
                            icon: '📈',
                            children: [
                                { id: 7, text: '销售组', icon: '💼' },
                                { id: 8, text: '推广组', icon: '📢' }
                            ]
                        }
                    ]
                }
            ],
            checkbox: [
                {
                    id: 1,
                    text: '系统管理',
                    icon: '⚙️',
                    expanded: true,
                    children: [
                        { id: 2, text: '用户管理', icon: '👤' },
                        { id: 3, text: '角色管理', icon: '👥' },
                        {
                            id: 4,
                            text: '权限管理',
                            icon: '🔐',
                            children: [
                                { id: 5, text: '菜单权限', icon: '📋' },
                                { id: 6, text: '操作权限', icon: '🔧' },
                                { id: 7, text: '数据权限', icon: '📊' }
                            ]
                        }
                    ]
                },
                {
                    id: 8,
                    text: '内容管理',
                    icon: '📝',
                    children: [
                        { id: 9, text: '文章管理', icon: '📄' },
                        { id: 10, text: '分类管理', icon: '📂' }
                    ]
                }
            ],
            lines: [
                {
                    id: 1,
                    text: 'src',
                    icon: '📁',
                    expanded: true,
                    children: [
                        {
                            id: 2,
                            text: 'components',
                            icon: '📁',
                            children: [
                                { id: 3, text: 'Button.vue', icon: '📄' },
                                { id: 4, text: 'Input.vue', icon: '📄' }
                            ]
                        },
                        {
                            id: 5,
                            text: 'utils',
                            icon: '📁',
                            children: [
                                { id: 6, text: 'helpers.js', icon: '📄' },
                                { id: 7, text: 'api.js', icon: '📄' }
                            ]
                        },
                        { id: 8, text: 'main.js', icon: '📄' }
                    ]
                }
            ],
            compact: [
                {
                    id: 1,
                    text: '首页',
                    icon: '🏠',
                    children: [
                        { id: 2, text: '数据概览', icon: '📊' },
                        { id: 3, text: '快捷操作', icon: '⚡' }
                    ]
                },
                {
                    id: 4,
                    text: '用户中心',
                    icon: '👤',
                    children: [
                        { id: 5, text: '个人信息', icon: '📝' },
                        { id: 6, text: '安全设置', icon: '🔒' }
                    ]
                }
            ],
            async: [
                { id: 1, text: '根节点 1', icon: '📁', hasChildren: true },
                { id: 2, text: '根节点 2', icon: '📁', hasChildren: true },
                { id: 3, text: '叶子节点', icon: '📄', hasChildren: false }
            ]
        };

        // 选中状态
        const treeStates = {
            basic: { selected: null, expanded: new Set([1]) },
            checkbox: { selected: new Set(), expanded: new Set([1]) },
            lines: { selected: null, expanded: new Set([1, 2]) },
            compact: { selected: null, expanded: new Set() },
            async: { selected: null, expanded: new Set(), loaded: new Set() }
        };

        // 初始化树形控件
        function initTrees() {
            Object.keys(treeData).forEach(type => {
                renderTree(type);
            });
        }

        // 渲染树形结构
        function renderTree(type, searchKeyword = '') {
            const container = document.getElementById(`${type}Tree`);
            if (!container) return;

            const data = treeData[type];
            const filteredData = searchKeyword ? filterTreeData(data, searchKeyword) : data;

            container.innerHTML = '';

            if (filteredData.length === 0) {
                const empty = document.createElement('div');
                empty.className = 'form-tree-empty';
                empty.innerHTML = `
                    <svg class="form-tree-empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    <span>${searchKeyword ? '无匹配结果' : '暂无数据'}</span>
                `;
                container.appendChild(empty);
                return;
            }

            filteredData.forEach(node => {
                renderNode(container, node, type, 0);
            });
        }

        // 渲染节点
        function renderNode(container, node, type, level) {
            const nodeElement = document.createElement('div');
            nodeElement.className = 'form-tree-node';
            nodeElement.dataset.nodeId = node.id;

            const hasChildren = node.children && node.children.length > 0;
            const isExpanded = treeStates[type].expanded.has(node.id);
            const isSelected = type === 'checkbox' ? 
                treeStates[type].selected.has(node.id) : 
                treeStates[type].selected === node.id;

            // 缩进
            const indent = Array(level).fill('<div class="form-tree-node-indent"></div>').join('');

            // 展开/折叠按钮
            const expandButton = hasChildren || node.hasChildren ? `
                <div class="form-tree-node-expand ${isExpanded ? 'expanded' : ''}" 
                     onclick="toggleExpand('${type}', ${node.id})">
                    <svg class="form-tree-node-expand-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                </div>
            ` : '<div class="form-tree-node-expand"></div>';

            // 复选框
            const checkbox = type === 'checkbox' ? `
                <input type="checkbox" class="form-tree-node-checkbox" 
                       ${isSelected ? 'checked' : ''} 
                       onchange="toggleSelection('${type}', ${node.id})">
            ` : '';

            // 节点内容
            nodeElement.innerHTML = `
                <div class="form-tree-node-content ${isSelected && type !== 'checkbox' ? 'selected' : ''}" 
                     onclick="selectNode('${type}', ${node.id})">
                    ${indent}
                    ${expandButton}
                    ${checkbox}
                    <span class="form-tree-node-icon">${node.icon || '📄'}</span>
                    <span class="form-tree-node-text">${node.text}</span>
                    <div class="form-tree-node-actions">
                        <button class="form-tree-node-action" onclick="editNode('${type}', ${node.id})" title="编辑">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                            </svg>
                        </button>
                        <button class="form-tree-node-action delete" onclick="deleteNode('${type}', ${node.id})" title="删除">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(nodeElement);

            // 渲染子节点
            if (hasChildren && isExpanded) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'form-tree-children expanded';
                
                node.children.forEach(child => {
                    renderNode(childrenContainer, child, type, level + 1);
                });
                
                nodeElement.appendChild(childrenContainer);
            } else if (node.hasChildren && isExpanded && type === 'async') {
                // 异步加载
                loadAsyncChildren(nodeElement, node, type, level);
            }
        }

        // 切换展开/折叠
        function toggleExpand(type, nodeId) {
            const expanded = treeStates[type].expanded;
            
            if (expanded.has(nodeId)) {
                expanded.delete(nodeId);
            } else {
                expanded.add(nodeId);
            }

            renderTree(type);
        }

        // 选择节点
        function selectNode(type, nodeId) {
            if (type === 'checkbox') return; // 复选框模式不支持单选

            treeStates[type].selected = nodeId;
            renderTree(type);
            updateResult(type);
        }

        // 切换复选框选择
        function toggleSelection(type, nodeId) {
            if (type !== 'checkbox') return;

            const selected = treeStates[type].selected;
            
            if (selected.has(nodeId)) {
                selected.delete(nodeId);
            } else {
                selected.add(nodeId);
            }

            updateResult(type);
        }

        // 搜索树形数据
        function searchTree(type, keyword) {
            renderTree(type, keyword);
        }

        // 过滤树形数据
        function filterTreeData(data, keyword) {
            if (!keyword) return data;

            const filtered = [];
            
            data.forEach(node => {
                if (node.text.toLowerCase().includes(keyword.toLowerCase())) {
                    filtered.push(node);
                } else if (node.children) {
                    const filteredChildren = filterTreeData(node.children, keyword);
                    if (filteredChildren.length > 0) {
                        filtered.push({
                            ...node,
                            children: filteredChildren,
                            expanded: true
                        });
                    }
                }
            });

            return filtered;
        }

        // 异步加载子节点
        function loadAsyncChildren(nodeElement, node, type, level) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'form-tree-children expanded';
            
            // 显示加载状态
            childrenContainer.innerHTML = `
                <div class="form-tree-loading">
                    <svg class="form-tree-loading-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
                    </svg>
                    <span>加载中...</span>
                </div>
            `;
            
            nodeElement.appendChild(childrenContainer);

            // 模拟异步加载
            setTimeout(() => {
                const children = [
                    { id: `${node.id}-1`, text: `子节点 ${node.id}-1`, icon: '📄' },
                    { id: `${node.id}-2`, text: `子节点 ${node.id}-2`, icon: '📄' },
                    { id: `${node.id}-3`, text: `子节点 ${node.id}-3`, icon: '📁', hasChildren: true }
                ];

                childrenContainer.innerHTML = '';
                children.forEach(child => {
                    renderNode(childrenContainer, child, type, level + 1);
                });

                treeStates[type].loaded.add(node.id);
            }, 1000);
        }

        // 编辑节点
        function editNode(type, nodeId) {
            const newText = prompt('请输入新的节点名称:');
            if (newText) {
                // 这里可以实现编辑逻辑
                alert(`编辑节点 ${nodeId}: ${newText}`);
            }
        }

        // 删除节点
        function deleteNode(type, nodeId) {
            if (confirm('确定要删除这个节点吗？')) {
                // 这里可以实现删除逻辑
                alert(`删除节点 ${nodeId}`);
            }
        }

        // 更新结果显示
        function updateResult(type) {
            const resultElement = document.getElementById(`${type}Result`);
            if (!resultElement) return;

            let resultText = '';

            switch (type) {
                case 'basic':
                    const selectedNode = findNodeById(treeData[type], treeStates[type].selected);
                    resultText = selectedNode ? `选中节点：${selectedNode.text}` : '选中节点：无';
                    break;
                case 'checkbox':
                    const selectedCount = treeStates[type].selected.size;
                    const selectedNodes = Array.from(treeStates[type].selected).map(id => {
                        const node = findNodeById(treeData[type], id);
                        return node ? node.text : '';
                    }).filter(Boolean);
                    resultText = `已选择：${selectedCount} 项`;
                    if (selectedNodes.length > 0) {
                        resultText += ` (${selectedNodes.join(', ')})`;
                    }
                    break;
                case 'lines':
                    const selectedFile = findNodeById(treeData[type], treeStates[type].selected);
                    resultText = selectedFile ? `选中文件：${selectedFile.text}` : '选中文件：无';
                    break;
                case 'compact':
                    const selectedMenu = findNodeById(treeData[type], treeStates[type].selected);
                    resultText = selectedMenu ? `选中菜单：${selectedMenu.text}` : '选中菜单：无';
                    break;
                case 'async':
                    const selectedAsync = findNodeById(treeData[type], treeStates[type].selected);
                    resultText = selectedAsync ? `选中节点：${selectedAsync.text}` : '选中节点：无';
                    break;
            }

            resultElement.textContent = resultText;
        }

        // 根据ID查找节点
        function findNodeById(data, id) {
            for (const node of data) {
                if (node.id === id) {
                    return node;
                }
                if (node.children) {
                    const found = findNodeById(node.children, id);
                    if (found) return found;
                }
            }
            return null;
        }

        // 初始化
        initTrees();
    </script>
</body>
</html>
