<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组字段组件</title>
</head>
<body>
    <!-- 分组字段组件 -->
    <div class="form-fieldset">
        <style>
            .form-fieldset {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-fieldset-container {
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 24px;
                background-color: #ffffff;
                transition: border-color 0.2s ease;
            }

            .form-fieldset-container:hover {
                border-color: #d1d5db;
            }

            .form-fieldset-container.focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
            }

            .form-fieldset-legend {
                font-size: 18px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #f3f4f6;
                position: relative;
            }

            .form-fieldset-legend::before {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 40px;
                height: 2px;
                background-color: #3b82f6;
            }

            .form-fieldset-description {
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 20px;
                line-height: 1.5;
            }

            .form-fieldset-content {
                display: grid;
                gap: 16px;
            }

            .form-fieldset-row {
                display: grid;
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .form-fieldset-row.two-columns {
                grid-template-columns: 1fr 1fr;
            }

            .form-fieldset-row.three-columns {
                grid-template-columns: 1fr 1fr 1fr;
            }

            /* 不同样式主题 */
            .form-fieldset-primary .form-fieldset-container {
                border-color: #3b82f6;
                background-color: #f8faff;
            }

            .form-fieldset-primary .form-fieldset-legend::before {
                background-color: #3b82f6;
            }

            .form-fieldset-success .form-fieldset-container {
                border-color: #10b981;
                background-color: #f0fdfa;
            }

            .form-fieldset-success .form-fieldset-legend::before {
                background-color: #10b981;
            }

            .form-fieldset-warning .form-fieldset-container {
                border-color: #f59e0b;
                background-color: #fffbeb;
            }

            .form-fieldset-warning .form-fieldset-legend::before {
                background-color: #f59e0b;
            }

            .form-fieldset-error .form-fieldset-container {
                border-color: #ef4444;
                background-color: #fef2f2;
            }

            .form-fieldset-error .form-fieldset-legend::before {
                background-color: #ef4444;
            }

            /* 可折叠样式 */
            .form-fieldset-collapsible .form-fieldset-legend {
                cursor: pointer;
                user-select: none;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .form-fieldset-toggle {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background-color: #f3f4f6;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            }

            .form-fieldset-toggle::after {
                content: '−';
                font-size: 16px;
                font-weight: bold;
                color: #6b7280;
                transition: transform 0.2s ease;
            }

            .form-fieldset-collapsible.collapsed .form-fieldset-toggle::after {
                content: '+';
                transform: rotate(0deg);
            }

            .form-fieldset-collapsible.collapsed .form-fieldset-content {
                display: none;
            }

            .form-fieldset-collapsible:hover .form-fieldset-toggle {
                background-color: #e5e7eb;
            }

            /* 紧凑样式 */
            .form-fieldset-compact .form-fieldset-container {
                padding: 16px;
            }

            .form-fieldset-compact .form-fieldset-legend {
                font-size: 16px;
                margin-bottom: 12px;
            }

            .form-fieldset-compact .form-fieldset-content {
                gap: 12px;
            }

            .form-fieldset-compact .form-fieldset-row {
                gap: 12px;
            }

            /* 卡片样式 */
            .form-fieldset-card .form-fieldset-container {
                border: none;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                border-radius: 8px;
            }

            .form-fieldset-card .form-fieldset-container:hover {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }

            /* 内嵌表单元素样式 */
            .form-fieldset .form-input-group,
            .form-fieldset .form-select-group,
            .form-fieldset .form-textarea-group,
            .form-fieldset .form-checkbox,
            .form-fieldset .form-radio {
                margin-bottom: 0;
            }

            .form-fieldset .form-input-label,
            .form-fieldset .form-select-label,
            .form-fieldset .form-textarea-label,
            .form-fieldset .form-checkbox-title,
            .form-fieldset .form-radio-title {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-fieldset .form-input,
            .form-fieldset .form-select-input,
            .form-fieldset .form-textarea-input {
                font-size: 14px;
                padding: 10px 12px;
                min-height: 40px;
            }

            @media (max-width: 480px) {
                .form-fieldset {
                    margin: 12px 0;
                }

                .form-fieldset-container {
                    padding: 16px;
                    border-radius: 8px;
                }

                .form-fieldset-legend {
                    font-size: 16px;
                    margin-bottom: 12px;
                }

                .form-fieldset-row.two-columns,
                .form-fieldset-row.three-columns {
                    grid-template-columns: 1fr;
                    gap: 12px;
                }

                .form-fieldset-content {
                    gap: 12px;
                }
            }
        </style>

        <!-- 基础分组字段 -->
        <div class="form-fieldset-container">
            <legend class="form-fieldset-legend">个人信息</legend>
            <div class="form-fieldset-description">
                请填写您的基本个人信息，所有标记为必填的字段都需要完成。
            </div>
            <div class="form-fieldset-content">
                <div class="form-fieldset-row two-columns">
                    <div class="form-input-group">
                        <label class="form-input-label" for="first-name">
                            姓 <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" id="first-name" name="first-name" class="form-input" placeholder="请输入姓氏" required>
                    </div>
                    <div class="form-input-group">
                        <label class="form-input-label" for="last-name">
                            名 <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" id="last-name" name="last-name" class="form-input" placeholder="请输入名字" required>
                    </div>
                </div>
                <div class="form-fieldset-row">
                    <div class="form-input-group">
                        <label class="form-input-label" for="email-personal">
                            邮箱地址 <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="email" id="email-personal" name="email" class="form-input" placeholder="请输入邮箱地址" required>
                    </div>
                </div>
                <div class="form-fieldset-row two-columns">
                    <div class="form-input-group">
                        <label class="form-input-label" for="phone-personal">
                            手机号码
                        </label>
                        <input type="tel" id="phone-personal" name="phone" class="form-input" placeholder="请输入手机号码">
                    </div>
                    <div class="form-input-group">
                        <label class="form-input-label" for="birth-date-personal">
                            出生日期
                        </label>
                        <input type="date" id="birth-date-personal" name="birth-date" class="form-input">
                    </div>
                </div>
            </div>
        </div>

        <!-- 主题色分组字段 -->
        <div class="form-fieldset-primary">
            <div class="form-fieldset-container">
                <legend class="form-fieldset-legend">联系信息</legend>
                <div class="form-fieldset-content">
                    <div class="form-fieldset-row">
                        <div class="form-textarea-group">
                            <label class="form-textarea-label" for="address">
                                详细地址
                            </label>
                            <textarea id="address" name="address" class="form-textarea-input" rows="3" placeholder="请输入详细地址"></textarea>
                        </div>
                    </div>
                    <div class="form-fieldset-row three-columns">
                        <div class="form-select-group">
                            <label class="form-select-label" for="country">
                                国家/地区
                            </label>
                            <select id="country" name="country" class="form-select-input">
                                <option value="">请选择</option>
                                <option value="cn" selected>中国</option>
                                <option value="us">美国</option>
                                <option value="jp">日本</option>
                            </select>
                        </div>
                        <div class="form-select-group">
                            <label class="form-select-label" for="province">
                                省/州
                            </label>
                            <select id="province" name="province" class="form-select-input">
                                <option value="">请选择</option>
                                <option value="beijing">北京</option>
                                <option value="shanghai">上海</option>
                                <option value="guangdong">广东</option>
                            </select>
                        </div>
                        <div class="form-input-group">
                            <label class="form-input-label" for="postal-code">
                                邮政编码
                            </label>
                            <input type="text" id="postal-code" name="postal-code" class="form-input" placeholder="邮编">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 可折叠分组字段 -->
        <div class="form-fieldset-collapsible" onclick="toggleFieldset(this)">
            <div class="form-fieldset-container">
                <legend class="form-fieldset-legend">
                    高级设置
                    <div class="form-fieldset-toggle"></div>
                </legend>
                <div class="form-fieldset-description">
                    可选的高级配置选项，如不需要可以跳过此部分。
                </div>
                <div class="form-fieldset-content">
                    <div class="form-fieldset-row">
                        <div class="form-checkbox">
                            <div class="form-checkbox-title">通知设置</div>
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="notifications" value="email">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">邮件通知</span>
                            </label>
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="notifications" value="sms">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">短信通知</span>
                            </label>
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="notifications" value="push">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">推送通知</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-fieldset-row">
                        <div class="form-radio">
                            <div class="form-radio-title">隐私级别</div>
                            <label class="form-radio-item">
                                <input type="radio" name="privacy" value="public">
                                <span class="form-radio-custom"></span>
                                <span class="form-radio-label">公开</span>
                            </label>
                            <label class="form-radio-item">
                                <input type="radio" name="privacy" value="friends" checked>
                                <span class="form-radio-custom"></span>
                                <span class="form-radio-label">仅好友</span>
                            </label>
                            <label class="form-radio-item">
                                <input type="radio" name="privacy" value="private">
                                <span class="form-radio-custom"></span>
                                <span class="form-radio-label">私密</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 紧凑样式分组字段 -->
        <div class="form-fieldset-compact form-fieldset-success">
            <div class="form-fieldset-container">
                <legend class="form-fieldset-legend">偏好设置</legend>
                <div class="form-fieldset-content">
                    <div class="form-fieldset-row two-columns">
                        <div class="form-select-group">
                            <label class="form-select-label" for="language">
                                语言
                            </label>
                            <select id="language" name="language" class="form-select-input">
                                <option value="zh-CN" selected>简体中文</option>
                                <option value="zh-TW">繁體中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </div>
                        <div class="form-select-group">
                            <label class="form-select-label" for="timezone">
                                时区
                            </label>
                            <select id="timezone" name="timezone" class="form-select-input">
                                <option value="Asia/Shanghai" selected>北京时间 (UTC+8)</option>
                                <option value="America/New_York">纽约时间 (UTC-5)</option>
                                <option value="Europe/London">伦敦时间 (UTC+0)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 卡片样式分组字段 -->
        <div class="form-fieldset-card form-fieldset-warning">
            <div class="form-fieldset-container">
                <legend class="form-fieldset-legend">紧急联系人</legend>
                <div class="form-fieldset-description">
                    请提供一位紧急联系人的信息，以备不时之需。
                </div>
                <div class="form-fieldset-content">
                    <div class="form-fieldset-row two-columns">
                        <div class="form-input-group">
                            <label class="form-input-label" for="emergency-name">
                                联系人姓名 <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="text" id="emergency-name" name="emergency-name" class="form-input" placeholder="请输入联系人姓名" required>
                        </div>
                        <div class="form-select-group">
                            <label class="form-select-label" for="emergency-relation">
                                关系
                            </label>
                            <select id="emergency-relation" name="emergency-relation" class="form-select-input">
                                <option value="">请选择关系</option>
                                <option value="parent">父母</option>
                                <option value="spouse">配偶</option>
                                <option value="sibling">兄弟姐妹</option>
                                <option value="friend">朋友</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-fieldset-row">
                        <div class="form-input-group">
                            <label class="form-input-label" for="emergency-phone">
                                联系电话 <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="tel" id="emergency-phone" name="emergency-phone" class="form-input" placeholder="请输入联系电话" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 折叠/展开功能
            function toggleFieldset(fieldset) {
                fieldset.classList.toggle('collapsed');
            }

            // 焦点状态管理
            function initFieldsetFocus() {
                const containers = document.querySelectorAll('.form-fieldset-container');
                
                containers.forEach(container => {
                    const inputs = container.querySelectorAll('input, select, textarea');
                    
                    inputs.forEach(input => {
                        input.addEventListener('focus', () => {
                            container.classList.add('focus-within');
                        });
                        
                        input.addEventListener('blur', () => {
                            // 延迟检查，确保焦点不是移动到同一容器内的其他元素
                            setTimeout(() => {
                                const focusedElement = document.activeElement;
                                if (!container.contains(focusedElement)) {
                                    container.classList.remove('focus-within');
                                }
                            }, 0);
                        });
                    });
                });
            }

            // 表单验证
            function validateFieldset(fieldsetContainer) {
                const requiredInputs = fieldsetContainer.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                
                requiredInputs.forEach(input => {
                    if (!input.value.trim()) {
                        input.classList.add('error');
                        isValid = false;
                    } else {
                        input.classList.remove('error');
                    }
                });
                
                return isValid;
            }

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', () => {
                initFieldsetFocus();
                
                // 添加必要的CSS样式（如果还没有引入其他组件的样式）
                const style = document.createElement('style');
                style.textContent = `
                    .form-input, .form-select-input, .form-textarea-input {
                        width: 100%;
                        border: 2px solid #d1d5db;
                        border-radius: 6px;
                        background-color: #ffffff;
                        transition: all 0.2s ease;
                        outline: none;
                        box-sizing: border-box;
                        font-family: inherit;
                    }
                    .form-input:focus, .form-select-input:focus, .form-textarea-input:focus {
                        border-color: #3b82f6;
                        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                    }
                    .form-input.error, .form-select-input.error, .form-textarea-input.error {
                        border-color: #ef4444;
                        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
                    }
                    .form-textarea-input {
                        resize: vertical;
                        min-height: 80px;
                    }
                    .form-checkbox-item, .form-radio-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 8px;
                        cursor: pointer;
                        min-height: 32px;
                    }
                    .form-checkbox-custom, .form-radio-custom {
                        width: 16px;
                        height: 16px;
                        border: 2px solid #d1d5db;
                        margin-right: 8px;
                        flex-shrink: 0;
                        transition: all 0.2s ease;
                    }
                    .form-checkbox-custom {
                        border-radius: 3px;
                    }
                    .form-radio-custom {
                        border-radius: 50%;
                    }
                `;
                document.head.appendChild(style);
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础分组字段 --&gt;
&lt;div class="form-fieldset-container"&gt;
    &lt;legend class="form-fieldset-legend"&gt;个人信息&lt;/legend&gt;
    &lt;div class="form-fieldset-description"&gt;
        请填写您的基本个人信息
    &lt;/div&gt;
    &lt;div class="form-fieldset-content"&gt;
        &lt;div class="form-fieldset-row two-columns"&gt;
            &lt;div class="form-input-group"&gt;
                &lt;label class="form-input-label"&gt;姓名&lt;/label&gt;
                &lt;input type="text" class="form-input" placeholder="请输入姓名"&gt;
            &lt;/div&gt;
            &lt;div class="form-input-group"&gt;
                &lt;label class="form-input-label"&gt;邮箱&lt;/label&gt;
                &lt;input type="email" class="form-input" placeholder="请输入邮箱"&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 主题色样式 --&gt;
&lt;div class="form-fieldset-primary"&gt;
    &lt;div class="form-fieldset-container"&gt;
        &lt;!-- 内容... --&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 可折叠样式 --&gt;
&lt;div class="form-fieldset-collapsible" onclick="toggleFieldset(this)"&gt;
    &lt;div class="form-fieldset-container"&gt;
        &lt;legend class="form-fieldset-legend"&gt;
            高级设置
            &lt;div class="form-fieldset-toggle"&gt;&lt;/div&gt;
        &lt;/legend&gt;
        &lt;!-- 内容... --&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 紧凑样式 --&gt;
&lt;div class="form-fieldset-compact"&gt;
    &lt;div class="form-fieldset-container"&gt;
        &lt;!-- 内容... --&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 卡片样式 --&gt;
&lt;div class="form-fieldset-card"&gt;
    &lt;div class="form-fieldset-container"&gt;
        &lt;!-- 内容... --&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 列布局 --&gt;
&lt;div class="form-fieldset-row"&gt;单列&lt;/div&gt;
&lt;div class="form-fieldset-row two-columns"&gt;两列&lt;/div&gt;
&lt;div class="form-fieldset-row three-columns"&gt;三列&lt;/div&gt;</code></pre>
    </div>
</body>
</html>