<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字链接组件</title>
</head>
<body>
    <!-- 文字链接组件 -->
    <div class="form-link">
        <style>
            .form-link {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-link-section {
                margin-bottom: 40px;
            }

            .form-link-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础链接样式 */
            .form-link-basic {
                color: #3b82f6;
                text-decoration: none;
                font-size: 16px;
                transition: all 0.2s ease;
                cursor: pointer;
                display: inline-block;
            }

            .form-link-basic:hover {
                color: #2563eb;
                text-decoration: underline;
            }

            .form-link-basic:active {
                color: #1e40af;
            }

            .form-link-basic:visited {
                color: #7c3aed;
            }

            /* 链接颜色变体 */
            .form-link-primary {
                color: #3b82f6;
            }

            .form-link-primary:hover {
                color: #2563eb;
            }

            .form-link-secondary {
                color: #6b7280;
            }

            .form-link-secondary:hover {
                color: #374151;
            }

            .form-link-success {
                color: #10b981;
            }

            .form-link-success:hover {
                color: #059669;
            }

            .form-link-danger {
                color: #ef4444;
            }

            .form-link-danger:hover {
                color: #dc2626;
            }

            .form-link-warning {
                color: #f59e0b;
            }

            .form-link-warning:hover {
                color: #d97706;
            }

            /* 链接尺寸 */
            .form-link-xs {
                font-size: 12px;
            }

            .form-link-sm {
                font-size: 14px;
            }

            .form-link-md {
                font-size: 16px;
            }

            .form-link-lg {
                font-size: 18px;
            }

            .form-link-xl {
                font-size: 20px;
            }

            /* 链接样式变体 */
            .form-link-underline {
                text-decoration: underline;
            }

            .form-link-no-underline {
                text-decoration: none;
            }

            .form-link-no-underline:hover {
                text-decoration: none;
            }

            .form-link-dotted {
                text-decoration: none;
                border-bottom: 1px dotted currentColor;
            }

            .form-link-dotted:hover {
                text-decoration: none;
                border-bottom-style: solid;
            }

            .form-link-dashed {
                text-decoration: none;
                border-bottom: 1px dashed currentColor;
            }

            .form-link-dashed:hover {
                text-decoration: none;
                border-bottom-style: solid;
            }

            /* 带图标的链接 */
            .form-link-with-icon {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                text-decoration: none;
                transition: all 0.2s ease;
            }

            .form-link-with-icon:hover {
                text-decoration: none;
                transform: translateX(2px);
            }

            .form-link-with-icon .form-link-icon {
                width: 16px;
                height: 16px;
                transition: transform 0.2s ease;
            }

            .form-link-with-icon:hover .form-link-icon {
                transform: translateX(2px);
            }

            .form-link-with-icon.icon-left {
                flex-direction: row;
            }

            .form-link-with-icon.icon-right {
                flex-direction: row-reverse;
            }

            /* 按钮样式链接 */
            .form-link-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 8px 16px;
                border: 1px solid currentColor;
                border-radius: 6px;
                text-decoration: none;
                font-weight: 500;
                transition: all 0.2s ease;
                min-height: 36px;
                box-sizing: border-box;
            }

            .form-link-button:hover {
                text-decoration: none;
                background-color: currentColor;
                color: white;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .form-link-button.filled {
                background-color: currentColor;
                color: white;
                border-color: transparent;
            }

            .form-link-button.filled:hover {
                opacity: 0.9;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }

            /* 卡片样式链接 */
            .form-link-card {
                display: block;
                padding: 16px;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                text-decoration: none;
                color: #374151;
                background: white;
                transition: all 0.2s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .form-link-card:hover {
                text-decoration: none;
                color: #374151;
                border-color: #3b82f6;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .form-link-card-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 4px;
                color: #1f2937;
            }

            .form-link-card-desc {
                font-size: 14px;
                color: #6b7280;
                line-height: 1.4;
            }

            /* 面包屑链接 */
            .form-link-breadcrumb {
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;
            }

            .form-link-breadcrumb-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
            }

            .form-link-breadcrumb-item:not(:last-child)::after {
                content: '/';
                color: #9ca3af;
                font-weight: normal;
            }

            .form-link-breadcrumb-item a {
                color: #6b7280;
                text-decoration: none;
                transition: color 0.2s ease;
            }

            .form-link-breadcrumb-item a:hover {
                color: #3b82f6;
            }

            .form-link-breadcrumb-item.current {
                color: #374151;
                font-weight: 500;
            }

            /* 标签样式链接 */
            .form-link-tag {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                text-decoration: none;
                transition: all 0.2s ease;
                border: 1px solid currentColor;
            }

            .form-link-tag:hover {
                text-decoration: none;
                background-color: currentColor;
                color: white;
            }

            .form-link-tag.filled {
                background-color: currentColor;
                color: white;
                border-color: transparent;
            }

            .form-link-tag.filled:hover {
                opacity: 0.9;
            }

            /* 禁用状态 */
            .form-link-disabled {
                color: #9ca3af;
                cursor: not-allowed;
                pointer-events: none;
                opacity: 0.6;
            }

            /* 加载状态 */
            .form-link-loading {
                position: relative;
                color: transparent;
                pointer-events: none;
            }

            .form-link-loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin: -8px 0 0 -8px;
                border: 2px solid #e5e7eb;
                border-top-color: #3b82f6;
                border-radius: 50%;
                animation: linkSpin 1s linear infinite;
            }

            @keyframes linkSpin {
                to {
                    transform: rotate(360deg);
                }
            }

            /* 动画效果 */
            .form-link-animated {
                position: relative;
                overflow: hidden;
            }

            .form-link-animated::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s ease;
            }

            .form-link-animated:hover::before {
                left: 100%;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-link-card {
                    padding: 12px;
                }

                .form-link-button {
                    padding: 10px 16px;
                    min-height: 44px;
                }

                .form-link-breadcrumb {
                    gap: 6px;
                }

                .form-link-breadcrumb-item {
                    gap: 6px;
                    font-size: 13px;
                }
            }

            @media (max-width: 480px) {
                .form-link-with-icon {
                    gap: 4px;
                }

                .form-link-with-icon .form-link-icon {
                    width: 14px;
                    height: 14px;
                }

                .form-link-breadcrumb {
                    gap: 4px;
                }

                .form-link-breadcrumb-item {
                    gap: 4px;
                    font-size: 12px;
                }
            }

            /* 演示样式 */
            .demo-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
                margin-bottom: 20px;
            }

            .demo-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin-bottom: 20px;
            }

            .demo-inline {
                display: flex;
                align-items: center;
                gap: 16px;
                flex-wrap: wrap;
                margin-bottom: 20px;
            }
        </style>

        <!-- 基础链接样式 -->
        <div class="form-link-section">
            <h3 class="form-link-title">基础链接样式</h3>
            <div class="demo-inline">
                <a href="#" class="form-link-basic">默认链接</a>
                <a href="#" class="form-link-basic form-link-underline">带下划线链接</a>
                <a href="#" class="form-link-basic form-link-no-underline">无下划线链接</a>
                <a href="#" class="form-link-basic form-link-dotted">点线链接</a>
                <a href="#" class="form-link-basic form-link-dashed">虚线链接</a>
            </div>
        </div>

        <!-- 链接颜色 -->
        <div class="form-link-section">
            <h3 class="form-link-title">链接颜色</h3>
            <div class="demo-inline">
                <a href="#" class="form-link-basic form-link-primary">主要链接</a>
                <a href="#" class="form-link-basic form-link-secondary">次要链接</a>
                <a href="#" class="form-link-basic form-link-success">成功链接</a>
                <a href="#" class="form-link-basic form-link-danger">危险链接</a>
                <a href="#" class="form-link-basic form-link-warning">警告链接</a>
            </div>
        </div>

        <!-- 链接尺寸 -->
        <div class="form-link-section">
            <h3 class="form-link-title">链接尺寸</h3>
            <div class="demo-inline">
                <a href="#" class="form-link-basic form-link-xs">超小链接</a>
                <a href="#" class="form-link-basic form-link-sm">小链接</a>
                <a href="#" class="form-link-basic form-link-md">中等链接</a>
                <a href="#" class="form-link-basic form-link-lg">大链接</a>
                <a href="#" class="form-link-basic form-link-xl">超大链接</a>
            </div>
        </div>

        <!-- 带图标的链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">带图标的链接</h3>
            <div class="demo-list">
                <a href="#" class="form-link-with-icon form-link-primary icon-left">
                    <svg class="form-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    返回首页
                </a>

                <a href="#" class="form-link-with-icon form-link-primary icon-right">
                    查看详情
                    <svg class="form-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"/>
                    </svg>
                </a>

                <a href="#" class="form-link-with-icon form-link-success icon-left">
                    <svg class="form-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    下载文件
                </a>

                <a href="#" class="form-link-with-icon form-link-danger icon-left">
                    <svg class="form-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 7l-.867 12.142A2 2 0 0 1 16.138 21H7.862a2 2 0 0 1-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v3M4 7h16"/>
                    </svg>
                    删除项目
                </a>
            </div>
        </div>

        <!-- 按钮样式链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">按钮样式链接</h3>
            <div class="demo-inline">
                <a href="#" class="form-link-button form-link-primary">主要按钮</a>
                <a href="#" class="form-link-button form-link-secondary">次要按钮</a>
                <a href="#" class="form-link-button form-link-success filled">成功按钮</a>
                <a href="#" class="form-link-button form-link-danger filled">危险按钮</a>
                <a href="#" class="form-link-button form-link-primary form-link-disabled">禁用按钮</a>
                <a href="#" class="form-link-button form-link-primary form-link-loading">加载中</a>
            </div>
        </div>

        <!-- 卡片样式链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">卡片样式链接</h3>
            <div class="demo-grid">
                <a href="#" class="form-link-card">
                    <div class="form-link-card-title">产品介绍</div>
                    <div class="form-link-card-desc">了解我们的产品特性和优势，探索更多可能性。</div>
                </a>

                <a href="#" class="form-link-card">
                    <div class="form-link-card-title">技术文档</div>
                    <div class="form-link-card-desc">查看详细的API文档和开发指南，快速上手。</div>
                </a>

                <a href="#" class="form-link-card">
                    <div class="form-link-card-title">联系我们</div>
                    <div class="form-link-card-desc">有任何问题或建议，欢迎随时与我们联系。</div>
                </a>
            </div>
        </div>

        <!-- 面包屑链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">面包屑链接</h3>
            <nav class="form-link-breadcrumb">
                <div class="form-link-breadcrumb-item">
                    <a href="#">首页</a>
                </div>
                <div class="form-link-breadcrumb-item">
                    <a href="#">产品中心</a>
                </div>
                <div class="form-link-breadcrumb-item">
                    <a href="#">软件产品</a>
                </div>
                <div class="form-link-breadcrumb-item current">
                    表单组件库
                </div>
            </nav>
        </div>

        <!-- 标签样式链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">标签样式链接</h3>
            <div class="demo-inline">
                <a href="#" class="form-link-tag form-link-primary">JavaScript</a>
                <a href="#" class="form-link-tag form-link-success filled">Vue.js</a>
                <a href="#" class="form-link-tag form-link-warning">React</a>
                <a href="#" class="form-link-tag form-link-danger filled">Angular</a>
                <a href="#" class="form-link-tag form-link-secondary">TypeScript</a>
            </div>
        </div>

        <!-- 特殊效果链接 -->
        <div class="form-link-section">
            <h3 class="form-link-title">特殊效果链接</h3>
            <div class="demo-list">
                <a href="#" class="form-link-button form-link-primary form-link-animated">动画效果链接</a>
                <a href="#" class="form-link-basic form-link-primary form-link-lg">悬停变色链接</a>
            </div>
        </div>

        <script>
            // 链接点击事件处理
            document.addEventListener('DOMContentLoaded', function() {
                // 为所有演示链接添加点击事件
                const demoLinks = document.querySelectorAll('.form-link a[href="#"]');
                demoLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // 显示点击提示
                        const toast = document.createElement('div');
                        toast.textContent = `点击了链接: ${this.textContent.trim()}`;
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #3b82f6;
                            color: white;
                            padding: 12px 16px;
                            border-radius: 6px;
                            font-size: 14px;
                            z-index: 1000;
                            animation: slideIn 0.3s ease;
                        `;
                        document.body.appendChild(toast);

                        setTimeout(() => {
                            if (document.body.contains(toast)) {
                                document.body.removeChild(toast);
                            }
                        }, 2000);
                    });
                });

                // 模拟加载状态
                const loadingLinks = document.querySelectorAll('.form-link-loading');
                loadingLinks.forEach(link => {
                    setTimeout(() => {
                        link.classList.remove('form-link-loading');
                        link.textContent = '加载完成';
                    }, 3000);
                });
            });

            // 添加滑入动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础链接 --&gt;
&lt;a href="#" class="form-link-basic form-link-primary"&gt;链接文字&lt;/a&gt;

&lt;!-- 带图标链接 --&gt;
&lt;a href="#" class="form-link-with-icon form-link-primary icon-left"&gt;
    &lt;svg class="form-link-icon"&gt;...&lt;/svg&gt;
    链接文字
&lt;/a&gt;

&lt;!-- 按钮样式链接 --&gt;
&lt;a href="#" class="form-link-button form-link-primary filled"&gt;按钮链接&lt;/a&gt;

&lt;!-- 卡片样式链接 --&gt;
&lt;a href="#" class="form-link-card"&gt;
    &lt;div class="form-link-card-title"&gt;标题&lt;/div&gt;
    &lt;div class="form-link-card-desc"&gt;描述文字&lt;/div&gt;
&lt;/a&gt;

&lt;!-- 面包屑导航 --&gt;
&lt;nav class="form-link-breadcrumb"&gt;
    &lt;div class="form-link-breadcrumb-item"&gt;
        &lt;a href="#"&gt;首页&lt;/a&gt;
    &lt;/div&gt;
    &lt;div class="form-link-breadcrumb-item current"&gt;
        当前页面
    &lt;/div&gt;
&lt;/nav&gt;</code></pre>
    </div>
</body>
</html>
