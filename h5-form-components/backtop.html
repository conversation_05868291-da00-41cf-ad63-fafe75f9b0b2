<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回到顶部组件</title>
</head>
<body>
    <!-- 回到顶部组件 -->
    <div class="form-backtop">
        <style>
            .form-backtop {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-backtop-section {
                margin-bottom: 40px;
            }

            .form-backtop-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础回到顶部按钮 */
            .form-backtop-button {
                position: fixed;
                right: 20px;
                bottom: 20px;
                width: 48px;
                height: 48px;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(20px);
            }

            .form-backtop-button.visible {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }

            .form-backtop-button:hover {
                background: #2563eb;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
            }

            .form-backtop-button:active {
                transform: translateY(0);
            }

            /* 不同尺寸 */
            .form-backtop-button.small {
                width: 40px;
                height: 40px;
                font-size: 16px;
                right: 16px;
                bottom: 16px;
            }

            .form-backtop-button.large {
                width: 56px;
                height: 56px;
                font-size: 24px;
                right: 24px;
                bottom: 24px;
            }

            /* 不同形状 */
            .form-backtop-button.square {
                border-radius: 8px;
            }

            .form-backtop-button.rounded {
                border-radius: 12px;
            }

            /* 不同颜色主题 */
            .form-backtop-button.success {
                background: #10b981;
            }

            .form-backtop-button.success:hover {
                background: #059669;
            }

            .form-backtop-button.warning {
                background: #f59e0b;
            }

            .form-backtop-button.warning:hover {
                background: #d97706;
            }

            .form-backtop-button.danger {
                background: #ef4444;
            }

            .form-backtop-button.danger:hover {
                background: #dc2626;
            }

            .form-backtop-button.purple {
                background: #8b5cf6;
            }

            .form-backtop-button.purple:hover {
                background: #7c3aed;
            }

            /* 带文字的按钮 */
            .form-backtop-button.with-text {
                width: auto;
                min-width: 48px;
                padding: 12px 16px;
                border-radius: 24px;
                font-size: 14px;
                font-weight: 500;
                gap: 6px;
            }

            .form-backtop-button.with-text.small {
                padding: 8px 12px;
                font-size: 12px;
                min-width: 40px;
            }

            .form-backtop-button.with-text.large {
                padding: 16px 20px;
                font-size: 16px;
                min-width: 56px;
            }

            /* 进度指示器 */
            .form-backtop-button.with-progress {
                position: relative;
                overflow: hidden;
            }

            .form-backtop-button.with-progress::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: conic-gradient(from 0deg, transparent 0deg, rgba(255, 255, 255, 0.3) var(--progress, 0deg), transparent var(--progress, 0deg));
                border-radius: inherit;
                pointer-events: none;
            }

            /* 带数字的按钮 */
            .form-backtop-button.with-number {
                flex-direction: column;
                font-size: 12px;
                line-height: 1;
                gap: 2px;
            }

            .form-backtop-number {
                font-size: 10px;
                opacity: 0.8;
            }

            /* 浮动样式 */
            .form-backtop-button.floating {
                animation: floating 3s ease-in-out infinite;
            }

            @keyframes floating {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-6px); }
            }

            .form-backtop-button.floating.visible {
                animation: floating 3s ease-in-out infinite;
            }

            /* 脉冲效果 */
            .form-backtop-button.pulse {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(59, 130, 246, 0.7); }
                70% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 10px rgba(59, 130, 246, 0); }
                100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(59, 130, 246, 0); }
            }

            /* 侧边栏样式 */
            .form-backtop-sidebar {
                position: fixed;
                right: 20px;
                bottom: 20px;
                display: flex;
                flex-direction: column;
                gap: 12px;
                z-index: 1000;
            }

            .form-backtop-sidebar .form-backtop-button {
                position: static;
                margin: 0;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-backtop-button {
                    right: 16px;
                    bottom: 16px;
                    width: 44px;
                    height: 44px;
                    font-size: 18px;
                }

                .form-backtop-button.small {
                    width: 36px;
                    height: 36px;
                    font-size: 14px;
                }

                .form-backtop-button.large {
                    width: 52px;
                    height: 52px;
                    font-size: 22px;
                }

                .form-backtop-button.with-text {
                    padding: 10px 14px;
                    font-size: 13px;
                }

                .form-backtop-sidebar {
                    right: 16px;
                    bottom: 16px;
                    gap: 8px;
                }
            }

            @media (max-width: 480px) {
                .form-backtop-button {
                    right: 12px;
                    bottom: 12px;
                    width: 40px;
                    height: 40px;
                    font-size: 16px;
                }

                .form-backtop-button.with-text {
                    padding: 8px 12px;
                    font-size: 12px;
                }
            }

            /* 演示内容样式 */
            .demo-content {
                height: 200vh;
                background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
                padding: 40px 20px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
            }

            .demo-section {
                margin-bottom: 24px;
                padding: 20px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                max-width: 600px;
                width: 100%;
            }

            .demo-buttons {
                display: flex;
                gap: 12px;
                flex-wrap: wrap;
                justify-content: center;
                margin-top: 20px;
            }

            .demo-button {
                padding: 8px 16px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background: white;
                color: #374151;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s ease;
            }

            .demo-button:hover {
                border-color: #3b82f6;
                color: #3b82f6;
            }

            .demo-button.active {
                background: #3b82f6;
                border-color: #3b82f6;
                color: white;
            }
        </style>

        <!-- 演示内容 -->
        <div class="demo-content">
            <div class="demo-section">
                <h3 class="form-backtop-title">回到顶部组件演示</h3>
                <p>向下滚动页面，当滚动距离超过200px时，回到顶部按钮会出现在页面右下角。</p>
                <p>点击按钮可以平滑滚动回到页面顶部。</p>
                
                <div class="demo-buttons">
                    <button class="demo-button active" onclick="switchBacktopStyle('basic')">基础样式</button>
                    <button class="demo-button" onclick="switchBacktopStyle('square')">方形按钮</button>
                    <button class="demo-button" onclick="switchBacktopStyle('text')">带文字</button>
                    <button class="demo-button" onclick="switchBacktopStyle('progress')">进度指示</button>
                    <button class="demo-button" onclick="switchBacktopStyle('floating')">浮动效果</button>
                    <button class="demo-button" onclick="switchBacktopStyle('sidebar')">侧边栏</button>
                </div>
            </div>

            <div class="demo-section">
                <h4>功能特性</h4>
                <ul style="text-align: left; max-width: 400px;">
                    <li>平滑滚动动画</li>
                    <li>自动显示/隐藏</li>
                    <li>多种样式主题</li>
                    <li>响应式设计</li>
                    <li>进度指示器</li>
                    <li>自定义触发距离</li>
                    <li>多种动画效果</li>
                    <li>移动端优化</li>
                </ul>
            </div>

            <div class="demo-section">
                <h4>使用场景</h4>
                <p>适用于长页面、文章阅读、商品列表、数据表格等需要快速返回顶部的场景。</p>
            </div>

            <div class="demo-section">
                <h4>继续滚动</h4>
                <p>继续向下滚动查看更多内容...</p>
            </div>

            <div class="demo-section">
                <h4>更多内容</h4>
                <p>这里是更多的演示内容，用于展示回到顶部功能的效果。</p>
                <p>当页面内容很长时，用户可以通过回到顶部按钮快速返回页面顶部。</p>
            </div>

            <div class="demo-section">
                <h4>页面底部</h4>
                <p>这是页面的底部内容。点击右下角的回到顶部按钮可以快速返回页面顶部。</p>
            </div>
        </div>

        <!-- 回到顶部按钮 -->
        <button class="form-backtop-button" id="backtopBtn" onclick="scrollToTop()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="18,15 12,9 6,15"></polyline>
            </svg>
        </button>

        <!-- 侧边栏样式（隐藏） -->
        <div class="form-backtop-sidebar" id="backtopSidebar" style="display: none;">
            <button class="form-backtop-button small success" onclick="scrollToTop()" title="回到顶部">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="18,15 12,9 6,15"></polyline>
                </svg>
            </button>
            <button class="form-backtop-button small warning" onclick="scrollToBottom()" title="到达底部">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
            </button>
            <button class="form-backtop-button small purple" onclick="window.print()" title="打印页面">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 6,2 18,2 18,9"></polyline>
                    <path d="M6,18H4a2,2,0,0,1-2-2V11a2,2,0,0,1,2-2H20a2,2,0,0,1,2,2v5a2,2,0,0,1-2,2H18"></path>
                    <polyline points="6,14 6,22 18,22 18,14"></polyline>
                </svg>
            </button>
        </div>

        <script>
            let currentStyle = 'basic';
            let scrollProgress = 0;

            // 滚动到顶部
            function scrollToTop() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }

            // 滚动到底部
            function scrollToBottom() {
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }

            // 切换回到顶部样式
            function switchBacktopStyle(style) {
                currentStyle = style;
                const backtopBtn = document.getElementById('backtopBtn');
                const backtopSidebar = document.getElementById('backtopSidebar');
                const buttons = document.querySelectorAll('.demo-button');
                
                // 更新按钮状态
                buttons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.textContent.includes(getStyleName(style))) {
                        btn.classList.add('active');
                    }
                });

                // 重置样式
                backtopBtn.className = 'form-backtop-button';
                backtopBtn.style.display = '';
                backtopSidebar.style.display = 'none';
                backtopBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                `;

                // 应用新样式
                switch (style) {
                    case 'square':
                        backtopBtn.classList.add('square');
                        break;
                    case 'text':
                        backtopBtn.classList.add('with-text');
                        backtopBtn.innerHTML = `
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="18,15 12,9 6,15"></polyline>
                            </svg>
                            顶部
                        `;
                        break;
                    case 'progress':
                        backtopBtn.classList.add('with-progress');
                        break;
                    case 'floating':
                        backtopBtn.classList.add('floating');
                        break;
                    case 'sidebar':
                        backtopBtn.style.display = 'none';
                        backtopSidebar.style.display = 'flex';
                        break;
                }

                // 重新检查显示状态
                handleScroll();
            }

            function getStyleName(style) {
                const names = {
                    'basic': '基础样式',
                    'square': '方形按钮',
                    'text': '带文字',
                    'progress': '进度指示',
                    'floating': '浮动效果',
                    'sidebar': '侧边栏'
                };
                return names[style] || '基础样式';
            }

            // 处理滚动事件
            function handleScroll() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                scrollProgress = (scrollTop / docHeight) * 100;

                const backtopBtn = document.getElementById('backtopBtn');
                const backtopSidebar = document.getElementById('backtopSidebar');
                const showThreshold = 200;

                // 显示/隐藏按钮
                if (scrollTop > showThreshold) {
                    if (currentStyle === 'sidebar') {
                        backtopSidebar.classList.add('visible');
                        backtopSidebar.querySelectorAll('.form-backtop-button').forEach(btn => {
                            btn.classList.add('visible');
                        });
                    } else {
                        backtopBtn.classList.add('visible');
                    }
                } else {
                    if (currentStyle === 'sidebar') {
                        backtopSidebar.classList.remove('visible');
                        backtopSidebar.querySelectorAll('.form-backtop-button').forEach(btn => {
                            btn.classList.remove('visible');
                        });
                    } else {
                        backtopBtn.classList.remove('visible');
                    }
                }

                // 更新进度指示器
                if (currentStyle === 'progress') {
                    backtopBtn.style.setProperty('--progress', `${scrollProgress * 3.6}deg`);
                }
            }

            // 节流函数
            function throttle(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }

            // 初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 绑定滚动事件（使用节流）
                window.addEventListener('scroll', throttle(handleScroll, 100));

                // 初始检查
                handleScroll();

                // 键盘支持
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Home' && e.ctrlKey) {
                        e.preventDefault();
                        scrollToTop();
                    } else if (e.key === 'End' && e.ctrlKey) {
                        e.preventDefault();
                        scrollToBottom();
                    }
                });
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础回到顶部按钮 --&gt;
&lt;button class="form-backtop-button" onclick="scrollToTop()"&gt;
    &lt;svg&gt;...&lt;/svg&gt;
&lt;/button&gt;

&lt;!-- 带文字的按钮 --&gt;
&lt;button class="form-backtop-button with-text" onclick="scrollToTop()"&gt;
    &lt;svg&gt;...&lt;/svg&gt;
    顶部
&lt;/button&gt;

&lt;!-- 方形按钮 --&gt;
&lt;button class="form-backtop-button square success" onclick="scrollToTop()"&gt;
    &lt;svg&gt;...&lt;/svg&gt;
&lt;/button&gt;

&lt;!-- 进度指示器 --&gt;
&lt;button class="form-backtop-button with-progress" onclick="scrollToTop()"&gt;
    &lt;svg&gt;...&lt;/svg&gt;
&lt;/button&gt;

&lt;!-- 侧边栏样式 --&gt;
&lt;div class="form-backtop-sidebar"&gt;
    &lt;button class="form-backtop-button small" onclick="scrollToTop()"&gt;
        &lt;svg&gt;...&lt;/svg&gt;
    &lt;/button&gt;
&lt;/div&gt;

&lt;script&gt;
// 滚动到顶部函数
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 滚动事件处理
window.addEventListener('scroll', function() {
    const scrollTop = window.pageYOffset;
    const button = document.querySelector('.form-backtop-button');
    
    if (scrollTop &gt; 200) {
        button.classList.add('visible');
    } else {
        button.classList.remove('visible');
    }
});
&lt;/script&gt;</code></pre>
    </div>
</body>
</html>
