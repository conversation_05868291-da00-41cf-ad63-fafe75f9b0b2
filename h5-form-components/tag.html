<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签组件</title>
</head>
<body>
    <!-- 标签组件 -->
    <div class="form-tag">
        <style>
            .form-tag {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-tag-section {
                margin-bottom: 40px;
            }

            .form-tag-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础标签样式 */
            .form-tag-element {
                display: inline-flex;
                align-items: center;
                padding: 4px 8px;
                margin: 2px;
                font-size: 12px;
                font-weight: 500;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
                background: #f9fafb;
                color: #374151;
                transition: all 0.2s ease;
                white-space: nowrap;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .form-tag-element:hover {
                border-color: #d1d5db;
                background: #f3f4f6;
            }

            /* 标签尺寸 */
            .form-tag-element.small {
                padding: 2px 6px;
                font-size: 11px;
                border-radius: 3px;
            }

            .form-tag-element.medium {
                padding: 6px 12px;
                font-size: 14px;
                border-radius: 6px;
            }

            .form-tag-element.large {
                padding: 8px 16px;
                font-size: 16px;
                border-radius: 8px;
            }

            /* 标签颜色主题 */
            .form-tag-element.primary {
                background: #dbeafe;
                border-color: #93c5fd;
                color: #1e40af;
            }

            .form-tag-element.primary:hover {
                background: #bfdbfe;
                border-color: #60a5fa;
            }

            .form-tag-element.success {
                background: #d1fae5;
                border-color: #6ee7b7;
                color: #065f46;
            }

            .form-tag-element.success:hover {
                background: #a7f3d0;
                border-color: #34d399;
            }

            .form-tag-element.warning {
                background: #fef3c7;
                border-color: #fcd34d;
                color: #92400e;
            }

            .form-tag-element.warning:hover {
                background: #fde68a;
                border-color: #f59e0b;
            }

            .form-tag-element.danger {
                background: #fee2e2;
                border-color: #fca5a5;
                color: #991b1b;
            }

            .form-tag-element.danger:hover {
                background: #fecaca;
                border-color: #f87171;
            }

            .form-tag-element.purple {
                background: #ede9fe;
                border-color: #c4b5fd;
                color: #5b21b6;
            }

            .form-tag-element.purple:hover {
                background: #ddd6fe;
                border-color: #a78bfa;
            }

            .form-tag-element.pink {
                background: #fce7f3;
                border-color: #f9a8d4;
                color: #be185d;
            }

            .form-tag-element.pink:hover {
                background: #fbcfe8;
                border-color: #f472b6;
            }

            /* 填充样式 */
            .form-tag-element.filled {
                border-color: transparent;
            }

            .form-tag-element.filled.primary {
                background: #3b82f6;
                color: white;
            }

            .form-tag-element.filled.primary:hover {
                background: #2563eb;
            }

            .form-tag-element.filled.success {
                background: #10b981;
                color: white;
            }

            .form-tag-element.filled.success:hover {
                background: #059669;
            }

            .form-tag-element.filled.warning {
                background: #f59e0b;
                color: white;
            }

            .form-tag-element.filled.warning:hover {
                background: #d97706;
            }

            .form-tag-element.filled.danger {
                background: #ef4444;
                color: white;
            }

            .form-tag-element.filled.danger:hover {
                background: #dc2626;
            }

            /* 可关闭标签 */
            .form-tag-element.closable {
                padding-right: 4px;
            }

            .form-tag-close {
                margin-left: 6px;
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 12px;
                line-height: 1;
            }

            .form-tag-close:hover {
                background: rgba(0, 0, 0, 0.1);
            }

            .form-tag-element.filled .form-tag-close:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            /* 可点击标签 */
            .form-tag-element.clickable {
                cursor: pointer;
                user-select: none;
            }

            .form-tag-element.clickable:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .form-tag-element.clickable:active {
                transform: translateY(0);
            }

            /* 可选择标签 */
            .form-tag-element.selectable {
                cursor: pointer;
                user-select: none;
            }

            .form-tag-element.selectable.selected {
                border-color: #3b82f6;
                background: #dbeafe;
                color: #1e40af;
            }

            .form-tag-element.selectable.selected.filled {
                background: #3b82f6;
                color: white;
            }

            /* 带图标的标签 */
            .form-tag-element.with-icon {
                padding-left: 6px;
            }

            .form-tag-icon {
                width: 14px;
                height: 14px;
                margin-right: 4px;
                flex-shrink: 0;
            }

            /* 圆形标签 */
            .form-tag-element.rounded {
                border-radius: 20px;
            }

            /* 标签组 */
            .form-tag-group {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-bottom: 16px;
            }

            .form-tag-group-title {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            /* 添加标签输入 */
            .form-tag-input-container {
                display: inline-flex;
                align-items: center;
                margin: 2px;
            }

            .form-tag-input {
                border: 1px dashed #d1d5db;
                background: transparent;
                padding: 4px 8px;
                font-size: 12px;
                border-radius: 4px;
                outline: none;
                min-width: 60px;
                max-width: 120px;
            }

            .form-tag-input:focus {
                border-color: #3b82f6;
                border-style: solid;
            }

            .form-tag-add-btn {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                border: 1px dashed #d1d5db;
                background: transparent;
                border-radius: 4px;
                cursor: pointer;
                color: #6b7280;
                font-size: 14px;
                margin: 2px;
                transition: all 0.2s ease;
            }

            .form-tag-add-btn:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                border-style: solid;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-tag-element {
                    font-size: 11px;
                    padding: 6px 10px;
                    max-width: 150px;
                }

                .form-tag-element.small {
                    padding: 4px 8px;
                    font-size: 10px;
                }

                .form-tag-element.medium {
                    padding: 8px 12px;
                    font-size: 13px;
                }

                .form-tag-element.large {
                    padding: 10px 14px;
                    font-size: 15px;
                }
            }

            @media (max-width: 480px) {
                .form-tag-element {
                    max-width: 120px;
                }

                .form-tag-group {
                    gap: 2px;
                }
            }

            /* 演示样式 */
            .demo-section {
                margin-bottom: 24px;
            }

            .demo-label {
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 8px;
                display: block;
            }
        </style>

        <!-- 基础标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">基础标签</h3>
            <div class="demo-section">
                <span class="demo-label">默认样式</span>
                <div class="form-tag-group">
                    <span class="form-tag-element">标签一</span>
                    <span class="form-tag-element">标签二</span>
                    <span class="form-tag-element">标签三</span>
                    <span class="form-tag-element">这是一个很长的标签名称</span>
                </div>
            </div>
        </div>

        <!-- 不同尺寸 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">不同尺寸</h3>
            <div class="demo-section">
                <span class="demo-label">小尺寸</span>
                <div class="form-tag-group">
                    <span class="form-tag-element small primary">小标签</span>
                    <span class="form-tag-element small success">成功</span>
                    <span class="form-tag-element small warning">警告</span>
                    <span class="form-tag-element small danger">错误</span>
                </div>

                <span class="demo-label">中等尺寸</span>
                <div class="form-tag-group">
                    <span class="form-tag-element medium primary">中等标签</span>
                    <span class="form-tag-element medium success">成功</span>
                    <span class="form-tag-element medium warning">警告</span>
                    <span class="form-tag-element medium danger">错误</span>
                </div>

                <span class="demo-label">大尺寸</span>
                <div class="form-tag-group">
                    <span class="form-tag-element large primary">大标签</span>
                    <span class="form-tag-element large success">成功</span>
                    <span class="form-tag-element large warning">警告</span>
                    <span class="form-tag-element large danger">错误</span>
                </div>
            </div>
        </div>

        <!-- 不同颜色 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">不同颜色</h3>
            <div class="demo-section">
                <span class="demo-label">浅色主题</span>
                <div class="form-tag-group">
                    <span class="form-tag-element primary">主要</span>
                    <span class="form-tag-element success">成功</span>
                    <span class="form-tag-element warning">警告</span>
                    <span class="form-tag-element danger">危险</span>
                    <span class="form-tag-element purple">紫色</span>
                    <span class="form-tag-element pink">粉色</span>
                </div>

                <span class="demo-label">填充主题</span>
                <div class="form-tag-group">
                    <span class="form-tag-element filled primary">主要</span>
                    <span class="form-tag-element filled success">成功</span>
                    <span class="form-tag-element filled warning">警告</span>
                    <span class="form-tag-element filled danger">危险</span>
                    <span class="form-tag-element filled purple">紫色</span>
                    <span class="form-tag-element filled pink">粉色</span>
                </div>
            </div>
        </div>

        <!-- 可关闭标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">可关闭标签</h3>
            <div class="demo-section">
                <div class="form-tag-group" id="closable-tags">
                    <span class="form-tag-element closable primary">
                        JavaScript
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <span class="form-tag-element closable success">
                        Vue.js
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <span class="form-tag-element closable warning">
                        React
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <span class="form-tag-element closable danger">
                        Angular
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <span class="form-tag-element closable filled primary">
                        TypeScript
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 可选择标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">可选择标签</h3>
            <div class="demo-section">
                <span class="demo-label">技能标签（可多选）</span>
                <div class="form-tag-group" id="selectable-tags">
                    <span class="form-tag-element selectable" onclick="toggleTag(this)">HTML</span>
                    <span class="form-tag-element selectable" onclick="toggleTag(this)">CSS</span>
                    <span class="form-tag-element selectable selected" onclick="toggleTag(this)">JavaScript</span>
                    <span class="form-tag-element selectable" onclick="toggleTag(this)">Python</span>
                    <span class="form-tag-element selectable" onclick="toggleTag(this)">Java</span>
                    <span class="form-tag-element selectable" onclick="toggleTag(this)">Go</span>
                    <span class="form-tag-element selectable selected" onclick="toggleTag(this)">Node.js</span>
                </div>
            </div>
        </div>

        <!-- 带图标的标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">带图标的标签</h3>
            <div class="demo-section">
                <div class="form-tag-group">
                    <span class="form-tag-element with-icon primary">
                        <svg class="form-tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        用户
                    </span>
                    <span class="form-tag-element with-icon success">
                        <svg class="form-tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="20,6 9,17 4,12"/>
                        </svg>
                        已完成
                    </span>
                    <span class="form-tag-element with-icon warning">
                        <svg class="form-tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        进行中
                    </span>
                    <span class="form-tag-element with-icon danger">
                        <svg class="form-tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                        已取消
                    </span>
                </div>
            </div>
        </div>

        <!-- 圆形标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">圆形标签</h3>
            <div class="demo-section">
                <div class="form-tag-group">
                    <span class="form-tag-element rounded primary">热门</span>
                    <span class="form-tag-element rounded success">推荐</span>
                    <span class="form-tag-element rounded warning">限时</span>
                    <span class="form-tag-element rounded danger">售罄</span>
                    <span class="form-tag-element rounded filled primary">新品</span>
                    <span class="form-tag-element rounded filled success">特价</span>
                </div>
            </div>
        </div>

        <!-- 动态添加标签 -->
        <div class="form-tag-section">
            <h3 class="form-tag-title">动态添加标签</h3>
            <div class="demo-section">
                <span class="demo-label">兴趣爱好</span>
                <div class="form-tag-group" id="dynamic-tags">
                    <span class="form-tag-element closable primary">
                        阅读
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <span class="form-tag-element closable success">
                        运动
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    </span>
                    <div class="form-tag-input-container">
                        <input type="text" class="form-tag-input" placeholder="添加标签" 
                               onkeypress="addTagOnEnter(event, this)" onblur="addTag(this)">
                    </div>
                    <button class="form-tag-add-btn" onclick="focusInput(this)">+</button>
                </div>
            </div>
        </div>

        <script>
            // 移除标签
            function removeTag(closeBtn) {
                const tag = closeBtn.parentElement;
                tag.style.animation = 'tagFadeOut 0.3s ease';
                setTimeout(() => {
                    tag.remove();
                }, 300);
            }

            // 切换标签选择状态
            function toggleTag(tag) {
                tag.classList.toggle('selected');
                
                // 显示选择状态提示
                const isSelected = tag.classList.contains('selected');
                const toast = document.createElement('div');
                toast.textContent = `${tag.textContent}: ${isSelected ? '已选择' : '已取消'}`;
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${isSelected ? '#10b981' : '#6b7280'};
                    color: white;
                    padding: 12px 16px;
                    border-radius: 6px;
                    font-size: 14px;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                `;
                document.body.appendChild(toast);

                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 2000);
            }

            // 添加标签
            function addTag(input) {
                const value = input.value.trim();
                if (value) {
                    const tagGroup = input.closest('.form-tag-group');
                    const inputContainer = input.closest('.form-tag-input-container');
                    
                    const newTag = document.createElement('span');
                    newTag.className = 'form-tag-element closable primary';
                    newTag.innerHTML = `
                        ${value}
                        <span class="form-tag-close" onclick="removeTag(this)">×</span>
                    `;
                    
                    tagGroup.insertBefore(newTag, inputContainer);
                    input.value = '';
                    
                    // 添加动画
                    newTag.style.animation = 'tagFadeIn 0.3s ease';
                }
            }

            // 回车添加标签
            function addTagOnEnter(event, input) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    addTag(input);
                }
            }

            // 聚焦输入框
            function focusInput(btn) {
                const input = btn.previousElementSibling.querySelector('.form-tag-input');
                input.focus();
            }

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes tagFadeIn {
                    from {
                        opacity: 0;
                        transform: scale(0.8);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }

                @keyframes tagFadeOut {
                    from {
                        opacity: 1;
                        transform: scale(1);
                    }
                    to {
                        opacity: 0;
                        transform: scale(0.8);
                    }
                }

                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础标签 --&gt;
&lt;span class="form-tag-element"&gt;标签文字&lt;/span&gt;

&lt;!-- 不同颜色和尺寸 --&gt;
&lt;span class="form-tag-element primary medium"&gt;主要标签&lt;/span&gt;
&lt;span class="form-tag-element filled success"&gt;成功标签&lt;/span&gt;

&lt;!-- 可关闭标签 --&gt;
&lt;span class="form-tag-element closable primary"&gt;
    标签文字
    &lt;span class="form-tag-close" onclick="removeTag(this)"&gt;×&lt;/span&gt;
&lt;/span&gt;

&lt;!-- 可选择标签 --&gt;
&lt;span class="form-tag-element selectable" onclick="toggleTag(this)"&gt;
    可选择标签
&lt;/span&gt;

&lt;!-- 带图标标签 --&gt;
&lt;span class="form-tag-element with-icon primary"&gt;
    &lt;svg class="form-tag-icon"&gt;...&lt;/svg&gt;
    图标标签
&lt;/span&gt;</code></pre>
    </div>
</body>
</html>
