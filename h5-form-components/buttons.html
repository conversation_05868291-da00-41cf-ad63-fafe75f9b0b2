<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮变体组件</title>
</head>
<body>
    <!-- 按钮变体组件 -->
    <div class="form-buttons">
        <style>
            .form-buttons {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                text-decoration: none;
                cursor: pointer;
                transition: all 0.2s ease;
                min-height: 44px;
                margin-right: 8px;
                margin-bottom: 8px;
                outline: none;
                user-select: none;
            }

            .form-button:focus {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            /* 主要按钮 */
            .form-button-primary {
                background-color: #3b82f6;
                color: #ffffff;
            }

            .form-button-primary:hover:not(:disabled) {
                background-color: #2563eb;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            .form-button-primary:active:not(:disabled) {
                transform: translateY(0);
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            }

            /* 次要按钮 */
            .form-button-secondary {
                background-color: #ffffff;
                color: #374151;
                border: 2px solid #d1d5db;
            }

            .form-button-secondary:hover:not(:disabled) {
                background-color: #f9fafb;
                border-color: #9ca3af;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .form-button-secondary:active:not(:disabled) {
                transform: translateY(0);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            /* 危险按钮 */
            .form-button-danger {
                background-color: #ef4444;
                color: #ffffff;
            }

            .form-button-danger:hover:not(:disabled) {
                background-color: #dc2626;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
            }

            .form-button-danger:active:not(:disabled) {
                transform: translateY(0);
                box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
            }

            .form-button-group {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-bottom: 20px;
            }

            @media (max-width: 480px) {
                .form-buttons {
                    margin: 12px 0;
                }

                .form-button {
                    width: 100%;
                    margin-right: 0;
                    margin-bottom: 12px;
                    padding: 14px 24px;
                    font-size: 16px;
                    min-height: 48px;
                }

                .form-button-group {
                    flex-direction: column;
                    gap: 12px;
                }
            }
        </style>

        <div class="form-button-group">
            <button class="form-button form-button-primary" type="submit">
                新增
            </button>
            
            <button class="form-button form-button-secondary" type="button">
                取消
            </button>
            
            <button class="form-button form-button-danger" type="button">
                删除
            </button>
        </div>

        <div class="form-button-group">
            <button class="form-button form-button-primary" disabled>
                禁用状态
            </button>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 主要按钮 --&gt;
&lt;button class="form-button form-button-primary" type="submit"&gt;
    新增
&lt;/button&gt;

&lt;!-- 次要按钮 --&gt;
&lt;button class="form-button form-button-secondary" type="button"&gt;
    取消
&lt;/button&gt;

&lt;!-- 危险按钮 --&gt;
&lt;button class="form-button form-button-danger" type="button"&gt;
    删除
&lt;/button&gt;

&lt;!-- 按钮组 --&gt;
&lt;div class="form-button-group"&gt;
    &lt;!-- 多个按钮... --&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>