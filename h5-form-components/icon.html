<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标组件</title>
</head>
<body>
    <!-- 图标组件 -->
    <div class="form-icon">
        <style>
            .form-icon {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-icon-section {
                margin-bottom: 40px;
            }

            .form-icon-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础图标样式 */
            .form-icon-svg {
                display: inline-block;
                width: 24px;
                height: 24px;
                fill: currentColor;
                vertical-align: middle;
                transition: all 0.2s ease;
            }

            /* 图标尺寸 */
            .form-icon-xs { width: 12px; height: 12px; }
            .form-icon-sm { width: 16px; height: 16px; }
            .form-icon-md { width: 24px; height: 24px; }
            .form-icon-lg { width: 32px; height: 32px; }
            .form-icon-xl { width: 48px; height: 48px; }
            .form-icon-2xl { width: 64px; height: 64px; }

            /* 图标颜色 */
            .form-icon-gray { color: #6b7280; }
            .form-icon-blue { color: #3b82f6; }
            .form-icon-green { color: #10b981; }
            .form-icon-red { color: #ef4444; }
            .form-icon-yellow { color: #f59e0b; }
            .form-icon-purple { color: #8b5cf6; }
            .form-icon-pink { color: #ec4899; }
            .form-icon-indigo { color: #6366f1; }

            /* 图标容器 */
            .form-icon-container {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .form-icon-container.with-bg {
                padding: 8px;
                background: #f3f4f6;
            }

            .form-icon-container.with-bg:hover {
                background: #e5e7eb;
                transform: scale(1.05);
            }

            .form-icon-container.with-bg.blue {
                background: #dbeafe;
                color: #1e40af;
            }

            .form-icon-container.with-bg.blue:hover {
                background: #bfdbfe;
            }

            .form-icon-container.with-bg.green {
                background: #d1fae5;
                color: #065f46;
            }

            .form-icon-container.with-bg.green:hover {
                background: #a7f3d0;
            }

            .form-icon-container.with-bg.red {
                background: #fee2e2;
                color: #991b1b;
            }

            .form-icon-container.with-bg.red:hover {
                background: #fecaca;
            }

            .form-icon-container.with-bg.yellow {
                background: #fef3c7;
                color: #92400e;
            }

            .form-icon-container.with-bg.yellow:hover {
                background: #fde68a;
            }

            /* 圆形图标容器 */
            .form-icon-container.rounded {
                border-radius: 50%;
            }

            /* 图标按钮 */
            .form-icon-button {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background: white;
                color: #374151;
                text-decoration: none;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .form-icon-button:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .form-icon-button.primary {
                background: #3b82f6;
                border-color: #3b82f6;
                color: white;
            }

            .form-icon-button.primary:hover {
                background: #2563eb;
                border-color: #2563eb;
                color: white;
            }

            /* 图标网格 */
            .form-icon-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 16px;
                margin-bottom: 20px;
            }

            .form-icon-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 16px;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background: white;
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .form-icon-item:hover {
                border-color: #3b82f6;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            .form-icon-item .form-icon-svg {
                margin-bottom: 8px;
                color: #6b7280;
            }

            .form-icon-item:hover .form-icon-svg {
                color: #3b82f6;
            }

            .form-icon-name {
                font-size: 12px;
                color: #6b7280;
                text-align: center;
                font-weight: 500;
            }

            /* 动画图标 */
            .form-icon-animated {
                animation-duration: 2s;
                animation-iteration-count: infinite;
            }

            .form-icon-spin {
                animation-name: iconSpin;
            }

            .form-icon-pulse {
                animation-name: iconPulse;
            }

            .form-icon-bounce {
                animation-name: iconBounce;
            }

            .form-icon-shake {
                animation-name: iconShake;
            }

            @keyframes iconSpin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            @keyframes iconPulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            @keyframes iconBounce {
                0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
                40%, 43% { transform: translateY(-8px); }
                70% { transform: translateY(-4px); }
                90% { transform: translateY(-2px); }
            }

            @keyframes iconShake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
                20%, 40%, 60%, 80% { transform: translateX(2px); }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-icon-grid {
                    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                    gap: 12px;
                }

                .form-icon-item {
                    padding: 12px;
                }
            }

            @media (max-width: 480px) {
                .form-icon-grid {
                    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                    gap: 8px;
                }

                .form-icon-item {
                    padding: 8px;
                }

                .form-icon-button {
                    padding: 6px 12px;
                    font-size: 13px;
                }
            }
        </style>

        <!-- 基础图标展示 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">基础图标</h3>
            <div class="form-icon-grid">
                <div class="form-icon-item" onclick="copyIconCode('home')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <div class="form-icon-name">home</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('user')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <div class="form-icon-name">user</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('mail')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                        <polyline points="22,6 12,13 2,6"/>
                    </svg>
                    <div class="form-icon-name">mail</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('phone')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                    </svg>
                    <div class="form-icon-name">phone</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('search')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                    </svg>
                    <div class="form-icon-name">search</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('settings')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    <div class="form-icon-name">settings</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('heart')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                    </svg>
                    <div class="form-icon-name">heart</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('star')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                    </svg>
                    <div class="form-icon-name">star</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('check')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    <div class="form-icon-name">check</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('x')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                    <div class="form-icon-name">x</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('plus')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    <div class="form-icon-name">plus</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('minus')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    <div class="form-icon-name">minus</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('edit')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                    <div class="form-icon-name">edit</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('delete')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 7l-.867 12.142A2 2 0 0 1 16.138 21H7.862a2 2 0 0 1-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v3M4 7h16"/>
                    </svg>
                    <div class="form-icon-name">delete</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('download')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    <div class="form-icon-name">download</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('upload')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="17,8 12,3 7,8"/>
                        <line x1="12" y1="3" x2="12" y2="15"/>
                    </svg>
                    <div class="form-icon-name">upload</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('eye')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                    <div class="form-icon-name">eye</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('eye-off')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                        <line x1="1" y1="1" x2="23" y2="23"/>
                    </svg>
                    <div class="form-icon-name">eye-off</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('lock')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                        <circle cx="12" cy="16" r="1"/>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                    </svg>
                    <div class="form-icon-name">lock</div>
                </div>

                <div class="form-icon-item" onclick="copyIconCode('unlock')">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                        <circle cx="12" cy="16" r="1"/>
                        <path d="M7 11V7a5 5 0 0 1 9.9-1"/>
                    </svg>
                    <div class="form-icon-name">unlock</div>
                </div>
            </div>
        </div>

        <!-- 图标尺寸 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">图标尺寸</h3>
            <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                <svg class="form-icon-svg form-icon-xs form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span style="font-size: 12px; color: #6b7280;">xs (12px)</span>

                <svg class="form-icon-svg form-icon-sm form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span style="font-size: 12px; color: #6b7280;">sm (16px)</span>

                <svg class="form-icon-svg form-icon-md form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span style="font-size: 12px; color: #6b7280;">md (24px)</span>

                <svg class="form-icon-svg form-icon-lg form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span style="font-size: 12px; color: #6b7280;">lg (32px)</span>

                <svg class="form-icon-svg form-icon-xl form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span style="font-size: 12px; color: #6b7280;">xl (48px)</span>
            </div>
        </div>

        <!-- 图标颜色 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">图标颜色</h3>
            <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                <svg class="form-icon-svg form-icon-lg form-icon-gray" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
                <svg class="form-icon-svg form-icon-lg form-icon-blue" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
                <svg class="form-icon-svg form-icon-lg form-icon-green" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
                <svg class="form-icon-svg form-icon-lg form-icon-red" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
                <svg class="form-icon-svg form-icon-lg form-icon-yellow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
                <svg class="form-icon-svg form-icon-lg form-icon-purple" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                </svg>
            </div>
        </div>

        <!-- 图标容器 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">图标容器</h3>
            <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                <div class="form-icon-container with-bg">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                </div>

                <div class="form-icon-container with-bg blue">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                </div>

                <div class="form-icon-container with-bg green rounded">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                </div>

                <div class="form-icon-container with-bg red rounded">
                    <svg class="form-icon-svg form-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 图标按钮 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">图标按钮</h3>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <button class="form-icon-button">
                    <svg class="form-icon-svg form-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 7l-.867 12.142A2 2 0 0 1 16.138 21H7.862a2 2 0 0 1-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v3M4 7h16"/>
                    </svg>
                    删除
                </button>

                <button class="form-icon-button primary">
                    <svg class="form-icon-svg form-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                    编辑
                </button>

                <button class="form-icon-button">
                    <svg class="form-icon-svg form-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    下载
                </button>
            </div>
        </div>

        <!-- 动画图标 -->
        <div class="form-icon-section">
            <h3 class="form-icon-title">动画图标</h3>
            <div style="display: flex; align-items: center; gap: 24px; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <svg class="form-icon-svg form-icon-xl form-icon-blue form-icon-animated form-icon-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">旋转</div>
                </div>

                <div style="text-align: center;">
                    <svg class="form-icon-svg form-icon-xl form-icon-red form-icon-animated form-icon-pulse" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                    </svg>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">脉冲</div>
                </div>

                <div style="text-align: center;">
                    <svg class="form-icon-svg form-icon-xl form-icon-green form-icon-animated form-icon-bounce" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                        <polyline points="22,4 12,14.01 9,11.01"/>
                    </svg>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">弹跳</div>
                </div>

                <div style="text-align: center;">
                    <svg class="form-icon-svg form-icon-xl form-icon-yellow form-icon-animated form-icon-shake" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                        <line x1="12" y1="9" x2="12" y2="13"/>
                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                    </svg>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">摇摆</div>
                </div>
            </div>
        </div>

        <script>
            // 复制图标代码功能
            function copyIconCode(iconName) {
                const iconCodes = {
                    'home': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>',
                    'user': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>',
                    'mail': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/><polyline points="22,6 12,13 2,6"/></svg>',
                    'phone': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>',
                    'search': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>',
                    'settings': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/></svg>',
                    'heart': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/></svg>',
                    'star': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>',
                    'check': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"/></svg>',
                    'x': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>',
                    'plus': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="12" y1="5" x2="12" y2="19"/><line x1="5" y1="12" x2="19" y2="12"/></svg>',
                    'minus': '<svg class="form-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="5" y1="12" x2="19" y2="12"/></svg>'
                };

                const code = iconCodes[iconName];
                if (code) {
                    navigator.clipboard.writeText(code).then(() => {
                        // 显示复制成功提示
                        const toast = document.createElement('div');
                        toast.textContent = `已复制 ${iconName} 图标代码`;
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #10b981;
                            color: white;
                            padding: 12px 16px;
                            border-radius: 6px;
                            font-size: 14px;
                            z-index: 1000;
                            animation: slideIn 0.3s ease;
                        `;
                        document.body.appendChild(toast);

                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 2000);
                    });
                }
            }

            // 添加滑入动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础图标 --&gt;
&lt;svg class="form-icon-svg form-icon-md form-icon-blue" viewBox="0 0 24 24"&gt;
    &lt;!-- SVG路径 --&gt;
&lt;/svg&gt;

&lt;!-- 图标容器 --&gt;
&lt;div class="form-icon-container with-bg blue rounded"&gt;
    &lt;svg class="form-icon-svg"&gt;...&lt;/svg&gt;
&lt;/div&gt;

&lt;!-- 图标按钮 --&gt;
&lt;button class="form-icon-button primary"&gt;
    &lt;svg class="form-icon-svg form-icon-sm"&gt;...&lt;/svg&gt;
    按钮文字
&lt;/button&gt;

&lt;!-- 动画图标 --&gt;
&lt;svg class="form-icon-svg form-icon-animated form-icon-spin"&gt;...&lt;/svg&gt;</code></pre>
    </div>
</body>
</html>
