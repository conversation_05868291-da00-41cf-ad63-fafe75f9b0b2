<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条组件</title>
</head>
<body>
    <!-- 进度条组件 -->
    <div class="form-progress">
        <style>
            .form-progress {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-progress-group {
                margin-bottom: 24px;
            }

            .form-progress-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            .form-progress-description {
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 12px;
                line-height: 1.5;
            }

            .form-progress-container {
                position: relative;
                width: 100%;
                height: 8px;
                background-color: #f3f4f6;
                border-radius: 4px;
                overflow: hidden;
            }

            .form-progress-bar {
                height: 100%;
                background-color: #3b82f6;
                border-radius: 4px;
                transition: width 0.3s ease;
                position: relative;
            }

            .form-progress-bar::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background-image: linear-gradient(
                    45deg,
                    rgba(255, 255, 255, 0.2) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.2) 50%,
                    rgba(255, 255, 255, 0.2) 75%,
                    transparent 75%,
                    transparent
                );
                background-size: 16px 16px;
                animation: progress-stripes 1s linear infinite;
            }

            @keyframes progress-stripes {
                0% {
                    background-position: 0 0;
                }
                100% {
                    background-position: 16px 0;
                }
            }

            .form-progress-text {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 8px;
                font-size: 14px;
            }

            .form-progress-percentage {
                font-weight: 500;
                color: #374151;
            }

            .form-progress-status {
                color: #6b7280;
            }

            /* 不同尺寸 */
            .form-progress-small .form-progress-container {
                height: 4px;
            }

            .form-progress-medium .form-progress-container {
                height: 8px;
            }

            .form-progress-large .form-progress-container {
                height: 12px;
            }

            /* 不同颜色主题 */
            .form-progress-success .form-progress-bar {
                background-color: #10b981;
            }

            .form-progress-warning .form-progress-bar {
                background-color: #f59e0b;
            }

            .form-progress-error .form-progress-bar {
                background-color: #ef4444;
            }

            .form-progress-purple .form-progress-bar {
                background-color: #8b5cf6;
            }

            .form-progress-pink .form-progress-bar {
                background-color: #ec4899;
            }

            /* 渐变样式 */
            .form-progress-gradient .form-progress-bar {
                background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            }

            .form-progress-gradient-success .form-progress-bar {
                background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            }

            .form-progress-gradient-rainbow .form-progress-bar {
                background: linear-gradient(90deg, #ef4444 0%, #f59e0b 25%, #10b981 50%, #3b82f6 75%, #8b5cf6 100%);
            }

            /* 无动画样式 */
            .form-progress-static .form-progress-bar::after {
                display: none;
            }

            /* 圆形进度条 */
            .form-progress-circle {
                display: flex;
                align-items: center;
                gap: 16px;
            }

            .form-progress-circle-container {
                position: relative;
                width: 80px;
                height: 80px;
                flex-shrink: 0;
            }

            .form-progress-circle-svg {
                width: 100%;
                height: 100%;
                transform: rotate(-90deg);
            }

            .form-progress-circle-bg {
                fill: none;
                stroke: #f3f4f6;
                stroke-width: 6;
            }

            .form-progress-circle-bar {
                fill: none;
                stroke: #3b82f6;
                stroke-width: 6;
                stroke-linecap: round;
                stroke-dasharray: 226;
                stroke-dashoffset: 226;
                transition: stroke-dashoffset 0.3s ease;
            }

            .form-progress-circle-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 16px;
                font-weight: 600;
                color: #374151;
            }

            .form-progress-circle-info {
                flex: 1;
            }

            .form-progress-circle-title {
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 4px;
            }

            .form-progress-circle-desc {
                font-size: 14px;
                color: #6b7280;
            }

            /* 步骤进度条 */
            .form-progress-steps {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 16px;
            }

            .form-progress-step {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                position: relative;
            }

            .form-progress-step:not(:last-child)::after {
                content: '';
                position: absolute;
                top: 16px;
                left: 50%;
                right: -50%;
                height: 2px;
                background-color: #e5e7eb;
                z-index: 1;
            }

            .form-progress-step.completed:not(:last-child)::after {
                background-color: #10b981;
            }

            .form-progress-step-circle {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: #e5e7eb;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 500;
                color: #6b7280;
                position: relative;
                z-index: 2;
                margin-bottom: 8px;
                transition: all 0.2s ease;
            }

            .form-progress-step.completed .form-progress-step-circle {
                background-color: #10b981;
                color: white;
            }

            .form-progress-step.active .form-progress-step-circle {
                background-color: #3b82f6;
                color: white;
                transform: scale(1.1);
            }

            .form-progress-step-title {
                font-size: 12px;
                color: #6b7280;
                text-align: center;
                line-height: 1.3;
            }

            .form-progress-step.completed .form-progress-step-title,
            .form-progress-step.active .form-progress-step-title {
                color: #374151;
                font-weight: 500;
            }

            @media (max-width: 480px) {
                .form-progress {
                    margin: 12px 0;
                }

                .form-progress-circle {
                    flex-direction: column;
                    gap: 12px;
                    text-align: center;
                }

                .form-progress-circle-container {
                    width: 60px;
                    height: 60px;
                }

                .form-progress-circle-text {
                    font-size: 14px;
                }

                .form-progress-steps {
                    flex-wrap: wrap;
                    gap: 16px;
                }

                .form-progress-step {
                    flex: none;
                    width: calc(50% - 8px);
                }

                .form-progress-step:not(:last-child)::after {
                    display: none;
                }

                .form-progress-step-title {
                    font-size: 11px;
                }
            }
        </style>

        <!-- 基础进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">文件上传进度</label>
            <div class="form-progress-description">正在上传您的文件，请稍候...</div>
            <div class="form-progress-container">
                <div class="form-progress-bar" style="width: 65%"></div>
            </div>
            <div class="form-progress-text">
                <span class="form-progress-status">已上传 6.5MB / 10MB</span>
                <span class="form-progress-percentage">65%</span>
            </div>
        </div>

        <!-- 不同尺寸的进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">小尺寸进度条</label>
            <div class="form-progress-small">
                <div class="form-progress-container">
                    <div class="form-progress-bar form-progress-success" style="width: 80%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">任务完成</span>
                    <span class="form-progress-percentage">80%</span>
                </div>
            </div>
        </div>

        <div class="form-progress-group">
            <label class="form-progress-label">大尺寸进度条</label>
            <div class="form-progress-large form-progress-warning">
                <div class="form-progress-container">
                    <div class="form-progress-bar" style="width: 45%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">处理中...</span>
                    <span class="form-progress-percentage">45%</span>
                </div>
            </div>
        </div>

        <!-- 不同颜色主题 -->
        <div class="form-progress-group">
            <label class="form-progress-label">成功状态</label>
            <div class="form-progress-success form-progress-static">
                <div class="form-progress-container">
                    <div class="form-progress-bar" style="width: 100%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">✅ 完成</span>
                    <span class="form-progress-percentage">100%</span>
                </div>
            </div>
        </div>

        <div class="form-progress-group">
            <label class="form-progress-label">错误状态</label>
            <div class="form-progress-error form-progress-static">
                <div class="form-progress-container">
                    <div class="form-progress-bar" style="width: 30%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">❌ 失败</span>
                    <span class="form-progress-percentage">30%</span>
                </div>
            </div>
        </div>

        <!-- 渐变进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">渐变进度条</label>
            <div class="form-progress-gradient">
                <div class="form-progress-container">
                    <div class="form-progress-bar" style="width: 70%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">数据同步中</span>
                    <span class="form-progress-percentage">70%</span>
                </div>
            </div>
        </div>

        <div class="form-progress-group">
            <label class="form-progress-label">彩虹进度条</label>
            <div class="form-progress-gradient-rainbow">
                <div class="form-progress-container">
                    <div class="form-progress-bar" style="width: 85%"></div>
                </div>
                <div class="form-progress-text">
                    <span class="form-progress-status">创意模式</span>
                    <span class="form-progress-percentage">85%</span>
                </div>
            </div>
        </div>

        <!-- 圆形进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">圆形进度条</label>
            <div class="form-progress-circle">
                <div class="form-progress-circle-container">
                    <svg class="form-progress-circle-svg" viewBox="0 0 80 80">
                        <circle class="form-progress-circle-bg" cx="40" cy="40" r="36"></circle>
                        <circle class="form-progress-circle-bar" cx="40" cy="40" r="36" style="stroke-dashoffset: 90.4;"></circle>
                    </svg>
                    <div class="form-progress-circle-text">60%</div>
                </div>
                <div class="form-progress-circle-info">
                    <div class="form-progress-circle-title">学习进度</div>
                    <div class="form-progress-circle-desc">已完成 12 / 20 个课程</div>
                </div>
            </div>
        </div>

        <!-- 步骤进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">表单填写步骤</label>
            <div class="form-progress-steps">
                <div class="form-progress-step completed">
                    <div class="form-progress-step-circle">✓</div>
                    <div class="form-progress-step-title">基本信息</div>
                </div>
                <div class="form-progress-step completed">
                    <div class="form-progress-step-circle">✓</div>
                    <div class="form-progress-step-title">联系方式</div>
                </div>
                <div class="form-progress-step active">
                    <div class="form-progress-step-circle">3</div>
                    <div class="form-progress-step-title">偏好设置</div>
                </div>
                <div class="form-progress-step">
                    <div class="form-progress-step-circle">4</div>
                    <div class="form-progress-step-title">确认提交</div>
                </div>
            </div>
            <div class="form-progress-description">
                当前步骤：偏好设置 (3/4)
            </div>
        </div>

        <!-- 动态控制的进度条 -->
        <div class="form-progress-group">
            <label class="form-progress-label">动态进度条</label>
            <div class="form-progress-description">点击按钮控制进度</div>
            <div class="form-progress-container">
                <div id="dynamic-progress" class="form-progress-bar form-progress-purple" style="width: 0%"></div>
            </div>
            <div class="form-progress-text">
                <span id="dynamic-status" class="form-progress-status">等待开始</span>
                <span id="dynamic-percentage" class="form-progress-percentage">0%</span>
            </div>
            <div style="margin-top: 16px; display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="setProgress(25)" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer;">25%</button>
                <button onclick="setProgress(50)" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer;">50%</button>
                <button onclick="setProgress(75)" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer;">75%</button>
                <button onclick="setProgress(100)" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer;">100%</button>
                <button onclick="resetProgress()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; cursor: pointer;">重置</button>
            </div>
        </div>

        <script>
            // 动态设置进度
            function setProgress(percentage) {
                const progressBar = document.getElementById('dynamic-progress');
                const statusText = document.getElementById('dynamic-status');
                const percentageText = document.getElementById('dynamic-percentage');
                
                progressBar.style.width = percentage + '%';
                percentageText.textContent = percentage + '%';
                
                if (percentage === 0) {
                    statusText.textContent = '等待开始';
                } else if (percentage < 100) {
                    statusText.textContent = '进行中...';
                } else {
                    statusText.textContent = '✅ 完成';
                }
            }
            
            // 重置进度
            function resetProgress() {
                setProgress(0);
            }
            
            // 创建进度条
            function createProgressBar(containerId, percentage, options = {}) {
                const container = document.getElementById(containerId);
                if (!container) return;
                
                const {
                    color = '#3b82f6',
                    height = '8px',
                    animated = true,
                    showText = true,
                    label = '',
                    description = ''
                } = options;
                
                container.innerHTML = `
                    ${label ? `<label class="form-progress-label">${label}</label>` : ''}
                    ${description ? `<div class="form-progress-description">${description}</div>` : ''}
                    <div class="form-progress-container" style="height: ${height};">
                        <div class="form-progress-bar" style="width: ${percentage}%; background-color: ${color}; ${!animated ? 'animation: none;' : ''}"></div>
                    </div>
                    ${showText ? `
                        <div class="form-progress-text">
                            <span class="form-progress-status">进度</span>
                            <span class="form-progress-percentage">${percentage}%</span>
                        </div>
                    ` : ''}
                `;
            }
            
            // 更新圆形进度条
            function updateCircleProgress(percentage) {
                const circumference = 2 * Math.PI * 36; // 半径为36
                const offset = circumference - (percentage / 100) * circumference;
                
                const circleBar = document.querySelector('.form-progress-circle-bar');
                const circleText = document.querySelector('.form-progress-circle-text');
                
                if (circleBar) {
                    circleBar.style.strokeDashoffset = offset;
                }
                if (circleText) {
                    circleText.textContent = percentage + '%';
                }
            }
            
            // 更新步骤进度
            function updateStepProgress(currentStep, totalSteps) {
                const steps = document.querySelectorAll('.form-progress-step');
                
                steps.forEach((step, index) => {
                    step.classList.remove('completed', 'active');
                    const circle = step.querySelector('.form-progress-step-circle');
                    
                    if (index < currentStep - 1) {
                        step.classList.add('completed');
                        circle.textContent = '✓';
                    } else if (index === currentStep - 1) {
                        step.classList.add('active');
                        circle.textContent = index + 1;
                    } else {
                        circle.textContent = index + 1;
                    }
                });
            }
            
            // 模拟进度更新
            function simulateProgress(elementId, duration = 3000) {
                const element = document.getElementById(elementId);
                if (!element) return;
                
                let progress = 0;
                const increment = 100 / (duration / 100);
                
                const interval = setInterval(() => {
                    progress += increment;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    
                    element.style.width = progress + '%';
                    
                    // 更新文本
                    const container = element.closest('.form-progress-group');
                    const percentageElement = container.querySelector('.form-progress-percentage');
                    if (percentageElement) {
                        percentageElement.textContent = Math.round(progress) + '%';
                    }
                }, 100);
            }
            
            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', () => {
                // 可以在这里添加自动更新的逻辑
                console.log('进度条组件已初始化');
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础进度条 --&gt;
&lt;div class="form-progress-group"&gt;
    &lt;label class="form-progress-label"&gt;文件上传进度&lt;/label&gt;
    &lt;div class="form-progress-description"&gt;正在上传您的文件&lt;/div&gt;
    &lt;div class="form-progress-container"&gt;
        &lt;div class="form-progress-bar" style="width: 65%"&gt;&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="form-progress-text"&gt;
        &lt;span class="form-progress-status"&gt;已上传 6.5MB / 10MB&lt;/span&gt;
        &lt;span class="form-progress-percentage"&gt;65%&lt;/span&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 不同尺寸 --&gt;
&lt;div class="form-progress-small"&gt;&lt;!-- 小尺寸 --&gt;&lt;/div&gt;
&lt;div class="form-progress-medium"&gt;&lt;!-- 中等尺寸（默认） --&gt;&lt;/div&gt;
&lt;div class="form-progress-large"&gt;&lt;!-- 大尺寸 --&gt;&lt;/div&gt;

&lt;!-- 不同颜色 --&gt;
&lt;div class="form-progress-success"&gt;&lt;!-- 成功绿色 --&gt;&lt;/div&gt;
&lt;div class="form-progress-warning"&gt;&lt;!-- 警告黄色 --&gt;&lt;/div&gt;
&lt;div class="form-progress-error"&gt;&lt;!-- 错误红色 --&gt;&lt;/div&gt;
&lt;div class="form-progress-purple"&gt;&lt;!-- 紫色 --&gt;&lt;/div&gt;

&lt;!-- 渐变样式 --&gt;
&lt;div class="form-progress-gradient"&gt;&lt;!-- 蓝色渐变 --&gt;&lt;/div&gt;
&lt;div class="form-progress-gradient-success"&gt;&lt;!-- 绿色渐变 --&gt;&lt;/div&gt;
&lt;div class="form-progress-gradient-rainbow"&gt;&lt;!-- 彩虹渐变 --&gt;&lt;/div&gt;

&lt;!-- 无动画 --&gt;
&lt;div class="form-progress-static"&gt;&lt;!-- 无条纹动画 --&gt;&lt;/div&gt;

&lt;!-- 圆形进度条 --&gt;
&lt;div class="form-progress-circle"&gt;
    &lt;div class="form-progress-circle-container"&gt;
        &lt;svg class="form-progress-circle-svg" viewBox="0 0 80 80"&gt;
            &lt;circle class="form-progress-circle-bg" cx="40" cy="40" r="36"&gt;&lt;/circle&gt;
            &lt;circle class="form-progress-circle-bar" cx="40" cy="40" r="36" style="stroke-dashoffset: 90.4;"&gt;&lt;/circle&gt;
        &lt;/svg&gt;
        &lt;div class="form-progress-circle-text"&gt;60%&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="form-progress-circle-info"&gt;
        &lt;div class="form-progress-circle-title"&gt;学习进度&lt;/div&gt;
        &lt;div class="form-progress-circle-desc"&gt;已完成 12 / 20 个课程&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 步骤进度条 --&gt;
&lt;div class="form-progress-steps"&gt;
    &lt;div class="form-progress-step completed"&gt;
        &lt;div class="form-progress-step-circle"&gt;✓&lt;/div&gt;
        &lt;div class="form-progress-step-title"&gt;步骤一&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="form-progress-step active"&gt;
        &lt;div class="form-progress-step-circle"&gt;2&lt;/div&gt;
        &lt;div class="form-progress-step-title"&gt;步骤二&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="form-progress-step"&gt;
        &lt;div class="form-progress-step-circle"&gt;3&lt;/div&gt;
        &lt;div class="form-progress-step-title"&gt;步骤三&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>