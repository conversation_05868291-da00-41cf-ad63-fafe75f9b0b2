<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload 上传组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 上传组件基础样式 */
        .form-upload-container {
            margin-bottom: 24px;
        }

        .form-upload-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .form-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background-color: #fafafa;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .form-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .form-upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
            transform: scale(1.02);
        }

        .form-upload-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            opacity: 0.6;
        }

        .form-upload-text {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 8px;
        }

        .form-upload-hint {
            font-size: 14px;
            color: #9ca3af;
        }

        .form-upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        /* 文件列表 */
        .form-upload-list {
            margin-top: 16px;
        }

        .form-upload-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: #fff;
            transition: all 0.2s ease;
        }

        .form-upload-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .form-upload-item-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .form-upload-item-info {
            flex: 1;
            min-width: 0;
        }

        .form-upload-item-name {
            font-size: 14px;
            color: #374151;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .form-upload-item-size {
            font-size: 12px;
            color: #9ca3af;
        }

        .form-upload-item-progress {
            width: 100%;
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            margin-top: 4px;
            overflow: hidden;
        }

        .form-upload-item-progress-bar {
            height: 100%;
            background-color: #3b82f6;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .form-upload-item-actions {
            display: flex;
            gap: 8px;
            margin-left: 12px;
        }

        .form-upload-item-action {
            width: 24px;
            height: 24px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .form-upload-item-action:hover {
            background-color: #f3f4f6;
        }

        .form-upload-item-action.delete:hover {
            background-color: #fef2f2;
            color: #ef4444;
        }

        /* 图片预览 */
        .form-upload-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .form-upload-preview-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e5e7eb;
            background-color: #f9fafb;
        }

        .form-upload-preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .form-upload-preview-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .form-upload-preview-item:hover .form-upload-preview-overlay {
            opacity: 1;
        }

        .form-upload-preview-action {
            width: 32px;
            height: 32px;
            border: none;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            cursor: pointer;
            margin: 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .form-upload-preview-action:hover {
            background-color: #fff;
            transform: scale(1.1);
        }

        /* 按钮样式上传 */
        .form-upload-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .form-upload-button:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .form-upload-button:active {
            transform: translateY(0);
        }

        .form-upload-button-icon {
            width: 16px;
            height: 16px;
        }

        /* 状态样式 */
        .form-upload-item.uploading {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }

        .form-upload-item.success {
            border-color: #10b981;
            background-color: #ecfdf5;
        }

        .form-upload-item.error {
            border-color: #ef4444;
            background-color: #fef2f2;
        }

        .form-upload-item.error .form-upload-item-progress-bar {
            background-color: #ef4444;
        }

        /* 文件类型限制提示 */
        .form-upload-tips {
            margin-top: 8px;
            font-size: 12px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-upload-area {
                padding: 30px 15px;
            }
            
            .form-upload-preview {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 8px;
            }
            
            .form-upload-item {
                padding: 10px;
            }
            
            .form-upload-item-icon {
                width: 28px;
                height: 28px;
                margin-right: 10px;
            }
        }

        /* 演示样式 */
        .demo-result {
            margin-top: 16px;
            padding: 12px;
            background-color: #f8fafc;
            border-radius: 6px;
            font-size: 14px;
            color: #4b5563;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Upload 上传组件</h1>
        <p class="demo-description">文件上传组件，支持拖拽上传、文件预览、进度显示等功能</p>

        <div class="demo-section">
            <h3>拖拽上传</h3>
            <div class="form-upload-container">
                <label class="form-upload-label">上传文件</label>
                <div class="form-upload-area" id="dragUpload" 
                     ondrop="handleDrop(event, 'drag')" 
                     ondragover="handleDragOver(event)" 
                     ondragenter="handleDragEnter(event)" 
                     ondragleave="handleDragLeave(event)"
                     onclick="triggerFileInput('dragInput')">
                    <svg class="form-upload-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <div class="form-upload-text">点击或拖拽文件到此区域上传</div>
                    <div class="form-upload-hint">支持单个或批量上传，严禁上传公司数据或其他敏感信息</div>
                    <input type="file" class="form-upload-input" id="dragInput" multiple onchange="handleFileSelect(event, 'drag')">
                </div>
                <div class="form-upload-tips">支持格式：jpg, png, gif, pdf, doc, docx (最大10MB)</div>
                <div class="form-upload-list" id="dragFileList"></div>
            </div>
            <div class="demo-result" id="dragResult">已上传文件：0 个</div>
        </div>

        <div class="demo-section">
            <h3>图片上传预览</h3>
            <div class="form-upload-container">
                <label class="form-upload-label">上传图片</label>
                <div class="form-upload-area" onclick="triggerFileInput('imageInput')">
                    <svg class="form-upload-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9,12C9,13.38 7.38,14.5 5.5,14.5C3.62,14.5 2,13.38 2,12C2,10.62 3.62,9.5 5.5,9.5C7.38,9.5 9,10.62 9,12M20,12C20,13.38 18.38,14.5 16.5,14.5C14.62,14.5 13,13.38 13,12C13,10.62 14.62,9.5 16.5,9.5C18.38,9.5 20,10.62 20,12M6,20A2,2 0 0,1 4,18V6A2,2 0 0,1 6,4H18A2,2 0 0,1 20,6V18A2,2 0 0,1 18,20H6Z"/>
                    </svg>
                    <div class="form-upload-text">点击上传图片</div>
                    <div class="form-upload-hint">支持 jpg、png、gif 格式</div>
                    <input type="file" class="form-upload-input" id="imageInput" accept="image/*" multiple onchange="handleImageSelect(event)">
                </div>
                <div class="form-upload-preview" id="imagePreview"></div>
            </div>
            <div class="demo-result" id="imageResult">已上传图片：0 张</div>
        </div>

        <div class="demo-grid">
            <div>
                <h3>按钮上传</h3>
                <div class="form-upload-container">
                    <label class="form-upload-label">选择文件</label>
                    <button class="form-upload-button" onclick="triggerFileInput('buttonInput')">
                        <svg class="form-upload-button-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        选择文件
                    </button>
                    <input type="file" style="display: none;" id="buttonInput" multiple onchange="handleFileSelect(event, 'button')">
                    <div class="form-upload-list" id="buttonFileList"></div>
                </div>
                <div class="demo-result" id="buttonResult">已选择文件：0 个</div>
            </div>

            <div>
                <h3>单文件上传</h3>
                <div class="form-upload-container">
                    <label class="form-upload-label">上传头像</label>
                    <div class="form-upload-area" onclick="triggerFileInput('avatarInput')">
                        <svg class="form-upload-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z"/>
                        </svg>
                        <div class="form-upload-text">点击上传头像</div>
                        <div class="form-upload-hint">仅支持 jpg、png 格式</div>
                        <input type="file" class="form-upload-input" id="avatarInput" accept="image/jpeg,image/png" onchange="handleAvatarSelect(event)">
                    </div>
                    <div class="form-upload-list" id="avatarFileList"></div>
                </div>
                <div class="demo-result" id="avatarResult">未选择头像</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>上传状态演示</h3>
            <div class="form-upload-container">
                <label class="form-upload-label">状态演示</label>
                <div class="form-upload-list">
                    <!-- 上传中状态 -->
                    <div class="form-upload-item uploading">
                        <svg class="form-upload-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <div class="form-upload-item-info">
                            <div class="form-upload-item-name">document.pdf</div>
                            <div class="form-upload-item-size">2.5 MB</div>
                            <div class="form-upload-item-progress">
                                <div class="form-upload-item-progress-bar" style="width: 65%;"></div>
                            </div>
                        </div>
                        <div class="form-upload-item-actions">
                            <button class="form-upload-item-action delete" onclick="removeFile(this)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 成功状态 -->
                    <div class="form-upload-item success">
                        <svg class="form-upload-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9,12C9,13.38 7.38,14.5 5.5,14.5C3.62,14.5 2,13.38 2,12C2,10.62 3.62,9.5 5.5,9.5C7.38,9.5 9,10.62 9,12M20,12C20,13.38 18.38,14.5 16.5,14.5C14.62,14.5 13,13.38 13,12C13,10.62 14.62,9.5 16.5,9.5C18.38,9.5 20,10.62 20,12M6,20A2,2 0 0,1 4,18V6A2,2 0 0,1 6,4H18A2,2 0 0,1 20,6V18A2,2 0 0,1 18,20H6Z"/>
                        </svg>
                        <div class="form-upload-item-info">
                            <div class="form-upload-item-name">image.jpg</div>
                            <div class="form-upload-item-size">1.2 MB</div>
                        </div>
                        <div class="form-upload-item-actions">
                            <button class="form-upload-item-action" onclick="previewFile(this)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                                </svg>
                            </button>
                            <button class="form-upload-item-action delete" onclick="removeFile(this)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div class="form-upload-item error">
                        <svg class="form-upload-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <div class="form-upload-item-info">
                            <div class="form-upload-item-name">large-file.zip</div>
                            <div class="form-upload-item-size">15.8 MB - 文件过大</div>
                            <div class="form-upload-item-progress">
                                <div class="form-upload-item-progress-bar" style="width: 30%;"></div>
                            </div>
                        </div>
                        <div class="form-upload-item-actions">
                            <button class="form-upload-item-action" onclick="retryUpload(this)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                                </svg>
                            </button>
                            <button class="form-upload-item-action delete" onclick="removeFile(this)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 文件存储
        const fileStores = {
            drag: [],
            button: [],
            image: [],
            avatar: null
        };

        // 触发文件选择
        function triggerFileInput(inputId) {
            document.getElementById(inputId).click();
        }

        // 处理文件选择
        function handleFileSelect(event, type) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (validateFile(file, type)) {
                    addFileToStore(file, type);
                    displayFile(file, type);
                    simulateUpload(file, type);
                }
            });
            updateResult(type);
        }

        // 处理图片选择
        function handleImageSelect(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    addFileToStore(file, 'image');
                    displayImagePreview(file);
                }
            });
            updateResult('image');
        }

        // 处理头像选择
        function handleAvatarSelect(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                fileStores.avatar = file;
                displayFile(file, 'avatar');
                simulateUpload(file, 'avatar');
                updateResult('avatar');
            }
        }

        // 拖拽处理
        function handleDragOver(event) {
            event.preventDefault();
        }

        function handleDragEnter(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            if (!event.currentTarget.contains(event.relatedTarget)) {
                event.currentTarget.classList.remove('dragover');
            }
        }

        function handleDrop(event, type) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            files.forEach(file => {
                if (validateFile(file, type)) {
                    addFileToStore(file, type);
                    displayFile(file, type);
                    simulateUpload(file, type);
                }
            });
            updateResult(type);
        }

        // 文件验证
        function validateFile(file, type) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = {
                drag: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
                button: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'],
                image: ['image/jpeg', 'image/png', 'image/gif'],
                avatar: ['image/jpeg', 'image/png']
            };

            if (file.size > maxSize) {
                alert(`文件 "${file.name}" 超过最大限制 10MB`);
                return false;
            }

            if (allowedTypes[type] && !allowedTypes[type].includes(file.type)) {
                alert(`文件 "${file.name}" 格式不支持`);
                return false;
            }

            return true;
        }

        // 添加文件到存储
        function addFileToStore(file, type) {
            if (type === 'avatar') {
                fileStores.avatar = file;
            } else {
                fileStores[type].push({
                    file: file,
                    id: Date.now() + Math.random(),
                    status: 'uploading',
                    progress: 0
                });
            }
        }

        // 显示文件
        function displayFile(file, type) {
            const listId = type + 'FileList';
            const list = document.getElementById(listId);
            if (!list) return;

            if (type === 'avatar') {
                list.innerHTML = '';
            }

            const item = document.createElement('div');
            item.className = 'form-upload-item uploading';
            item.innerHTML = `
                <svg class="form-upload-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                <div class="form-upload-item-info">
                    <div class="form-upload-item-name">${file.name}</div>
                    <div class="form-upload-item-size">${formatFileSize(file.size)}</div>
                    <div class="form-upload-item-progress">
                        <div class="form-upload-item-progress-bar" style="width: 0%;"></div>
                    </div>
                </div>
                <div class="form-upload-item-actions">
                    <button class="form-upload-item-action delete" onclick="removeFile(this, '${type}')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                        </svg>
                    </button>
                </div>
            `;

            list.appendChild(item);
        }

        // 显示图片预览
        function displayImagePreview(file) {
            const preview = document.getElementById('imagePreview');
            if (!preview) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const item = document.createElement('div');
                item.className = 'form-upload-preview-item';
                item.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}" class="form-upload-preview-image">
                    <div class="form-upload-preview-overlay">
                        <button class="form-upload-preview-action" onclick="previewImage('${e.target.result}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                            </svg>
                        </button>
                        <button class="form-upload-preview-action" onclick="removeImagePreview(this)">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                            </svg>
                        </button>
                    </div>
                `;
                preview.appendChild(item);
            };
            reader.readAsDataURL(file);
        }

        // 模拟上传进度
        function simulateUpload(file, type) {
            const items = document.querySelectorAll(`#${type}FileList .form-upload-item.uploading`);
            const currentItem = items[items.length - 1];
            if (!currentItem) return;

            const progressBar = currentItem.querySelector('.form-upload-item-progress-bar');
            let progress = 0;

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    currentItem.classList.remove('uploading');
                    currentItem.classList.add('success');
                    
                    // 添加预览按钮
                    const actions = currentItem.querySelector('.form-upload-item-actions');
                    const previewBtn = document.createElement('button');
                    previewBtn.className = 'form-upload-item-action';
                    previewBtn.onclick = () => previewFile(previewBtn);
                    previewBtn.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                        </svg>
                    `;
                    actions.insertBefore(previewBtn, actions.firstChild);
                }
                progressBar.style.width = `${progress}%`;
            }, 200);
        }

        // 移除文件
        function removeFile(button, type) {
            const item = button.closest('.form-upload-item');
            const fileName = item.querySelector('.form-upload-item-name').textContent;
            
            if (type === 'avatar') {
                fileStores.avatar = null;
            } else if (fileStores[type]) {
                fileStores[type] = fileStores[type].filter(f => f.file.name !== fileName);
            }
            
            item.remove();
            updateResult(type);
        }

        // 移除图片预览
        function removeImagePreview(button) {
            const item = button.closest('.form-upload-preview-item');
            const img = item.querySelector('img');
            const fileName = img.alt;
            
            fileStores.image = fileStores.image.filter(f => f.file.name !== fileName);
            item.remove();
            updateResult('image');
        }

        // 预览文件
        function previewFile(button) {
            const fileName = button.closest('.form-upload-item').querySelector('.form-upload-item-name').textContent;
            alert(`预览文件: ${fileName}`);
        }

        // 预览图片
        function previewImage(src) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.8); display: flex; align-items: center;
                justify-content: center; z-index: 10000; cursor: pointer;
            `;
            modal.innerHTML = `<img src="${src}" style="max-width: 90%; max-height: 90%; object-fit: contain;">`;
            modal.onclick = () => document.body.removeChild(modal);
            document.body.appendChild(modal);
        }

        // 重试上传
        function retryUpload(button) {
            const item = button.closest('.form-upload-item');
            item.classList.remove('error');
            item.classList.add('uploading');
            
            const progressBar = item.querySelector('.form-upload-item-progress-bar');
            progressBar.style.width = '0%';
            
            // 重新开始上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    item.classList.remove('uploading');
                    item.classList.add('success');
                }
                progressBar.style.width = `${progress}%`;
            }, 300);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新结果显示
        function updateResult(type) {
            const resultElement = document.getElementById(`${type}Result`);
            if (!resultElement) return;

            let count = 0;
            let text = '';

            switch (type) {
                case 'avatar':
                    text = fileStores.avatar ? `已选择头像：${fileStores.avatar.name}` : '未选择头像';
                    break;
                case 'image':
                    count = fileStores.image.length;
                    text = `已上传图片：${count} 张`;
                    break;
                default:
                    count = fileStores[type] ? fileStores[type].length : 0;
                    text = `已${type === 'button' ? '选择' : '上传'}文件：${count} 个`;
            }

            resultElement.textContent = text;
        }
    </script>
</body>
</html>
