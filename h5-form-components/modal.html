<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗组件库</title>
</head>
<body>
    <!-- 弹窗组件库 -->
    <div class="form-modal">
        <style>
            .form-modal {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            /* 基础弹窗结构 */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                padding: 20px;
                box-sizing: border-box;
            }

            .modal-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .modal-container {
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                max-width: 500px;
                width: 100%;
                max-height: 90vh;
                overflow: hidden;
                transform: scale(0.9) translateY(20px);
                transition: all 0.3s ease;
                position: relative;
            }

            .modal-overlay.show .modal-container {
                transform: scale(1) translateY(0);
            }

            .modal-header {
                padding: 24px 24px 0 24px;
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
            }

            .modal-title {
                font-size: 20px;
                font-weight: 600;
                color: #1f2937;
                margin: 0;
                line-height: 1.4;
                flex: 1;
            }

            .modal-close {
                width: 32px;
                height: 32px;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #6b7280;
                transition: all 0.2s ease;
                flex-shrink: 0;
                margin-left: 16px;
            }

            .modal-close:hover {
                background-color: #f3f4f6;
                color: #374151;
            }

            .modal-body {
                padding: 16px 24px 24px 24px;
                overflow-y: auto;
                max-height: calc(90vh - 140px);
            }

            .modal-message {
                font-size: 16px;
                line-height: 1.6;
                color: #374151;
                margin: 0;
            }

            .modal-footer {
                padding: 0 24px 24px 24px;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }

            .modal-button {
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
                min-height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                text-decoration: none;
                user-select: none;
            }

            .modal-button:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .modal-button-primary {
                background-color: #3b82f6;
                color: white;
            }

            .modal-button-primary:hover {
                background-color: #2563eb;
            }

            .modal-button-secondary {
                background-color: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .modal-button-secondary:hover {
                background-color: #e5e7eb;
            }

            .modal-button-danger {
                background-color: #ef4444;
                color: white;
            }

            .modal-button-danger:hover {
                background-color: #dc2626;
            }

            .modal-button-success {
                background-color: #10b981;
                color: white;
            }

            .modal-button-success:hover {
                background-color: #059669;
            }

            .modal-button-warning {
                background-color: #f59e0b;
                color: white;
            }

            .modal-button-warning:hover {
                background-color: #d97706;
            }

            /* 警告弹框样式 */
            .modal-warning .modal-header {
                border-bottom: 3px solid #f59e0b;
            }

            .modal-warning .modal-title {
                color: #92400e;
                display: flex;
                align-items: center;
            }

            .modal-warning .modal-title::before {
                content: '⚠️';
                margin-right: 8px;
                font-size: 24px;
            }

            /* 成功弹框样式 */
            .modal-success .modal-header {
                border-bottom: 3px solid #10b981;
            }

            .modal-success .modal-title {
                color: #065f46;
                display: flex;
                align-items: center;
            }

            .modal-success .modal-title::before {
                content: '✅';
                margin-right: 8px;
                font-size: 24px;
            }

            /* 错误弹框样式 */
            .modal-error .modal-header {
                border-bottom: 3px solid #ef4444;
            }

            .modal-error .modal-title {
                color: #991b1b;
                display: flex;
                align-items: center;
            }

            .modal-error .modal-title::before {
                content: '❌';
                margin-right: 8px;
                font-size: 24px;
            }

            /* 信息弹框样式 */
            .modal-info .modal-header {
                border-bottom: 3px solid #3b82f6;
            }

            .modal-info .modal-title {
                color: #1e40af;
                display: flex;
                align-items: center;
            }

            .modal-info .modal-title::before {
                content: 'ℹ️';
                margin-right: 8px;
                font-size: 24px;
            }

            /* 确认弹框样式 */
            .modal-confirm .modal-header {
                border-bottom: 3px solid #6b7280;
            }

            .modal-confirm .modal-title {
                color: #374151;
                display: flex;
                align-items: center;
            }

            .modal-confirm .modal-title::before {
                content: '❓';
                margin-right: 8px;
                font-size: 24px;
            }

            /* 加载弹框样式 */
            .modal-loading {
                background-color: rgba(0, 0, 0, 0.7);
            }

            .modal-loading .modal-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                max-width: 200px;
                text-align: center;
                padding: 32px;
            }

            .modal-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f4f6;
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 16px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 全屏弹框样式 */
            .modal-fullscreen .modal-container {
                max-width: 100%;
                width: 100%;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
                margin: 0;
            }

            .modal-fullscreen .modal-body {
                max-height: calc(100vh - 140px);
            }

            /* 抽屉式弹框样式 */
            .modal-drawer {
                align-items: flex-end;
                justify-content: center;
            }

            .modal-drawer.modal-drawer-right {
                align-items: center;
                justify-content: flex-end;
            }

            .modal-drawer.modal-drawer-left {
                align-items: center;
                justify-content: flex-start;
            }

            .modal-drawer .modal-container {
                max-width: 100%;
                width: 100%;
                max-height: 70vh;
                border-radius: 12px 12px 0 0;
                transform: translateY(100%);
            }

            .modal-drawer.modal-drawer-right .modal-container,
            .modal-drawer.modal-drawer-left .modal-container {
                width: 320px;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
            }

            .modal-drawer.modal-drawer-right .modal-container {
                transform: translateX(100%);
            }

            .modal-drawer.modal-drawer-left .modal-container {
                transform: translateX(-100%);
            }

            .modal-drawer.show .modal-container {
                transform: translateY(0);
            }

            .modal-drawer.modal-drawer-right.show .modal-container {
                transform: translateX(0);
            }

            .modal-drawer.modal-drawer-left.show .modal-container {
                transform: translateX(0);
            }

            /* 表单样式 */
            .modal-form-group {
                margin-bottom: 16px;
            }

            .modal-form-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .modal-form-input,
            .modal-form-select,
            .modal-form-textarea {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
            }

            .modal-form-input:focus,
            .modal-form-select:focus,
            .modal-form-textarea:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .modal-form-textarea {
                resize: vertical;
                min-height: 80px;
            }

            /* 移动端适配 */
            @media (max-width: 480px) {
                .modal-overlay {
                    padding: 0;
                    align-items: flex-end;
                }

                .modal-container {
                    max-width: 100%;
                    width: 100%;
                    border-radius: 12px 12px 0 0;
                    max-height: 90vh;
                }

                .modal-fullscreen .modal-container {
                    border-radius: 0;
                    height: 100vh;
                    max-height: 100vh;
                }

                .modal-header {
                    padding: 20px 20px 0 20px;
                }

                .modal-body {
                    padding: 16px 20px 20px 20px;
                }

                .modal-footer {
                    padding: 0 20px 20px 20px;
                    flex-direction: column;
                }

                .modal-button {
                    width: 100%;
                    min-height: 44px;
                }

                .modal-drawer .modal-container,
                .modal-drawer.modal-drawer-right .modal-container,
                .modal-drawer.modal-drawer-left .modal-container {
                    width: 100%;
                    border-radius: 12px 12px 0 0;
                }

                .modal-drawer.modal-drawer-right .modal-container,
                .modal-drawer.modal-drawer-left .modal-container {
                    transform: translateY(100%);
                }

                .modal-drawer.modal-drawer-right.show .modal-container,
                .modal-drawer.modal-drawer-left.show .modal-container {
                    transform: translateY(0);
                }
            }
        </style>

        <!-- 触发按钮 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 32px;">
            <button class="modal-button modal-button-warning" onclick="showWarningModal()">显示警告弹框</button>
            <button class="modal-button modal-button-success" onclick="showSuccessModal()">显示成功弹框</button>
            <button class="modal-button modal-button-danger" onclick="showErrorModal()">显示错误弹框</button>
            <button class="modal-button modal-button-secondary" onclick="showConfirmModal()">显示确认弹框</button>
            <button class="modal-button modal-button-primary" onclick="showInfoModal()">显示信息弹框</button>
            <button class="modal-button modal-button-secondary" onclick="showFormModal()">显示表单弹框</button>
            <button class="modal-button modal-button-secondary" onclick="showLoadingModal()">显示加载弹框</button>
            <button class="modal-button modal-button-primary" onclick="showCustomModal()">显示自定义弹框</button>
            <button class="modal-button modal-button-secondary" onclick="showFullscreenModal()">显示全屏弹框</button>
            <button class="modal-button modal-button-primary" onclick="showDrawerModal()">显示抽屉弹框</button>
        </div>

        <!-- 警告弹框 -->
        <div id="warningModal" class="modal-overlay">
            <div class="modal-container modal-warning">
                <div class="modal-header">
                    <h3 class="modal-title">操作警告</h3>
                    <button class="modal-close" onclick="hideModal('warningModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您即将执行一个重要操作，此操作可能会影响您的数据。请确认您要继续执行此操作。</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('warningModal')">取消</button>
                    <button class="modal-button modal-button-warning" onclick="confirmWarning()">确认执行</button>
                </div>
            </div>
        </div>

        <!-- 成功弹框 -->
        <div id="successModal" class="modal-overlay">
            <div class="modal-container modal-success">
                <div class="modal-header">
                    <h3 class="modal-title">操作成功</h3>
                    <button class="modal-close" onclick="hideModal('successModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您的操作已成功完成！数据已保存，您可以继续使用系统。</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-success" onclick="hideModal('successModal')">确定</button>
                </div>
            </div>
        </div>

        <!-- 错误弹框 -->
        <div id="errorModal" class="modal-overlay">
            <div class="modal-container modal-error">
                <div class="modal-header">
                    <h3 class="modal-title">操作失败</h3>
                    <button class="modal-close" onclick="hideModal('errorModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">操作执行失败，可能是网络连接问题或服务器暂时不可用。</p>
                    <div style="margin-top: 12px; padding: 12px; background-color: #fef2f2; border-radius: 6px; font-size: 14px; color: #991b1b;">
                        <strong>错误详情：</strong> 连接超时 (错误代码: 500)
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('errorModal')">关闭</button>
                    <button class="modal-button modal-button-danger" onclick="retryOperation()">重试</button>
                </div>
            </div>
        </div>

        <!-- 确认弹框 -->
        <div id="confirmModal" class="modal-overlay">
            <div class="modal-container modal-confirm">
                <div class="modal-header">
                    <h3 class="modal-title">确认删除</h3>
                    <button class="modal-close" onclick="hideModal('confirmModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您确定要删除这个项目吗？此操作不可撤销，删除后所有相关数据将永久丢失。</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('confirmModal')">取消</button>
                    <button class="modal-button modal-button-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>

        <!-- 信息弹框 -->
        <div id="infoModal" class="modal-overlay">
            <div class="modal-container modal-info">
                <div class="modal-header">
                    <h3 class="modal-title">系统通知</h3>
                    <button class="modal-close" onclick="hideModal('infoModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">系统将在今晚 23:00 - 01:00 进行维护升级，期间可能会影响部分功能的使用。我们会尽快完成维护，感谢您的理解。</p>
                    <div style="margin-top: 16px; padding: 12px; background-color: #eff6ff; border-radius: 6px; font-size: 14px; color: #1e40af;">
                        <strong>提示：</strong> 建议您提前保存重要数据，避免数据丢失。
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-primary" onclick="hideModal('infoModal')">我知道了</button>
                </div>
            </div>
        </div>

        <!-- 表单弹框 -->
        <div id="formModal" class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title">添加用户</h3>
                    <button class="modal-close" onclick="hideModal('formModal')">×</button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="modal-form-group">
                            <label class="modal-form-label" for="userName">用户名 *</label>
                            <input type="text" id="userName" name="userName" class="modal-form-input" placeholder="请输入用户名" required>
                        </div>
                        <div class="modal-form-group">
                            <label class="modal-form-label" for="userEmail">邮箱地址 *</label>
                            <input type="email" id="userEmail" name="userEmail" class="modal-form-input" placeholder="请输入邮箱地址" required>
                        </div>
                        <div class="modal-form-group">
                            <label class="modal-form-label" for="userRole">用户角色</label>
                            <select id="userRole" name="userRole" class="modal-form-select">
                                <option value="">请选择角色</option>
                                <option value="admin">管理员</option>
                                <option value="user">普通用户</option>
                                <option value="guest">访客</option>
                            </select>
                        </div>
                        <div class="modal-form-group">
                            <label class="modal-form-label" for="userBio">个人简介</label>
                            <textarea id="userBio" name="userBio" class="modal-form-textarea" placeholder="请输入个人简介"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('formModal')">取消</button>
                    <button class="modal-button modal-button-primary" onclick="submitForm()">保存</button>
                </div>
            </div>
        </div>

        <!-- 加载弹框 -->
        <div id="loadingModal" class="modal-overlay modal-loading">
            <div class="modal-container">
                <div class="modal-spinner"></div>
                <p style="margin: 0; color: #374151; font-size: 16px;">正在处理中...</p>
            </div>
        </div>

        <!-- 自定义内容弹框 -->
        <div id="customModal" class="modal-overlay">
            <div class="modal-container" style="max-width: 600px;">
                <div class="modal-header">
                    <h3 class="modal-title">产品详情</h3>
                    <button class="modal-close" onclick="hideModal('customModal')">×</button>
                </div>
                <div class="modal-body">
                    <div style="display: grid; grid-template-columns: 200px 1fr; gap: 20px; align-items: start;">
                        <div style="background-color: #f3f4f6; border-radius: 8px; height: 150px; display: flex; align-items: center; justify-content: center; color: #6b7280;">
                            产品图片
                        </div>
                        <div>
                            <h4 style="margin: 0 0 8px 0; color: #1f2937;">智能手机 Pro Max</h4>
                            <p style="margin: 0 0 12px 0; color: #6b7280; font-size: 14px;">型号: SPM-2024</p>
                            <div style="margin-bottom: 16px;">
                                <span style="font-size: 24px; font-weight: 600; color: #ef4444;">¥8,999</span>
                                <span style="font-size: 16px; color: #6b7280; text-decoration: line-through; margin-left: 8px;">¥9,999</span>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                    <span style="padding: 4px 8px; background-color: #dbeafe; color: #1e40af; border-radius: 4px; font-size: 12px;">6.7英寸</span>
                                    <span style="padding: 4px 8px; background-color: #dcfce7; color: #166534; border-radius: 4px; font-size: 12px;">256GB</span>
                                    <span style="padding: 4px 8px; background-color: #fef3c7; color: #92400e; border-radius: 4px; font-size: 12px;">5G</span>
                                </div>
                            </div>
                            <p style="margin: 0; color: #374151; font-size: 14px; line-height: 1.5;">
                                全新设计的智能手机，搭载最新处理器，支持5G网络，拍照效果出色，电池续航能力强。
                            </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('customModal')">关闭</button>
                    <button class="modal-button modal-button-primary" onclick="addToCart()">加入购物车</button>
                </div>
            </div>
        </div>

        <!-- 全屏弹框 -->
        <div id="fullscreenModal" class="modal-overlay modal-fullscreen">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title">用户注册</h3>
                    <button class="modal-close" onclick="hideModal('fullscreenModal')">×</button>
                </div>
                <div class="modal-body">
                    <div style="max-width: 500px; margin: 0 auto;">
                        <div style="text-align: center; margin-bottom: 32px;">
                            <h2 style="color: #1f2937; margin-bottom: 8px;">欢迎加入我们</h2>
                            <p style="color: #6b7280; margin: 0;">请填写以下信息完成注册</p>
                        </div>
                        
                        <form>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                                <div class="modal-form-group">
                                    <label class="modal-form-label">姓</label>
                                    <input type="text" class="modal-form-input" placeholder="请输入姓氏">
                                </div>
                                <div class="modal-form-group">
                                    <label class="modal-form-label">名</label>
                                    <input type="text" class="modal-form-input" placeholder="请输入名字">
                                </div>
                            </div>
                            
                            <div class="modal-form-group">
                                <label class="modal-form-label">邮箱地址</label>
                                <input type="email" class="modal-form-input" placeholder="请输入邮箱地址">
                            </div>
                            
                            <div class="modal-form-group">
                                <label class="modal-form-label">手机号码</label>
                                <input type="tel" class="modal-form-input" placeholder="请输入手机号码">
                            </div>
                            
                            <div class="modal-form-group">
                                <label class="modal-form-label">密码</label>
                                <input type="password" class="modal-form-input" placeholder="请输入密码">
                            </div>
                            
                            <div class="modal-form-group">
                                <label class="modal-form-label">确认密码</label>
                                <input type="password" class="modal-form-input" placeholder="请再次输入密码">
                            </div>
                            
                            <div style="margin: 24px 0;">
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" style="margin-right: 8px;">
                                    <span style="font-size: 14px; color: #374151;">我已阅读并同意 <a href="#" style="color: #3b82f6;">用户协议</a> 和 <a href="#" style="color: #3b82f6;">隐私政策</a></span>
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('fullscreenModal')">取消</button>
                    <button class="modal-button modal-button-primary" onclick="registerUser()">注册账户</button>
                </div>
            </div>
        </div>

        <!-- 抽屉式弹框 -->
        <div id="drawerModal" class="modal-overlay modal-drawer">
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title">设置面板</h3>
                    <button class="modal-close" onclick="hideModal('drawerModal')">×</button>
                </div>
                <div class="modal-body">
                    <div style="margin-bottom: 24px;">
                        <h4 style="margin: 0 0 12px 0; color: #1f2937; font-size: 16px;">外观设置</h4>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                                <span>深色模式</span>
                                <input type="checkbox" style="transform: scale(1.2);">
                            </label>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label class="modal-form-label">主题色彩</label>
                            <select class="modal-form-select">
                                <option>蓝色</option>
                                <option>绿色</option>
                                <option>紫色</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 24px;">
                        <h4 style="margin: 0 0 12px 0; color: #1f2937; font-size: 16px;">通知设置</h4>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                                <span>邮件通知</span>
                                <input type="checkbox" checked style="transform: scale(1.2);">
                            </label>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                                <span>推送通知</span>
                                <input type="checkbox" style="transform: scale(1.2);">
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="margin: 0 0 12px 0; color: #1f2937; font-size: 16px;">账户设置</h4>
                        <div style="margin-bottom: 16px;">
                            <button class="modal-button modal-button-secondary" style="width: 100%; margin-bottom: 8px;">修改密码</button>
                            <button class="modal-button modal-button-secondary" style="width: 100%; margin-bottom: 8px;">导出数据</button>
                            <button class="modal-button modal-button-danger" style="width: 100%;">删除账户</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 显示弹窗
            function showModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    
                    // 添加ESC键关闭功能
                    document.addEventListener('keydown', handleEscKey);
                    
                    // 点击遮罩关闭
                    modal.addEventListener('click', handleOverlayClick);
                }
            }

            // 隐藏弹窗
            function hideModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                    
                    // 移除事件监听
                    document.removeEventListener('keydown', handleEscKey);
                    modal.removeEventListener('click', handleOverlayClick);
                }
            }

            // ESC键关闭
            function handleEscKey(e) {
                if (e.key === 'Escape') {
                    const openModal = document.querySelector('.modal-overlay.show');
                    if (openModal) {
                        hideModal(openModal.id);
                    }
                }
            }

            // 点击遮罩关闭
            function handleOverlayClick(e) {
                if (e.target === e.currentTarget) {
                    hideModal(e.currentTarget.id);
                }
            }

            // 具体弹窗显示函数
            function showWarningModal() {
                showModal('warningModal');
            }

            function showSuccessModal() {
                showModal('successModal');
                // 3秒后自动关闭
                setTimeout(() => {
                    hideModal('successModal');
                }, 3000);
            }

            function showErrorModal() {
                showModal('errorModal');
            }

            function showConfirmModal() {
                showModal('confirmModal');
            }

            function showInfoModal() {
                showModal('infoModal');
            }

            function showFormModal() {
                showModal('formModal');
            }

            function showLoadingModal() {
                showModal('loadingModal');
                // 3秒后自动关闭
                setTimeout(() => {
                    hideModal('loadingModal');
                }, 3000);
            }

            function showCustomModal() {
                showModal('customModal');
            }

            function showFullscreenModal() {
                showModal('fullscreenModal');
            }

            function showDrawerModal() {
                showModal('drawerModal');
            }

            // 业务逻辑函数
            function confirmWarning() {
                alert('警告操作已确认');
                hideModal('warningModal');
            }

            function retryOperation() {
                alert('正在重试操作...');
                hideModal('errorModal');
            }

            function confirmDelete() {
                alert('项目已删除');
                hideModal('confirmModal');
            }

            function submitForm() {
                const form = document.getElementById('userForm');
                const formData = new FormData(form);
                
                // 简单验证
                if (!formData.get('userName') || !formData.get('userEmail')) {
                    alert('请填写必填字段');
                    return;
                }
                
                alert('用户信息已保存');
                hideModal('formModal');
                form.reset();
            }

            function addToCart() {
                alert('商品已加入购物车');
                hideModal('customModal');
            }

            function registerUser() {
                alert('注册成功！');
                hideModal('fullscreenModal');
            }

            // 通用弹窗API
            window.Modal = {
                // 显示警告弹窗
                warning: function(title, message, onConfirm, onCancel) {
                    return this.show({
                        type: 'warning',
                        title: title,
                        message: message,
                        buttons: [
                            { text: '取消', type: 'secondary', onClick: onCancel },
                            { text: '确认', type: 'warning', onClick: onConfirm }
                        ]
                    });
                },
                
                // 显示成功弹窗
                success: function(message, autoClose = true) {
                    return this.show({
                        type: 'success',
                        title: '操作成功',
                        message: message,
                        autoClose: autoClose ? 3000 : false,
                        buttons: [
                            { text: '确定', type: 'success' }
                        ]
                    });
                },
                
                // 显示错误弹窗
                error: function(title, message, onRetry, onClose) {
                    return this.show({
                        type: 'error',
                        title: title,
                        message: message,
                        buttons: [
                            { text: '关闭', type: 'secondary', onClick: onClose },
                            { text: '重试', type: 'danger', onClick: onRetry }
                        ]
                    });
                },
                
                // 显示确认弹窗
                confirm: function(title, message, onConfirm, onCancel) {
                    return this.show({
                        type: 'confirm',
                        title: title,
                        message: message,
                        buttons: [
                            { text: '取消', type: 'secondary', onClick: onCancel },
                            { text: '确认', type: 'primary', onClick: onConfirm }
                        ]
                    });
                },
                
                // 显示信息弹窗
                info: function(title, message) {
                    return this.show({
                        type: 'info',
                        title: title,
                        message: message,
                        buttons: [
                            { text: '我知道了', type: 'primary' }
                        ]
                    });
                },
                
                // 显示加载弹窗
                loading: function(message = '正在处理中...') {
                    return this.show({
                        type: 'loading',
                        message: message,
                        closable: false
                    });
                },
                
                // 通用显示方法
                show: function(options) {
                    // 这里可以实现动态创建弹窗的逻辑
                    console.log('Modal.show called with options:', options);
                    return {
                        close: function() {
                            console.log('Modal closed');
                        }
                    };
                }
            };
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础弹窗结构 --&gt;
&lt;div id="myModal" class="modal-overlay"&gt;
    &lt;div class="modal-container"&gt;
        &lt;div class="modal-header"&gt;
            &lt;h3 class="modal-title"&gt;弹窗标题&lt;/h3&gt;
            &lt;button class="modal-close" onclick="hideModal('myModal')"&gt;×&lt;/button&gt;
        &lt;/div&gt;
        &lt;div class="modal-body"&gt;
            &lt;p class="modal-message"&gt;弹窗内容&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class="modal-footer"&gt;
            &lt;button class="modal-button modal-button-secondary"&gt;取消&lt;/button&gt;
            &lt;button class="modal-button modal-button-primary"&gt;确定&lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- JavaScript 调用 --&gt;
&lt;script&gt;
// 显示弹窗
function showModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

// 隐藏弹窗
function hideModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// 使用API
Modal.warning('警告', '确定要删除吗？', 
    () =&gt; console.log('确认'), 
    () =&gt; console.log('取消')
);
&lt;/script&gt;</code></pre>
    </div>
</body>
</html>