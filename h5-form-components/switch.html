<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开关组件</title>
</head>
<body>
    <!-- 开关组件 -->
    <div class="form-switch">
        <style>
            .form-switch {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-switch-section {
                margin-bottom: 40px;
            }

            .form-switch-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础开关样式 */
            .form-switch-group {
                margin-bottom: 20px;
            }

            .form-switch-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-switch-container {
                position: relative;
                display: inline-block;
                width: 52px;
                height: 28px;
            }

            .form-switch-input {
                opacity: 0;
                width: 0;
                height: 0;
                position: absolute;
            }

            .form-switch-slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #d1d5db;
                transition: all 0.3s ease;
                border-radius: 28px;
                border: 2px solid transparent;
            }

            .form-switch-slider:before {
                position: absolute;
                content: "";
                height: 20px;
                width: 20px;
                left: 2px;
                bottom: 2px;
                background-color: white;
                transition: all 0.3s ease;
                border-radius: 50%;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .form-switch-input:checked + .form-switch-slider {
                background-color: #3b82f6;
            }

            .form-switch-input:checked + .form-switch-slider:before {
                transform: translateX(24px);
            }

            .form-switch-input:focus + .form-switch-slider {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            /* 不同尺寸 */
            .form-switch-container.small {
                width: 40px;
                height: 22px;
            }

            .form-switch-container.small .form-switch-slider {
                border-radius: 22px;
            }

            .form-switch-container.small .form-switch-slider:before {
                height: 16px;
                width: 16px;
                left: 1px;
                bottom: 1px;
            }

            .form-switch-container.small .form-switch-input:checked + .form-switch-slider:before {
                transform: translateX(18px);
            }

            .form-switch-container.large {
                width: 64px;
                height: 34px;
            }

            .form-switch-container.large .form-switch-slider {
                border-radius: 34px;
            }

            .form-switch-container.large .form-switch-slider:before {
                height: 26px;
                width: 26px;
                left: 2px;
                bottom: 2px;
            }

            .form-switch-container.large .form-switch-input:checked + .form-switch-slider:before {
                transform: translateX(30px);
            }

            /* 颜色主题 */
            .form-switch-input:checked + .form-switch-slider.primary {
                background-color: #3b82f6;
            }

            .form-switch-input:checked + .form-switch-slider.success {
                background-color: #10b981;
            }

            .form-switch-input:checked + .form-switch-slider.warning {
                background-color: #f59e0b;
            }

            .form-switch-input:checked + .form-switch-slider.danger {
                background-color: #ef4444;
            }

            .form-switch-input:checked + .form-switch-slider.purple {
                background-color: #8b5cf6;
            }

            /* 禁用状态 */
            .form-switch-container.disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            .form-switch-container.disabled .form-switch-slider {
                cursor: not-allowed;
            }

            /* 带文字的开关 */
            .form-switch-with-text {
                display: inline-flex;
                align-items: center;
                gap: 12px;
            }

            .form-switch-text {
                font-size: 14px;
                color: #6b7280;
                transition: color 0.3s ease;
            }

            .form-switch-input:checked ~ .form-switch-text.on {
                color: #3b82f6;
                font-weight: 500;
            }

            .form-switch-input:not(:checked) ~ .form-switch-text.off {
                color: #374151;
                font-weight: 500;
            }

            /* 内嵌文字开关 */
            .form-switch-slider.with-inner-text {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 8px;
                font-size: 12px;
                font-weight: 500;
                color: white;
                min-width: 60px;
                width: auto;
            }

            .form-switch-container.with-inner-text {
                width: auto;
                min-width: 60px;
            }

            .form-switch-slider.with-inner-text:before {
                position: relative;
                left: 0;
                bottom: 0;
                transform: none;
                margin-right: auto;
                z-index: 1;
            }

            .form-switch-input:checked + .form-switch-slider.with-inner-text:before {
                margin-right: 0;
                margin-left: auto;
            }

            .form-switch-inner-text {
                position: absolute;
                left: 8px;
                right: 8px;
                display: flex;
                justify-content: space-between;
                pointer-events: none;
                z-index: 0;
            }

            .form-switch-inner-on,
            .form-switch-inner-off {
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .form-switch-input:checked ~ .form-switch-inner-text .form-switch-inner-on {
                opacity: 1;
            }

            .form-switch-input:not(:checked) ~ .form-switch-inner-text .form-switch-inner-off {
                opacity: 1;
            }

            /* 加载状态 */
            .form-switch-loading .form-switch-slider:before {
                background: conic-gradient(from 0deg, #3b82f6, #93c5fd, #3b82f6);
                animation: switchSpin 1s linear infinite;
            }

            @keyframes switchSpin {
                to {
                    transform: rotate(360deg);
                }
            }

            .form-switch-input:checked + .form-switch-slider.loading:before {
                transform: translateX(24px) rotate(360deg);
                animation: switchSpin 1s linear infinite;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-switch-container {
                    width: 56px;
                    height: 32px;
                }

                .form-switch-slider:before {
                    height: 24px;
                    width: 24px;
                }

                .form-switch-input:checked + .form-switch-slider:before {
                    transform: translateX(24px);
                }

                .form-switch-with-text {
                    gap: 8px;
                }
            }

            @media (max-width: 480px) {
                .form-switch-text {
                    font-size: 13px;
                }
            }

            /* 演示样式 */
            .demo-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }

            .demo-row {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 16px;
                flex-wrap: wrap;
            }

            .demo-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        </style>

        <!-- 基础开关 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">基础开关</h3>
            <div class="demo-grid">
                <div class="form-switch-group">
                    <label class="form-switch-label">开启通知</label>
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch1" checked>
                        <label for="switch1" class="form-switch-slider"></label>
                    </div>
                </div>

                <div class="form-switch-group">
                    <label class="form-switch-label">自动保存</label>
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch2">
                        <label for="switch2" class="form-switch-slider"></label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 不同尺寸 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">不同尺寸</h3>
            <div class="demo-row">
                <div class="demo-item">
                    <div class="form-switch-container small">
                        <input type="checkbox" class="form-switch-input" id="switch-small" checked>
                        <label for="switch-small" class="form-switch-slider"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">小尺寸</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-default" checked>
                        <label for="switch-default" class="form-switch-slider"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">默认尺寸</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container large">
                        <input type="checkbox" class="form-switch-input" id="switch-large" checked>
                        <label for="switch-large" class="form-switch-slider"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">大尺寸</span>
                </div>
            </div>
        </div>

        <!-- 不同颜色 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">不同颜色</h3>
            <div class="demo-row">
                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-primary" checked>
                        <label for="switch-primary" class="form-switch-slider primary"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">主要色</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-success" checked>
                        <label for="switch-success" class="form-switch-slider success"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">成功色</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-warning" checked>
                        <label for="switch-warning" class="form-switch-slider warning"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">警告色</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-danger" checked>
                        <label for="switch-danger" class="form-switch-slider danger"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">危险色</span>
                </div>

                <div class="demo-item">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-purple" checked>
                        <label for="switch-purple" class="form-switch-slider purple"></label>
                    </div>
                    <span style="font-size: 12px; color: #6b7280;">紫色</span>
                </div>
            </div>
        </div>

        <!-- 带文字的开关 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">带文字的开关</h3>
            <div class="demo-grid">
                <div class="form-switch-with-text">
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-text1" checked>
                        <label for="switch-text1" class="form-switch-slider success"></label>
                        <span class="form-switch-text on">开启</span>
                        <span class="form-switch-text off">关闭</span>
                    </div>
                </div>

                <div class="form-switch-with-text">
                    <span style="font-size: 14px; color: #374151;">夜间模式</span>
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-text2">
                        <label for="switch-text2" class="form-switch-slider"></label>
                    </div>
                </div>

                <div class="form-switch-with-text">
                    <span style="font-size: 14px; color: #374151;">接收推送</span>
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-text3" checked>
                        <label for="switch-text3" class="form-switch-slider success"></label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内嵌文字开关 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">内嵌文字开关</h3>
            <div class="demo-row">
                <div class="form-switch-container with-inner-text">
                    <input type="checkbox" class="form-switch-input" id="switch-inner1" checked>
                    <label for="switch-inner1" class="form-switch-slider with-inner-text success"></label>
                    <div class="form-switch-inner-text">
                        <span class="form-switch-inner-on">ON</span>
                        <span class="form-switch-inner-off">OFF</span>
                    </div>
                </div>

                <div class="form-switch-container with-inner-text">
                    <input type="checkbox" class="form-switch-input" id="switch-inner2">
                    <label for="switch-inner2" class="form-switch-slider with-inner-text primary"></label>
                    <div class="form-switch-inner-text">
                        <span class="form-switch-inner-on">是</span>
                        <span class="form-switch-inner-off">否</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 不同状态 -->
        <div class="form-switch-section">
            <h3 class="form-switch-title">不同状态</h3>
            <div class="demo-grid">
                <div class="form-switch-group">
                    <label class="form-switch-label">正常状态</label>
                    <div class="form-switch-container">
                        <input type="checkbox" class="form-switch-input" id="switch-normal" checked>
                        <label for="switch-normal" class="form-switch-slider"></label>
                    </div>
                </div>

                <div class="form-switch-group">
                    <label class="form-switch-label">禁用状态</label>
                    <div class="form-switch-container disabled">
                        <input type="checkbox" class="form-switch-input" id="switch-disabled" disabled checked>
                        <label for="switch-disabled" class="form-switch-slider"></label>
                    </div>
                </div>

                <div class="form-switch-group">
                    <label class="form-switch-label">加载状态</label>
                    <div class="form-switch-container loading">
                        <input type="checkbox" class="form-switch-input" id="switch-loading" checked>
                        <label for="switch-loading" class="form-switch-slider loading"></label>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 开关状态变化处理
            document.addEventListener('DOMContentLoaded', function() {
                const switches = document.querySelectorAll('.form-switch-input');
                
                switches.forEach(switchInput => {
                    switchInput.addEventListener('change', function() {
                        const switchId = this.id;
                        const isChecked = this.checked;
                        
                        // 显示状态变化提示
                        const toast = document.createElement('div');
                        toast.textContent = `${switchId}: ${isChecked ? '开启' : '关闭'}`;
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: ${isChecked ? '#10b981' : '#6b7280'};
                            color: white;
                            padding: 12px 16px;
                            border-radius: 6px;
                            font-size: 14px;
                            z-index: 1000;
                            animation: slideIn 0.3s ease;
                        `;
                        document.body.appendChild(toast);

                        setTimeout(() => {
                            if (document.body.contains(toast)) {
                                document.body.removeChild(toast);
                            }
                        }, 2000);

                        // 特殊处理：夜间模式切换
                        if (switchId === 'switch-text2') {
                            if (isChecked) {
                                document.body.style.background = '#1f2937';
                                document.body.style.color = '#f9fafb';
                            } else {
                                document.body.style.background = '';
                                document.body.style.color = '';
                            }
                        }
                    });

                    // 键盘支持
                    switchInput.addEventListener('keydown', function(e) {
                        if (e.key === ' ' || e.key === 'Enter') {
                            e.preventDefault();
                            this.checked = !this.checked;
                            this.dispatchEvent(new Event('change'));
                        }
                    });
                });

                // 模拟加载状态
                const loadingSwitch = document.getElementById('switch-loading');
                if (loadingSwitch) {
                    const container = loadingSwitch.closest('.form-switch-container');
                    setTimeout(() => {
                        container.classList.remove('loading');
                    }, 3000);
                }
            });

            // 添加滑入动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础开关 --&gt;
&lt;div class="form-switch-container"&gt;
    &lt;input type="checkbox" class="form-switch-input" id="switch1"&gt;
    &lt;label for="switch1" class="form-switch-slider"&gt;&lt;/label&gt;
&lt;/div&gt;

&lt;!-- 不同颜色 --&gt;
&lt;div class="form-switch-container"&gt;
    &lt;input type="checkbox" class="form-switch-input" id="switch2" checked&gt;
    &lt;label for="switch2" class="form-switch-slider success"&gt;&lt;/label&gt;
&lt;/div&gt;

&lt;!-- 带文字开关 --&gt;
&lt;div class="form-switch-with-text"&gt;
    &lt;span&gt;夜间模式&lt;/span&gt;
    &lt;div class="form-switch-container"&gt;
        &lt;input type="checkbox" class="form-switch-input" id="switch3"&gt;
        &lt;label for="switch3" class="form-switch-slider"&gt;&lt;/label&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 内嵌文字开关 --&gt;
&lt;div class="form-switch-container with-inner-text"&gt;
    &lt;input type="checkbox" class="form-switch-input" id="switch4"&gt;
    &lt;label for="switch4" class="form-switch-slider with-inner-text"&gt;&lt;/label&gt;
    &lt;div class="form-switch-inner-text"&gt;
        &lt;span class="form-switch-inner-on"&gt;ON&lt;/span&gt;
        &lt;span class="form-switch-inner-off"&gt;OFF&lt;/span&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
