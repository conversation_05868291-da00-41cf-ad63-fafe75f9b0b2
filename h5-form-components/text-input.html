<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本输入框组件</title>
</head>
<body>
    <!-- 文本输入框组件 -->
    <div class="form-text-input">
        <style>
            .form-text-input {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-input-group {
                margin-bottom: 20px;
            }

            .form-input-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-input-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-input {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                min-height: 44px;
                box-sizing: border-box;
            }

            .form-input::placeholder {
                color: #9ca3af;
            }

            .form-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-input:hover:not(:focus) {
                border-color: #9ca3af;
            }

            /* 错误状态 */
            .form-input.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            .form-input.error:focus {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 成功状态 */
            .form-input.success {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-input.success:focus {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-input-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-input-message.error {
                color: #ef4444;
            }

            .form-input-message.success {
                color: #10b981;
            }

            .form-input-message.help {
                color: #6b7280;
            }

            @media (max-width: 480px) {
                .form-text-input {
                    margin: 12px 0;
                }

                .form-input {
                    padding: 14px 16px;
                    font-size: 16px;
                    min-height: 48px;
                }

                .form-input-label {
                    font-size: 16px;
                }
            }
        </style>

        <!-- 普通输入框 -->
        <div class="form-input-group">
            <label class="form-input-label" for="username">
                用户名 <span class="form-input-required">*</span>
            </label>
            <input 
                type="text" 
                id="username" 
                name="username" 
                class="form-input" 
                placeholder="请输入您的用户名"
                required
            >
            <div class="form-input-message help">用户名长度应为3-20个字符</div>
        </div>

        <!-- 错误状态输入框 -->
        <div class="form-input-group">
            <label class="form-input-label" for="email-error">
                邮箱地址 <span class="form-input-required">*</span>
            </label>
            <input 
                type="email" 
                id="email-error" 
                name="email" 
                class="form-input error" 
                placeholder="请输入您的邮箱地址"
                value="invalid-email"
            >
            <div class="form-input-message error">请输入有效的邮箱地址</div>
        </div>

        <!-- 成功状态输入框 -->
        <div class="form-input-group">
            <label class="form-input-label" for="phone">
                手机号码
            </label>
            <input 
                type="tel" 
                id="phone" 
                name="phone" 
                class="form-input success" 
                placeholder="请输入您的手机号码"
                value="13800138000"
            >
            <div class="form-input-message success">手机号码格式正确</div>
        </div>

        <!-- 密码输入框 -->
        <div class="form-input-group">
            <label class="form-input-label" for="password">
                密码 <span class="form-input-required">*</span>
            </label>
            <input 
                type="password" 
                id="password" 
                name="password" 
                class="form-input" 
                placeholder="请输入密码"
                required
            >
            <div class="form-input-message help">密码长度至少8位，包含字母和数字</div>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-input-group"&gt;
    &lt;label class="form-input-label" for="username"&gt;
        用户名 &lt;span class="form-input-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;input 
        type="text" 
        id="username" 
        name="username" 
        class="form-input" 
        placeholder="请输入您的用户名"
        required
    &gt;
    &lt;div class="form-input-message help"&gt;用户名长度应为3-20个字符&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 错误状态 --&gt;
&lt;input class="form-input error" ... &gt;
&lt;div class="form-input-message error"&gt;错误信息&lt;/div&gt;

&lt;!-- 成功状态 --&gt;
&lt;input class="form-input success" ... &gt;
&lt;div class="form-input-message success"&gt;成功信息&lt;/div&gt;</code></pre>
    </div>
</body>
</html>