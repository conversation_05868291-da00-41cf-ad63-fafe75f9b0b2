<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer 穿梭框组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 穿梭框基础样式 */
        .form-transfer-container {
            margin-bottom: 24px;
        }

        .form-transfer-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .form-transfer-wrapper {
            display: flex;
            align-items: center;
            gap: 16px;
            min-height: 300px;
        }

        .form-transfer-panel {
            flex: 1;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            height: 300px;
        }

        .form-transfer-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            background-color: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .form-transfer-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .form-transfer-count {
            font-size: 12px;
            color: #6b7280;
        }

        .form-transfer-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .form-transfer-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
        }

        .form-transfer-search input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .form-transfer-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;
        }

        .form-transfer-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }

        .form-transfer-item:hover {
            background-color: #f8fafc;
        }

        .form-transfer-item.selected {
            background-color: #eff6ff;
            color: #3b82f6;
        }

        .form-transfer-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .form-transfer-checkbox {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .form-transfer-item-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-transfer-item-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .form-transfer-item-text {
            font-size: 14px;
        }

        .form-transfer-item-desc {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }

        /* 操作按钮区域 */
        .form-transfer-operations {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-self: center;
        }

        .form-transfer-button {
            width: 32px;
            height: 32px;
            border: 1px solid #d1d5db;
            background-color: #fff;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .form-transfer-button:hover:not(:disabled) {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .form-transfer-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .form-transfer-button-icon {
            width: 16px;
            height: 16px;
        }

        /* 全选操作 */
        .form-transfer-select-all {
            padding: 8px 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            background-color: #f9fafb;
        }

        .form-transfer-select-all input {
            margin-right: 8px;
        }

        .form-transfer-select-all label {
            font-size: 12px;
            color: #6b7280;
            cursor: pointer;
        }

        /* 空状态 */
        .form-transfer-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100px;
            color: #9ca3af;
            font-size: 14px;
        }

        .form-transfer-empty-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-transfer-wrapper {
                flex-direction: column;
                gap: 12px;
            }
            
            .form-transfer-operations {
                flex-direction: row;
                justify-content: center;
                order: 1;
            }
            
            .form-transfer-panel {
                height: 250px;
            }
        }

        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-transfer-panel {
                height: 200px;
            }
            
            .form-transfer-item {
                padding: 6px 12px;
            }
        }

        /* 演示样式 */
        .demo-result {
            margin-top: 16px;
            padding: 12px;
            background-color: #f8fafc;
            border-radius: 6px;
            font-size: 14px;
            color: #4b5563;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
            margin-top: 20px;
        }

        /* 自定义样式变体 */
        .form-transfer-compact .form-transfer-panel {
            height: 200px;
        }

        .form-transfer-compact .form-transfer-item {
            padding: 6px 12px;
        }

        .form-transfer-large .form-transfer-panel {
            height: 400px;
        }

        .form-transfer-large .form-transfer-item {
            padding: 12px 16px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Transfer 穿梭框</h1>
        <p class="demo-description">双列表数据传输组件，用于在两个列表之间移动数据项</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="form-transfer-container">
                <label class="form-transfer-label">选择用户权限</label>
                <div class="form-transfer-wrapper" id="basicTransfer">
                    <!-- 左侧面板 -->
                    <div class="form-transfer-panel">
                        <div class="form-transfer-header">
                            <span class="form-transfer-title">可选权限</span>
                            <span class="form-transfer-count" id="leftCount">0/0</span>
                        </div>
                        <div class="form-transfer-select-all">
                            <input type="checkbox" id="leftSelectAll" onchange="toggleSelectAll('left', 'basic')">
                            <label for="leftSelectAll">全选</label>
                        </div>
                        <div class="form-transfer-search">
                            <input type="text" placeholder="搜索权限..." oninput="searchItems('left', 'basic', this.value)">
                        </div>
                        <div class="form-transfer-list" id="leftList"></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-transfer-operations">
                        <button class="form-transfer-button" id="toRightBtn" onclick="moveItems('basic', 'right')" disabled>
                            <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                            </svg>
                        </button>
                        <button class="form-transfer-button" id="toLeftBtn" onclick="moveItems('basic', 'left')" disabled>
                            <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 右侧面板 -->
                    <div class="form-transfer-panel">
                        <div class="form-transfer-header">
                            <span class="form-transfer-title">已选权限</span>
                            <span class="form-transfer-count" id="rightCount">0/0</span>
                        </div>
                        <div class="form-transfer-select-all">
                            <input type="checkbox" id="rightSelectAll" onchange="toggleSelectAll('right', 'basic')">
                            <label for="rightSelectAll">全选</label>
                        </div>
                        <div class="form-transfer-search">
                            <input type="text" placeholder="搜索权限..." oninput="searchItems('right', 'basic', this.value)">
                        </div>
                        <div class="form-transfer-list" id="rightList"></div>
                    </div>
                </div>
            </div>
            <div class="demo-result" id="basicResult">已选择权限：0 项</div>
        </div>

        <div class="demo-section">
            <h3>带图标和描述</h3>
            <div class="form-transfer-container">
                <label class="form-transfer-label">选择功能模块</label>
                <div class="form-transfer-wrapper" id="iconTransfer">
                    <!-- 左侧面板 -->
                    <div class="form-transfer-panel">
                        <div class="form-transfer-header">
                            <span class="form-transfer-title">可用模块</span>
                            <span class="form-transfer-count" id="iconLeftCount">0/0</span>
                        </div>
                        <div class="form-transfer-select-all">
                            <input type="checkbox" id="iconLeftSelectAll" onchange="toggleSelectAll('left', 'icon')">
                            <label for="iconLeftSelectAll">全选</label>
                        </div>
                        <div class="form-transfer-search">
                            <input type="text" placeholder="搜索模块..." oninput="searchItems('left', 'icon', this.value)">
                        </div>
                        <div class="form-transfer-list" id="iconLeftList"></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-transfer-operations">
                        <button class="form-transfer-button" id="iconToRightBtn" onclick="moveItems('icon', 'right')" disabled>
                            <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                            </svg>
                        </button>
                        <button class="form-transfer-button" id="iconToLeftBtn" onclick="moveItems('icon', 'left')" disabled>
                            <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 右侧面板 -->
                    <div class="form-transfer-panel">
                        <div class="form-transfer-header">
                            <span class="form-transfer-title">已选模块</span>
                            <span class="form-transfer-count" id="iconRightCount">0/0</span>
                        </div>
                        <div class="form-transfer-select-all">
                            <input type="checkbox" id="iconRightSelectAll" onchange="toggleSelectAll('right', 'icon')">
                            <label for="iconRightSelectAll">全选</label>
                        </div>
                        <div class="form-transfer-search">
                            <input type="text" placeholder="搜索模块..." oninput="searchItems('right', 'icon', this.value)">
                        </div>
                        <div class="form-transfer-list" id="iconRightList"></div>
                    </div>
                </div>
            </div>
            <div class="demo-result" id="iconResult">已选择模块：0 项</div>
        </div>

        <div class="demo-grid">
            <div>
                <h3>紧凑模式</h3>
                <div class="form-transfer-container">
                    <label class="form-transfer-label">选择标签</label>
                    <div class="form-transfer-wrapper form-transfer-compact" id="compactTransfer">
                        <!-- 左侧面板 -->
                        <div class="form-transfer-panel">
                            <div class="form-transfer-header">
                                <span class="form-transfer-title">可选标签</span>
                                <span class="form-transfer-count" id="compactLeftCount">0/0</span>
                            </div>
                            <div class="form-transfer-list" id="compactLeftList"></div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-transfer-operations">
                            <button class="form-transfer-button" onclick="moveItems('compact', 'right')" disabled>
                                <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                                </svg>
                            </button>
                            <button class="form-transfer-button" onclick="moveItems('compact', 'left')" disabled>
                                <svg class="form-transfer-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                                </svg>
                            </button>
                        </div>

                        <!-- 右侧面板 -->
                        <div class="form-transfer-panel">
                            <div class="form-transfer-header">
                                <span class="form-transfer-title">已选标签</span>
                                <span class="form-transfer-count" id="compactRightCount">0/0</span>
                            </div>
                            <div class="form-transfer-list" id="compactRightList"></div>
                        </div>
                    </div>
                </div>
                <div class="demo-result" id="compactResult">已选择标签：0 项</div>
            </div>
        </div>
    </div>

    <script>
        // 数据源
        const transferData = {
            basic: {
                left: [
                    { id: 1, text: '用户管理', value: 'user_manage' },
                    { id: 2, text: '角色管理', value: 'role_manage' },
                    { id: 3, text: '权限管理', value: 'permission_manage' },
                    { id: 4, text: '系统设置', value: 'system_setting' },
                    { id: 5, text: '日志查看', value: 'log_view' },
                    { id: 6, text: '数据导出', value: 'data_export' },
                    { id: 7, text: '数据导入', value: 'data_import' },
                    { id: 8, text: '备份管理', value: 'backup_manage' }
                ],
                right: [],
                leftSelected: [],
                rightSelected: []
            },
            icon: {
                left: [
                    { id: 1, text: '用户中心', value: 'user_center', icon: '👤', desc: '用户信息管理' },
                    { id: 2, text: '订单管理', value: 'order_manage', icon: '📋', desc: '订单处理和跟踪' },
                    { id: 3, text: '商品管理', value: 'product_manage', icon: '📦', desc: '商品信息维护' },
                    { id: 4, text: '财务报表', value: 'finance_report', icon: '💰', desc: '财务数据统计' },
                    { id: 5, text: '营销活动', value: 'marketing', icon: '🎯', desc: '促销活动管理' },
                    { id: 6, text: '客服系统', value: 'customer_service', icon: '💬', desc: '客户服务支持' }
                ],
                right: [],
                leftSelected: [],
                rightSelected: []
            },
            compact: {
                left: [
                    { id: 1, text: 'JavaScript', value: 'javascript' },
                    { id: 2, text: 'Python', value: 'python' },
                    { id: 3, text: 'Java', value: 'java' },
                    { id: 4, text: 'C++', value: 'cpp' },
                    { id: 5, text: 'Go', value: 'go' },
                    { id: 6, text: 'Rust', value: 'rust' },
                    { id: 7, text: 'TypeScript', value: 'typescript' },
                    { id: 8, text: 'PHP', value: 'php' }
                ],
                right: [],
                leftSelected: [],
                rightSelected: []
            }
        };

        // 初始化穿梭框
        function initTransfer() {
            Object.keys(transferData).forEach(type => {
                renderList('left', type);
                renderList('right', type);
                updateCounts(type);
                updateButtons(type);
            });
        }

        // 渲染列表
        function renderList(side, type, searchKeyword = '') {
            const listId = type === 'basic' ? `${side}List` : 
                          type === 'icon' ? `icon${side.charAt(0).toUpperCase() + side.slice(1)}List` :
                          `compact${side.charAt(0).toUpperCase() + side.slice(1)}List`;
            const list = document.getElementById(listId);
            if (!list) return;

            const data = transferData[type][side];
            const filteredData = searchKeyword ? 
                data.filter(item => item.text.toLowerCase().includes(searchKeyword.toLowerCase())) : 
                data;

            list.innerHTML = '';

            if (filteredData.length === 0) {
                const empty = document.createElement('div');
                empty.className = 'form-transfer-empty';
                empty.innerHTML = `
                    <svg class="form-transfer-empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    <span>${searchKeyword ? '无匹配结果' : '暂无数据'}</span>
                `;
                list.appendChild(empty);
                return;
            }

            filteredData.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'form-transfer-item';
                itemElement.onclick = () => toggleItemSelection(side, type, item.id);

                const isSelected = transferData[type][`${side}Selected`].includes(item.id);
                if (isSelected) {
                    itemElement.classList.add('selected');
                }

                itemElement.innerHTML = `
                    <input type="checkbox" class="form-transfer-checkbox" ${isSelected ? 'checked' : ''} 
                           onchange="toggleItemSelection('${side}', '${type}', ${item.id})">
                    <div class="form-transfer-item-content">
                        ${item.icon ? `<span class="form-transfer-item-icon">${item.icon}</span>` : ''}
                        <div>
                            <div class="form-transfer-item-text">${item.text}</div>
                            ${item.desc ? `<div class="form-transfer-item-desc">${item.desc}</div>` : ''}
                        </div>
                    </div>
                `;

                list.appendChild(itemElement);
            });
        }

        // 切换项目选择状态
        function toggleItemSelection(side, type, itemId) {
            const selected = transferData[type][`${side}Selected`];
            const index = selected.indexOf(itemId);

            if (index > -1) {
                selected.splice(index, 1);
            } else {
                selected.push(itemId);
            }

            renderList(side, type);
            updateCounts(type);
            updateButtons(type);
            updateSelectAllState(side, type);
        }

        // 全选/取消全选
        function toggleSelectAll(side, type) {
            const checkbox = document.getElementById(
                type === 'basic' ? `${side}SelectAll` :
                type === 'icon' ? `icon${side.charAt(0).toUpperCase() + side.slice(1)}SelectAll` :
                `compact${side.charAt(0).toUpperCase() + side.slice(1)}SelectAll`
            );
            
            const data = transferData[type][side];
            const selected = transferData[type][`${side}Selected`];

            if (checkbox.checked) {
                // 全选
                data.forEach(item => {
                    if (!selected.includes(item.id)) {
                        selected.push(item.id);
                    }
                });
            } else {
                // 取消全选
                transferData[type][`${side}Selected`] = [];
            }

            renderList(side, type);
            updateCounts(type);
            updateButtons(type);
        }

        // 更新全选状态
        function updateSelectAllState(side, type) {
            const checkbox = document.getElementById(
                type === 'basic' ? `${side}SelectAll` :
                type === 'icon' ? `icon${side.charAt(0).toUpperCase() + side.slice(1)}SelectAll` :
                `compact${side.charAt(0).toUpperCase() + side.slice(1)}SelectAll`
            );
            
            if (!checkbox) return;

            const data = transferData[type][side];
            const selected = transferData[type][`${side}Selected`];

            if (selected.length === 0) {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            } else if (selected.length === data.length) {
                checkbox.checked = true;
                checkbox.indeterminate = false;
            } else {
                checkbox.checked = false;
                checkbox.indeterminate = true;
            }
        }

        // 移动项目
        function moveItems(type, direction) {
            const fromSide = direction === 'right' ? 'left' : 'right';
            const toSide = direction === 'right' ? 'right' : 'left';
            
            const selectedIds = transferData[type][`${fromSide}Selected`];
            if (selectedIds.length === 0) return;

            // 移动数据
            const itemsToMove = transferData[type][fromSide].filter(item => selectedIds.includes(item.id));
            transferData[type][toSide].push(...itemsToMove);
            transferData[type][fromSide] = transferData[type][fromSide].filter(item => !selectedIds.includes(item.id));
            
            // 清空选择
            transferData[type][`${fromSide}Selected`] = [];
            transferData[type][`${toSide}Selected`] = [];

            // 重新渲染
            renderList('left', type);
            renderList('right', type);
            updateCounts(type);
            updateButtons(type);
            updateSelectAllState('left', type);
            updateSelectAllState('right', type);
            updateResult(type);
        }

        // 搜索项目
        function searchItems(side, type, keyword) {
            renderList(side, type, keyword);
        }

        // 更新计数
        function updateCounts(type) {
            const leftCountId = type === 'basic' ? 'leftCount' :
                               type === 'icon' ? 'iconLeftCount' :
                               'compactLeftCount';
            const rightCountId = type === 'basic' ? 'rightCount' :
                                 type === 'icon' ? 'iconRightCount' :
                                 'compactRightCount';

            const leftCount = document.getElementById(leftCountId);
            const rightCount = document.getElementById(rightCountId);

            if (leftCount) {
                const leftSelected = transferData[type].leftSelected.length;
                const leftTotal = transferData[type].left.length;
                leftCount.textContent = `${leftSelected}/${leftTotal}`;
            }

            if (rightCount) {
                const rightSelected = transferData[type].rightSelected.length;
                const rightTotal = transferData[type].right.length;
                rightCount.textContent = `${rightSelected}/${rightTotal}`;
            }
        }

        // 更新按钮状态
        function updateButtons(type) {
            const toRightBtnId = type === 'basic' ? 'toRightBtn' :
                                type === 'icon' ? 'iconToRightBtn' :
                                null;
            const toLeftBtnId = type === 'basic' ? 'toLeftBtn' :
                               type === 'icon' ? 'iconToLeftBtn' :
                               null;

            // 对于compact模式，按钮没有ID，通过父容器查找
            let toRightBtn, toLeftBtn;
            
            if (type === 'compact') {
                const container = document.getElementById('compactTransfer');
                const buttons = container.querySelectorAll('.form-transfer-button');
                toRightBtn = buttons[0];
                toLeftBtn = buttons[1];
            } else {
                toRightBtn = document.getElementById(toRightBtnId);
                toLeftBtn = document.getElementById(toLeftBtnId);
            }

            if (toRightBtn) {
                toRightBtn.disabled = transferData[type].leftSelected.length === 0;
            }

            if (toLeftBtn) {
                toLeftBtn.disabled = transferData[type].rightSelected.length === 0;
            }
        }

        // 更新结果显示
        function updateResult(type) {
            const resultId = `${type}Result`;
            const resultElement = document.getElementById(resultId);
            if (!resultElement) return;

            const rightData = transferData[type].right;
            const count = rightData.length;
            const items = rightData.map(item => item.text).join(', ');

            let resultText = '';
            switch (type) {
                case 'basic':
                    resultText = `已选择权限：${count} 项`;
                    if (count > 0) resultText += ` (${items})`;
                    break;
                case 'icon':
                    resultText = `已选择模块：${count} 项`;
                    if (count > 0) resultText += ` (${items})`;
                    break;
                case 'compact':
                    resultText = `已选择标签：${count} 项`;
                    if (count > 0) resultText += ` (${items})`;
                    break;
            }

            resultElement.textContent = resultText;
        }

        // 初始化
        initTransfer();
    </script>
</body>
</html>
