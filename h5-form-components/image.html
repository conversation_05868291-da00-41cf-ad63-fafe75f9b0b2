<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片展示控件</title>
</head>
<body>
    <!-- 图片展示控件 -->
    <div class="form-image">
        <style>
            .form-image {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-image-section {
                margin-bottom: 40px;
            }

            .form-image-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础图片样式 */
            .form-image-basic {
                display: block;
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }

            .form-image-basic:hover {
                transform: scale(1.02);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            /* 圆形头像 */
            .form-image-avatar {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                object-fit: cover;
                border: 3px solid #e5e7eb;
                transition: all 0.3s ease;
            }

            .form-image-avatar:hover {
                border-color: #3b82f6;
                transform: scale(1.05);
            }

            .form-image-avatar.large {
                width: 120px;
                height: 120px;
            }

            .form-image-avatar.small {
                width: 40px;
                height: 40px;
                border-width: 2px;
            }

            /* 网格布局 */
            .form-image-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
                margin-bottom: 20px;
            }

            .form-image-grid.grid-2 {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-image-grid.grid-3 {
                grid-template-columns: repeat(3, 1fr);
            }

            .form-image-grid.grid-4 {
                grid-template-columns: repeat(4, 1fr);
            }

            .form-image-grid-item {
                position: relative;
                overflow: hidden;
                border-radius: 8px;
                aspect-ratio: 1;
                background: #f3f4f6;
            }

            .form-image-grid-item img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            .form-image-grid-item:hover img {
                transform: scale(1.1);
            }

            .form-image-grid-item .overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                font-size: 14px;
                font-weight: 500;
            }

            .form-image-grid-item:hover .overlay {
                opacity: 1;
            }

            /* 轮播图 */
            .form-image-carousel {
                position: relative;
                max-width: 600px;
                margin: 0 auto;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .form-image-carousel-container {
                position: relative;
                width: 100%;
                height: 300px;
                overflow: hidden;
            }

            .form-image-carousel-slide {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
                transition: opacity 0.5s ease;
            }

            .form-image-carousel-slide.active {
                opacity: 1;
            }

            .form-image-carousel-slide img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .form-image-carousel-nav {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(255, 255, 255, 0.9);
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                color: #374151;
                transition: all 0.3s ease;
                z-index: 10;
            }

            .form-image-carousel-nav:hover {
                background: white;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }

            .form-image-carousel-nav.prev {
                left: 16px;
            }

            .form-image-carousel-nav.next {
                right: 16px;
            }

            .form-image-carousel-dots {
                position: absolute;
                bottom: 16px;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                gap: 8px;
                z-index: 10;
            }

            .form-image-carousel-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.5);
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .form-image-carousel-dot.active {
                background: white;
                transform: scale(1.2);
            }

            /* 瀑布流布局 */
            .form-image-masonry {
                column-count: 3;
                column-gap: 16px;
                margin-bottom: 20px;
            }

            .form-image-masonry-item {
                break-inside: avoid;
                margin-bottom: 16px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
            }

            .form-image-masonry-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            .form-image-masonry-item img {
                width: 100%;
                height: auto;
                display: block;
            }

            /* 图片预览 */
            .form-image-preview {
                position: relative;
                display: inline-block;
                cursor: pointer;
            }

            .form-image-preview img {
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .form-image-preview:hover img {
                filter: brightness(0.8);
            }

            .form-image-preview::after {
                content: '🔍';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 24px;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .form-image-preview:hover::after {
                opacity: 1;
            }

            /* 图片标签 */
            .form-image-with-tag {
                position: relative;
                display: inline-block;
            }

            .form-image-tag {
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .form-image-tag.new {
                background: #ef4444;
            }

            .form-image-tag.hot {
                background: #f59e0b;
            }

            .form-image-tag.sale {
                background: #10b981;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-image-grid.grid-4 {
                    grid-template-columns: repeat(2, 1fr);
                }

                .form-image-grid.grid-3 {
                    grid-template-columns: repeat(2, 1fr);
                }

                .form-image-masonry {
                    column-count: 2;
                }

                .form-image-carousel-container {
                    height: 200px;
                }
            }

            @media (max-width: 480px) {
                .form-image-grid {
                    grid-template-columns: 1fr;
                }

                .form-image-masonry {
                    column-count: 1;
                }

                .form-image-carousel-nav {
                    width: 32px;
                    height: 32px;
                    font-size: 14px;
                }

                .form-image-carousel-nav.prev {
                    left: 8px;
                }

                .form-image-carousel-nav.next {
                    right: 8px;
                }
            }

            /* 加载状态 */
            .form-image-loading {
                background: #f3f4f6;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #9ca3af;
                font-size: 14px;
                min-height: 200px;
                border-radius: 8px;
            }

            .form-image-loading::before {
                content: '⏳';
                margin-right: 8px;
                font-size: 18px;
            }

            /* 错误状态 */
            .form-image-error {
                background: #fee2e2;
                color: #dc2626;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                min-height: 200px;
                border-radius: 8px;
                border: 1px dashed #fca5a5;
            }

            .form-image-error::before {
                content: '❌';
                margin-right: 8px;
                font-size: 18px;
            }
        </style>

        <!-- 基础图片展示 -->
        <div class="form-image-section">
            <h3 class="form-image-title">基础图片展示</h3>
            <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                <img src="https://picsum.photos/300/200?random=1" alt="示例图片" class="form-image-basic" style="max-width: 300px;">
                
                <div style="display: flex; gap: 12px; align-items: center;">
                    <img src="https://picsum.photos/120/120?random=2" alt="大头像" class="form-image-avatar large">
                    <img src="https://picsum.photos/80/80?random=3" alt="普通头像" class="form-image-avatar">
                    <img src="https://picsum.photos/40/40?random=4" alt="小头像" class="form-image-avatar small">
                </div>
            </div>
        </div>

        <!-- 网格布局 -->
        <div class="form-image-section">
            <h3 class="form-image-title">网格布局</h3>
            <div class="form-image-grid grid-4">
                <div class="form-image-grid-item">
                    <img src="https://picsum.photos/300/300?random=5" alt="图片1">
                    <div class="overlay">查看详情</div>
                </div>
                <div class="form-image-grid-item">
                    <img src="https://picsum.photos/300/300?random=6" alt="图片2">
                    <div class="overlay">查看详情</div>
                </div>
                <div class="form-image-grid-item">
                    <img src="https://picsum.photos/300/300?random=7" alt="图片3">
                    <div class="overlay">查看详情</div>
                </div>
                <div class="form-image-grid-item">
                    <img src="https://picsum.photos/300/300?random=8" alt="图片4">
                    <div class="overlay">查看详情</div>
                </div>
            </div>
        </div>

        <!-- 轮播图 -->
        <div class="form-image-section">
            <h3 class="form-image-title">轮播图</h3>
            <div class="form-image-carousel" id="carousel1">
                <div class="form-image-carousel-container">
                    <div class="form-image-carousel-slide active">
                        <img src="https://picsum.photos/600/300?random=9" alt="轮播图1">
                    </div>
                    <div class="form-image-carousel-slide">
                        <img src="https://picsum.photos/600/300?random=10" alt="轮播图2">
                    </div>
                    <div class="form-image-carousel-slide">
                        <img src="https://picsum.photos/600/300?random=11" alt="轮播图3">
                    </div>
                </div>
                <button class="form-image-carousel-nav prev" onclick="prevSlide('carousel1')">‹</button>
                <button class="form-image-carousel-nav next" onclick="nextSlide('carousel1')">›</button>
                <div class="form-image-carousel-dots">
                    <div class="form-image-carousel-dot active" onclick="goToSlide('carousel1', 0)"></div>
                    <div class="form-image-carousel-dot" onclick="goToSlide('carousel1', 1)"></div>
                    <div class="form-image-carousel-dot" onclick="goToSlide('carousel1', 2)"></div>
                </div>
            </div>
        </div>

        <!-- 带标签的图片 -->
        <div class="form-image-section">
            <h3 class="form-image-title">带标签图片</h3>
            <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                <div class="form-image-with-tag">
                    <img src="https://picsum.photos/200/150?random=12" alt="新品" class="form-image-basic" style="max-width: 200px;">
                    <div class="form-image-tag new">新品</div>
                </div>
                <div class="form-image-with-tag">
                    <img src="https://picsum.photos/200/150?random=13" alt="热销" class="form-image-basic" style="max-width: 200px;">
                    <div class="form-image-tag hot">热销</div>
                </div>
                <div class="form-image-with-tag">
                    <img src="https://picsum.photos/200/150?random=14" alt="促销" class="form-image-basic" style="max-width: 200px;">
                    <div class="form-image-tag sale">促销</div>
                </div>
            </div>
        </div>

        <!-- 瀑布流布局 -->
        <div class="form-image-section">
            <h3 class="form-image-title">瀑布流布局</h3>
            <div class="form-image-masonry">
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/400?random=15" alt="瀑布流1">
                </div>
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/300?random=16" alt="瀑布流2">
                </div>
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/500?random=17" alt="瀑布流3">
                </div>
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/350?random=18" alt="瀑布流4">
                </div>
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/450?random=19" alt="瀑布流5">
                </div>
                <div class="form-image-masonry-item">
                    <img src="https://picsum.photos/300/320?random=20" alt="瀑布流6">
                </div>
            </div>
        </div>

        <!-- 状态展示 -->
        <div class="form-image-section">
            <h3 class="form-image-title">状态展示</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div class="form-image-loading">
                    加载中...
                </div>
                <div class="form-image-error">
                    加载失败
                </div>
            </div>
        </div>

        <script>
            // 轮播图功能
            const carousels = {};

            function initCarousel(carouselId) {
                carousels[carouselId] = {
                    currentSlide: 0,
                    slides: document.querySelectorAll(`#${carouselId} .form-image-carousel-slide`),
                    dots: document.querySelectorAll(`#${carouselId} .form-image-carousel-dot`)
                };
            }

            function goToSlide(carouselId, slideIndex) {
                const carousel = carousels[carouselId];
                if (!carousel) return;

                // 隐藏当前幻灯片
                carousel.slides[carousel.currentSlide].classList.remove('active');
                carousel.dots[carousel.currentSlide].classList.remove('active');

                // 显示新幻灯片
                carousel.currentSlide = slideIndex;
                carousel.slides[carousel.currentSlide].classList.add('active');
                carousel.dots[carousel.currentSlide].classList.add('active');
            }

            function nextSlide(carouselId) {
                const carousel = carousels[carouselId];
                if (!carousel) return;

                const nextIndex = (carousel.currentSlide + 1) % carousel.slides.length;
                goToSlide(carouselId, nextIndex);
            }

            function prevSlide(carouselId) {
                const carousel = carousels[carouselId];
                if (!carousel) return;

                const prevIndex = (carousel.currentSlide - 1 + carousel.slides.length) % carousel.slides.length;
                goToSlide(carouselId, prevIndex);
            }

            // 初始化轮播图
            document.addEventListener('DOMContentLoaded', function() {
                initCarousel('carousel1');

                // 自动播放
                setInterval(() => {
                    nextSlide('carousel1');
                }, 5000);
            });

            // 图片懒加载
            function lazyLoadImages() {
                const images = document.querySelectorAll('img[data-src]');
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            }

            // 图片预览功能
            function previewImage(src) {
                // 创建预览模态框
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    cursor: pointer;
                `;

                const img = document.createElement('img');
                img.src = src;
                img.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                `;

                modal.appendChild(img);
                document.body.appendChild(modal);

                // 点击关闭
                modal.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });

                // ESC键关闭
                const handleEsc = (e) => {
                    if (e.key === 'Escape') {
                        document.body.removeChild(modal);
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);
            }

            // 为预览图片添加点击事件
            document.addEventListener('DOMContentLoaded', function() {
                const previewImages = document.querySelectorAll('.form-image-preview img');
                previewImages.forEach(img => {
                    img.addEventListener('click', () => {
                        previewImage(img.src);
                    });
                });
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础图片 --&gt;
&lt;img src="image.jpg" alt="描述" class="form-image-basic"&gt;

&lt;!-- 头像 --&gt;
&lt;img src="avatar.jpg" alt="头像" class="form-image-avatar"&gt;

&lt;!-- 网格布局 --&gt;
&lt;div class="form-image-grid grid-3"&gt;
    &lt;div class="form-image-grid-item"&gt;
        &lt;img src="image1.jpg" alt="图片1"&gt;
        &lt;div class="overlay"&gt;查看详情&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 轮播图 --&gt;
&lt;div class="form-image-carousel" id="carousel1"&gt;
    &lt;div class="form-image-carousel-container"&gt;
        &lt;div class="form-image-carousel-slide active"&gt;
            &lt;img src="slide1.jpg" alt="幻灯片1"&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;button class="form-image-carousel-nav prev"&gt;‹&lt;/button&gt;
    &lt;button class="form-image-carousel-nav next"&gt;›&lt;/button&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
