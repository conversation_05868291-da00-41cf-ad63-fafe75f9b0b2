<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel 走马灯组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 走马灯基础样式 */
        .form-carousel-container {
            position: relative;
            margin-bottom: 24px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f8fafc;
        }

        .form-carousel-wrapper {
            position: relative;
            width: 100%;
            height: 300px;
            overflow: hidden;
        }

        .form-carousel-track {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease;
        }

        .form-carousel-slide {
            flex: 0 0 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .form-carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .form-carousel-slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 40px 20px 20px;
        }

        .form-carousel-slide-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-carousel-slide-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 导航按钮 */
        .form-carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .form-carousel-nav:hover {
            background-color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .form-carousel-nav.prev {
            left: 16px;
        }

        .form-carousel-nav.next {
            right: 16px;
        }

        .form-carousel-nav-icon {
            width: 20px;
            height: 20px;
            fill: #374151;
        }

        /* 指示器 */
        .form-carousel-indicators {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .form-carousel-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .form-carousel-indicator.active {
            background-color: white;
            transform: scale(1.2);
        }

        /* 缩略图导航 */
        .form-carousel-thumbnails {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            overflow-x: auto;
            padding: 8px 0;
        }

        .form-carousel-thumbnail {
            flex: 0 0 80px;
            height: 60px;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .form-carousel-thumbnail:hover {
            opacity: 0.8;
        }

        .form-carousel-thumbnail.active {
            opacity: 1;
            border-color: #3b82f6;
        }

        .form-carousel-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 自动播放控制 */
        .form-carousel-controls {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .form-carousel-control {
            width: 32px;
            height: 32px;
            background-color: rgba(0, 0, 0, 0.5);
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .form-carousel-control:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .form-carousel-control-icon {
            width: 16px;
            height: 16px;
        }

        /* 垂直轮播 */
        .form-carousel-vertical .form-carousel-wrapper {
            height: 400px;
        }

        .form-carousel-vertical .form-carousel-track {
            flex-direction: column;
            height: auto;
        }

        .form-carousel-vertical .form-carousel-slide {
            flex: 0 0 100%;
            width: 100%;
        }

        .form-carousel-vertical .form-carousel-nav {
            left: 50%;
            transform: translateX(-50%);
        }

        .form-carousel-vertical .form-carousel-nav.prev {
            top: 16px;
        }

        .form-carousel-vertical .form-carousel-nav.next {
            bottom: 16px;
            top: auto;
        }

        /* 卡片轮播 */
        .form-carousel-cards .form-carousel-slide {
            padding: 20px;
        }

        .form-carousel-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 300px;
            margin: 0 auto;
        }

        .form-carousel-card-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            background-color: #eff6ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }

        .form-carousel-card-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .form-carousel-card-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-carousel-wrapper {
                height: 250px;
            }
            
            .form-carousel-slide-title {
                font-size: 20px;
            }
            
            .form-carousel-nav {
                width: 36px;
                height: 36px;
            }
            
            .form-carousel-nav.prev {
                left: 12px;
            }
            
            .form-carousel-nav.next {
                right: 12px;
            }
        }

        @media (max-width: 480px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-carousel-wrapper {
                height: 200px;
            }
            
            .form-carousel-slide-content {
                padding: 20px 16px 16px;
            }
            
            .form-carousel-slide-title {
                font-size: 18px;
            }
            
            .form-carousel-slide-desc {
                font-size: 13px;
            }
            
            .form-carousel-thumbnail {
                flex: 0 0 60px;
                height: 45px;
            }
        }

        /* 演示样式 */
        .demo-controls {
            margin-bottom: 16px;
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .demo-button {
            padding: 6px 12px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s ease;
        }

        .demo-button:hover {
            background-color: #2563eb;
        }

        .demo-info {
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Carousel 走马灯</h1>
        <p class="demo-description">轮播图组件，支持自动播放、手动控制、指示器和缩略图导航</p>

        <div class="demo-section">
            <h3>基础轮播</h3>
            <div class="demo-controls">
                <button class="demo-button" onclick="toggleAutoplay('basic')">切换自动播放</button>
                <span class="demo-info" id="basicInfo">自动播放：开启</span>
            </div>
            <div class="form-carousel-container" id="basicCarousel">
                <div class="form-carousel-wrapper">
                    <div class="form-carousel-track" id="basicTrack">
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjY3ZWVhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7luYblubvlm74gMTwvdGV4dD48L3N2Zz4=" alt="幻灯片 1">
                            <div class="form-carousel-slide-content">
                                <div class="form-carousel-slide-title">精彩内容 1</div>
                                <div class="form-carousel-slide-desc">这是第一张幻灯片的描述信息</div>
                            </div>
                        </div>
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjA5M2ZiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7luYblubvlm74gMjwvdGV4dD48L3N2Zz4=" alt="幻灯片 2">
                            <div class="form-carousel-slide-content">
                                <div class="form-carousel-slide-title">精彩内容 2</div>
                                <div class="form-carousel-slide-desc">这是第二张幻灯片的描述信息</div>
                            </div>
                        </div>
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNGZkMWM3Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7luYblubvlm74gMzwvdGV4dD48L3N2Zz4=" alt="幻灯片 3">
                            <div class="form-carousel-slide-content">
                                <div class="form-carousel-slide-title">精彩内容 3</div>
                                <div class="form-carousel-slide-desc">这是第三张幻灯片的描述信息</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="form-carousel-nav prev" onclick="prevSlide('basic')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                </button>
                <button class="form-carousel-nav next" onclick="nextSlide('basic')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                </button>
                
                <div class="form-carousel-indicators" id="basicIndicators"></div>
                
                <div class="form-carousel-controls">
                    <button class="form-carousel-control" onclick="toggleAutoplay('basic')" title="播放/暂停">
                        <svg class="form-carousel-control-icon" viewBox="0 0 24 24" fill="currentColor" id="basicPlayIcon">
                            <path d="M14,19H18V5H14M6,19H10V5H6V19Z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>带缩略图导航</h3>
            <div class="form-carousel-container" id="thumbCarousel">
                <div class="form-carousel-wrapper">
                    <div class="form-carousel-track" id="thumbTrack">
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2YjZiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYcgMTwvdGV4dD48L3N2Zz4=" alt="图片 1">
                        </div>
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNGVjZGMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYcgMjwvdGV4dD48L3N2Zz4=" alt="图片 2">
                        </div>
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmJiZjI0Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYcgMzwvdGV4dD48L3N2Zz4=" alt="图片 3">
                        </div>
                        <div class="form-carousel-slide">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjYTc4YmZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYcgNDwvdGV4dD48L3N2Zz4=" alt="图片 4">
                        </div>
                    </div>
                </div>
                
                <button class="form-carousel-nav prev" onclick="prevSlide('thumb')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                </button>
                <button class="form-carousel-nav next" onclick="nextSlide('thumb')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                </button>
                
                <div class="form-carousel-indicators" id="thumbIndicators"></div>
            </div>
            
            <div class="form-carousel-thumbnails" id="thumbThumbnails"></div>
        </div>

        <div class="demo-section">
            <h3>卡片轮播</h3>
            <div class="form-carousel-container form-carousel-cards" id="cardCarousel">
                <div class="form-carousel-wrapper">
                    <div class="form-carousel-track" id="cardTrack">
                        <div class="form-carousel-slide">
                            <div class="form-carousel-card">
                                <div class="form-carousel-card-icon">🚀</div>
                                <div class="form-carousel-card-title">快速开发</div>
                                <div class="form-carousel-card-desc">提供丰富的组件库，让您的开发效率提升10倍</div>
                            </div>
                        </div>
                        <div class="form-carousel-slide">
                            <div class="form-carousel-card">
                                <div class="form-carousel-card-icon">🎨</div>
                                <div class="form-carousel-card-title">精美设计</div>
                                <div class="form-carousel-card-desc">现代化的设计风格，完美适配移动端和桌面端</div>
                            </div>
                        </div>
                        <div class="form-carousel-slide">
                            <div class="form-carousel-card">
                                <div class="form-carousel-card-icon">⚡</div>
                                <div class="form-carousel-card-title">高性能</div>
                                <div class="form-carousel-card-desc">轻量级实现，加载速度快，用户体验佳</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="form-carousel-nav prev" onclick="prevSlide('card')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                </button>
                <button class="form-carousel-nav next" onclick="nextSlide('card')">
                    <svg class="form-carousel-nav-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                </button>
                
                <div class="form-carousel-indicators" id="cardIndicators"></div>
            </div>
        </div>
    </div>

    <script>
        // 轮播状态管理
        const carouselStates = {
            basic: {
                currentIndex: 0,
                totalSlides: 3,
                autoplay: true,
                interval: null,
                duration: 3000
            },
            thumb: {
                currentIndex: 0,
                totalSlides: 4,
                autoplay: false,
                interval: null,
                duration: 4000
            },
            card: {
                currentIndex: 0,
                totalSlides: 3,
                autoplay: false,
                interval: null,
                duration: 3000
            }
        };

        // 初始化轮播
        function initCarousels() {
            Object.keys(carouselStates).forEach(type => {
                initCarousel(type);
            });
        }

        // 初始化单个轮播
        function initCarousel(type) {
            const state = carouselStates[type];
            
            // 创建指示器
            createIndicators(type);
            
            // 创建缩略图（如果需要）
            if (type === 'thumb') {
                createThumbnails(type);
            }
            
            // 更新显示
            updateCarousel(type);
            
            // 启动自动播放
            if (state.autoplay) {
                startAutoplay(type);
            }
            
            // 添加触摸支持
            addTouchSupport(type);
        }

        // 创建指示器
        function createIndicators(type) {
            const container = document.getElementById(`${type}Indicators`);
            if (!container) return;

            container.innerHTML = '';
            const state = carouselStates[type];

            for (let i = 0; i < state.totalSlides; i++) {
                const indicator = document.createElement('div');
                indicator.className = `form-carousel-indicator ${i === 0 ? 'active' : ''}`;
                indicator.onclick = () => goToSlide(type, i);
                container.appendChild(indicator);
            }
        }

        // 创建缩略图
        function createThumbnails(type) {
            const container = document.getElementById(`${type}Thumbnails`);
            if (!container) return;

            container.innerHTML = '';
            const slides = document.querySelectorAll(`#${type}Track .form-carousel-slide`);

            slides.forEach((slide, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = `form-carousel-thumbnail ${index === 0 ? 'active' : ''}`;
                thumbnail.onclick = () => goToSlide(type, index);

                const img = slide.querySelector('img');
                if (img) {
                    const thumbImg = document.createElement('img');
                    thumbImg.src = img.src;
                    thumbImg.alt = img.alt;
                    thumbnail.appendChild(thumbImg);
                }

                container.appendChild(thumbnail);
            });
        }

        // 更新轮播显示
        function updateCarousel(type) {
            const state = carouselStates[type];
            const track = document.getElementById(`${type}Track`);
            const indicators = document.querySelectorAll(`#${type}Indicators .form-carousel-indicator`);
            const thumbnails = document.querySelectorAll(`#${type}Thumbnails .form-carousel-thumbnail`);

            if (track) {
                const translateX = -state.currentIndex * 100;
                track.style.transform = `translateX(${translateX}%)`;
            }

            // 更新指示器
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === state.currentIndex);
            });

            // 更新缩略图
            thumbnails.forEach((thumbnail, index) => {
                thumbnail.classList.toggle('active', index === state.currentIndex);
            });
        }

        // 下一张
        function nextSlide(type) {
            const state = carouselStates[type];
            state.currentIndex = (state.currentIndex + 1) % state.totalSlides;
            updateCarousel(type);
        }

        // 上一张
        function prevSlide(type) {
            const state = carouselStates[type];
            state.currentIndex = (state.currentIndex - 1 + state.totalSlides) % state.totalSlides;
            updateCarousel(type);
        }

        // 跳转到指定幻灯片
        function goToSlide(type, index) {
            const state = carouselStates[type];
            state.currentIndex = index;
            updateCarousel(type);
        }

        // 启动自动播放
        function startAutoplay(type) {
            const state = carouselStates[type];
            if (state.interval) {
                clearInterval(state.interval);
            }
            
            state.interval = setInterval(() => {
                nextSlide(type);
            }, state.duration);
            
            state.autoplay = true;
            updatePlayIcon(type);
        }

        // 停止自动播放
        function stopAutoplay(type) {
            const state = carouselStates[type];
            if (state.interval) {
                clearInterval(state.interval);
                state.interval = null;
            }
            
            state.autoplay = false;
            updatePlayIcon(type);
        }

        // 切换自动播放
        function toggleAutoplay(type) {
            const state = carouselStates[type];
            if (state.autoplay) {
                stopAutoplay(type);
            } else {
                startAutoplay(type);
            }
            
            updateAutoplayInfo(type);
        }

        // 更新播放图标
        function updatePlayIcon(type) {
            const icon = document.getElementById(`${type}PlayIcon`);
            if (!icon) return;

            const state = carouselStates[type];
            if (state.autoplay) {
                // 暂停图标
                icon.innerHTML = '<path d="M14,19H18V5H14M6,19H10V5H6V19Z"/>';
            } else {
                // 播放图标
                icon.innerHTML = '<path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>';
            }
        }

        // 更新自动播放信息
        function updateAutoplayInfo(type) {
            const info = document.getElementById(`${type}Info`);
            if (!info) return;

            const state = carouselStates[type];
            info.textContent = `自动播放：${state.autoplay ? '开启' : '关闭'}`;
        }

        // 添加触摸支持
        function addTouchSupport(type) {
            const container = document.getElementById(`${type}Carousel`);
            if (!container) return;

            let startX = 0;
            let startY = 0;
            let isDragging = false;

            container.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                isDragging = true;
                
                // 暂停自动播放
                if (carouselStates[type].autoplay) {
                    stopAutoplay(type);
                }
            });

            container.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                e.preventDefault();
            });

            container.addEventListener('touchend', (e) => {
                if (!isDragging) return;
                isDragging = false;

                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const deltaX = endX - startX;
                const deltaY = endY - startY;

                // 判断是否为水平滑动
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                    if (deltaX > 0) {
                        prevSlide(type);
                    } else {
                        nextSlide(type);
                    }
                }

                // 恢复自动播放（如果之前是开启的）
                setTimeout(() => {
                    if (type === 'basic') {
                        startAutoplay(type);
                        updateAutoplayInfo(type);
                    }
                }, 1000);
            });
        }

        // 鼠标悬停暂停自动播放
        function addHoverPause(type) {
            const container = document.getElementById(`${type}Carousel`);
            if (!container) return;

            container.addEventListener('mouseenter', () => {
                if (carouselStates[type].autoplay) {
                    stopAutoplay(type);
                }
            });

            container.addEventListener('mouseleave', () => {
                if (carouselStates[type].autoplay) {
                    startAutoplay(type);
                }
            });
        }

        // 键盘导航支持
        function addKeyboardSupport() {
            document.addEventListener('keydown', (e) => {
                // 只在焦点在轮播容器内时响应
                const activeCarousel = document.activeElement.closest('[id$="Carousel"]');
                if (!activeCarousel) return;

                const type = activeCarousel.id.replace('Carousel', '');
                
                switch (e.key) {
                    case 'ArrowLeft':
                        e.preventDefault();
                        prevSlide(type);
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        nextSlide(type);
                        break;
                    case ' ':
                        e.preventDefault();
                        toggleAutoplay(type);
                        break;
                }
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initCarousels();
            addKeyboardSupport();
            
            // 为基础轮播添加鼠标悬停暂停
            addHoverPause('basic');
        });
    </script>
</body>
</html>
