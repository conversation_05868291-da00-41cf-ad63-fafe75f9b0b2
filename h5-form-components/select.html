<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉选择框组件</title>
</head>
<body>
    <!-- 下拉选择框组件 -->
    <div class="form-select">
        <style>
            .form-select {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-select-group {
                margin-bottom: 20px;
                position: relative;
            }

            .form-select-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-select-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-select-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
            }

            .form-select-input {
                width: 100%;
                padding: 12px 40px 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                min-height: 44px;
                box-sizing: border-box;
                cursor: pointer;
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
            }

            .form-select-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-select-input:hover:not(:focus) {
                border-color: #9ca3af;
            }

            .form-select-arrow {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #6b7280;
                pointer-events: none;
                transition: transform 0.2s ease;
            }

            .form-select-input:focus + .form-select-arrow {
                transform: translateY(-50%) rotate(180deg);
            }

            /* 错误状态 */
            .form-select-input.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            .form-select-input.error:focus {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 成功状态 */
            .form-select-input.success {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-select-input.success:focus {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-select-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-select-message.error {
                color: #ef4444;
            }

            .form-select-message.success {
                color: #10b981;
            }

            .form-select-message.help {
                color: #6b7280;
            }

            /* 选项样式优化 */
            .form-select-input option {
                padding: 8px 12px;
                font-size: 16px;
                color: #374151;
            }

            .form-select-input option:disabled {
                color: #9ca3af;
                background-color: #f3f4f6;
            }

            @media (max-width: 480px) {
                .form-select {
                    margin: 12px 0;
                }

                .form-select-input {
                    padding: 14px 40px 14px 16px;
                    font-size: 16px;
                    min-height: 48px;
                }

                .form-select-label {
                    font-size: 16px;
                }

                .form-select-arrow {
                    right: 14px;
                }
            }
        </style>

        <!-- 普通下拉选择框 -->
        <div class="form-select-group">
            <label class="form-select-label" for="city">
                所在城市 <span class="form-select-required">*</span>
            </label>
            <div class="form-select-wrapper">
                <select id="city" name="city" class="form-select-input" required>
                    <option value="" disabled selected>请选择您的城市</option>
                    <option value="beijing">北京</option>
                    <option value="shanghai">上海</option>
                    <option value="guangzhou">广州</option>
                    <option value="shenzhen">深圳</option>
                    <option value="hangzhou">杭州</option>
                    <option value="nanjing">南京</option>
                    <option value="wuhan">武汉</option>
                    <option value="chengdu">成都</option>
                </select>
                <div class="form-select-arrow"></div>
            </div>
            <div class="form-select-message help">请选择您当前居住的城市</div>
        </div>

        <!-- 错误状态下拉选择框 -->
        <div class="form-select-group">
            <label class="form-select-label" for="department-error">
                部门 <span class="form-select-required">*</span>
            </label>
            <div class="form-select-wrapper">
                <select id="department-error" name="department" class="form-select-input error" required>
                    <option value="" disabled selected>请选择部门</option>
                    <option value="tech">技术部</option>
                    <option value="sales">销售部</option>
                    <option value="marketing">市场部</option>
                    <option value="hr">人力资源部</option>
                </select>
                <div class="form-select-arrow"></div>
            </div>
            <div class="form-select-message error">请选择您的部门</div>
        </div>

        <!-- 成功状态下拉选择框 -->
        <div class="form-select-group">
            <label class="form-select-label" for="experience">
                工作经验
            </label>
            <div class="form-select-wrapper">
                <select id="experience" name="experience" class="form-select-input success">
                    <option value="" disabled>请选择工作经验</option>
                    <option value="0-1">0-1年</option>
                    <option value="1-3" selected>1-3年</option>
                    <option value="3-5">3-5年</option>
                    <option value="5-10">5-10年</option>
                    <option value="10+">10年以上</option>
                </select>
                <div class="form-select-arrow"></div>
            </div>
            <div class="form-select-message success">选择完成</div>
        </div>

        <!-- 分组选项 -->
        <div class="form-select-group">
            <label class="form-select-label" for="skill">
                技能领域
            </label>
            <div class="form-select-wrapper">
                <select id="skill" name="skill" class="form-select-input">
                    <option value="" disabled selected>请选择技能领域</option>
                    <optgroup label="前端开发">
                        <option value="html">HTML/CSS</option>
                        <option value="javascript">JavaScript</option>
                        <option value="react">React</option>
                        <option value="vue">Vue.js</option>
                    </optgroup>
                    <optgroup label="后端开发">
                        <option value="java">Java</option>
                        <option value="python">Python</option>
                        <option value="nodejs">Node.js</option>
                        <option value="php">PHP</option>
                    </optgroup>
                    <optgroup label="移动开发">
                        <option value="ios">iOS</option>
                        <option value="android">Android</option>
                        <option value="flutter">Flutter</option>
                    </optgroup>
                </select>
                <div class="form-select-arrow"></div>
            </div>
            <div class="form-select-message help">选择您最擅长的技能领域</div>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-select-group"&gt;
    &lt;label class="form-select-label" for="city"&gt;
        所在城市 &lt;span class="form-select-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-select-wrapper"&gt;
        &lt;select id="city" name="city" class="form-select-input" required&gt;
            &lt;option value="" disabled selected&gt;请选择您的城市&lt;/option&gt;
            &lt;option value="beijing"&gt;北京&lt;/option&gt;
            &lt;option value="shanghai"&gt;上海&lt;/option&gt;
            &lt;!-- 更多选项... --&gt;
        &lt;/select&gt;
        &lt;div class="form-select-arrow"&gt;&lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="form-select-message help"&gt;请选择您当前居住的城市&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 错误状态 --&gt;
&lt;select class="form-select-input error" ... &gt;
&lt;div class="form-select-message error"&gt;错误信息&lt;/div&gt;

&lt;!-- 成功状态 --&gt;
&lt;select class="form-select-input success" ... &gt;
&lt;div class="form-select-message success"&gt;成功信息&lt;/div&gt;</code></pre>
    </div>
</body>
</html>