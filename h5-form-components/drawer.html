<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drawer 抽屉组件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-align: center;
        }

        .demo-description {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* 抽屉基础样式 */
        .form-drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .form-drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .form-drawer-container {
            position: absolute;
            background-color: #fff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        /* 右侧抽屉 */
        .form-drawer-right {
            top: 0;
            right: 0;
            bottom: 0;
            width: 400px;
            transform: translateX(100%);
        }

        .form-drawer-overlay.show .form-drawer-right {
            transform: translateX(0);
        }

        /* 左侧抽屉 */
        .form-drawer-left {
            top: 0;
            left: 0;
            bottom: 0;
            width: 400px;
            transform: translateX(-100%);
        }

        .form-drawer-overlay.show .form-drawer-left {
            transform: translateX(0);
        }

        /* 顶部抽屉 */
        .form-drawer-top {
            top: 0;
            left: 0;
            right: 0;
            height: 300px;
            transform: translateY(-100%);
        }

        .form-drawer-overlay.show .form-drawer-top {
            transform: translateY(0);
        }

        /* 底部抽屉 */
        .form-drawer-bottom {
            bottom: 0;
            left: 0;
            right: 0;
            height: 300px;
            transform: translateY(100%);
        }

        .form-drawer-overlay.show .form-drawer-bottom {
            transform: translateY(0);
        }

        /* 抽屉头部 */
        .form-drawer-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .form-drawer-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .form-drawer-close {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .form-drawer-close:hover {
            background-color: #f3f4f6;
        }

        .form-drawer-close-icon {
            width: 20px;
            height: 20px;
        }

        /* 抽屉内容 */
        .form-drawer-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        /* 抽屉底部 */
        .form-drawer-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            flex-shrink: 0;
        }

        /* 按钮样式 */
        .form-button {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .form-button-primary {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .form-button-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }

        .form-button-secondary {
            background-color: white;
            color: #374151;
        }

        .form-button-secondary:hover {
            background-color: #f9fafb;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-drawer-right,
            .form-drawer-left {
                width: 320px;
            }
            
            .form-drawer-top,
            .form-drawer-bottom {
                height: 250px;
            }
        }

        @media (max-width: 480px) {
            .form-drawer-right,
            .form-drawer-left {
                width: 100%;
            }
            
            .form-drawer-header {
                padding: 16px 20px;
            }
            
            .form-drawer-body {
                padding: 20px;
            }
            
            .form-drawer-footer {
                padding: 12px 20px;
            }
        }

        /* 演示样式 */
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .demo-button {
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        /* 内容样式 */
        .drawer-content {
            line-height: 1.6;
        }

        .drawer-content h4 {
            color: #1f2937;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .drawer-content p {
            color: #6b7280;
            margin-bottom: 16px;
        }

        .drawer-content ul {
            margin-left: 20px;
            margin-bottom: 16px;
        }

        .drawer-content li {
            color: #6b7280;
            margin-bottom: 8px;
        }

        /* 表单样式 */
        .drawer-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .drawer-form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .drawer-form-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .drawer-form-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .drawer-form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .drawer-form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        /* 菜单样式 */
        .drawer-menu {
            list-style: none;
        }

        .drawer-menu-item {
            border-bottom: 1px solid #f3f4f6;
        }

        .drawer-menu-link {
            display: flex;
            align-items: center;
            padding: 16px 0;
            color: #374151;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .drawer-menu-link:hover {
            color: #3b82f6;
        }

        .drawer-menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Drawer 抽屉</h1>
        <p class="demo-description">侧边抽屉组件，支持四个方向弹出，适用于导航菜单、表单填写等场景</p>

        <div class="demo-section">
            <h3>基础用法</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="showDrawer('right')">右侧抽屉</button>
                <button class="demo-button" onclick="showDrawer('left')">左侧抽屉</button>
                <button class="demo-button" onclick="showDrawer('top')">顶部抽屉</button>
                <button class="demo-button" onclick="showDrawer('bottom')">底部抽屉</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>不同内容类型</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="showDrawer('form')">表单抽屉</button>
                <button class="demo-button" onclick="showDrawer('menu')">菜单抽屉</button>
                <button class="demo-button" onclick="showDrawer('info')">信息抽屉</button>
            </div>
        </div>
    </div>

    <!-- 右侧抽屉 -->
    <div class="form-drawer-overlay" id="rightDrawer">
        <div class="form-drawer-container form-drawer-right">
            <div class="form-drawer-header">
                <div class="form-drawer-title">右侧抽屉</div>
                <button class="form-drawer-close" onclick="hideDrawer('rightDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <div class="drawer-content">
                    <h4>抽屉内容</h4>
                    <p>这是一个从右侧滑出的抽屉组件。您可以在这里放置任何内容，比如导航菜单、表单、详细信息等。</p>
                    <p>抽屉支持以下特性：</p>
                    <ul>
                        <li>四个方向的滑出动画</li>
                        <li>点击遮罩层关闭</li>
                        <li>ESC键关闭</li>
                        <li>响应式设计</li>
                        <li>自定义内容</li>
                    </ul>
                </div>
            </div>
            <div class="form-drawer-footer">
                <button class="form-button form-button-secondary" onclick="hideDrawer('rightDrawer')">取消</button>
                <button class="form-button form-button-primary">确定</button>
            </div>
        </div>
    </div>

    <!-- 左侧抽屉 -->
    <div class="form-drawer-overlay" id="leftDrawer">
        <div class="form-drawer-container form-drawer-left">
            <div class="form-drawer-header">
                <div class="form-drawer-title">左侧抽屉</div>
                <button class="form-drawer-close" onclick="hideDrawer('leftDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <div class="drawer-content">
                    <h4>导航菜单</h4>
                    <p>左侧抽屉通常用于放置导航菜单或侧边栏内容。</p>
                    <ul class="drawer-menu">
                        <li class="drawer-menu-item">
                            <a href="#" class="drawer-menu-link">
                                <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                                </svg>
                                首页
                            </a>
                        </li>
                        <li class="drawer-menu-item">
                            <a href="#" class="drawer-menu-link">
                                <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z"/>
                                </svg>
                                用户中心
                            </a>
                        </li>
                        <li class="drawer-menu-item">
                            <a href="#" class="drawer-menu-link">
                                <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                </svg>
                                设置
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 顶部抽屉 -->
    <div class="form-drawer-overlay" id="topDrawer">
        <div class="form-drawer-container form-drawer-top">
            <div class="form-drawer-header">
                <div class="form-drawer-title">顶部抽屉</div>
                <button class="form-drawer-close" onclick="hideDrawer('topDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <div class="drawer-content">
                    <h4>通知中心</h4>
                    <p>顶部抽屉适合展示通知、消息或其他临时信息。</p>
                    <p>这种布局在移动端特别有用，可以充分利用屏幕宽度。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部抽屉 -->
    <div class="form-drawer-overlay" id="bottomDrawer">
        <div class="form-drawer-container form-drawer-bottom">
            <div class="form-drawer-header">
                <div class="form-drawer-title">底部抽屉</div>
                <button class="form-drawer-close" onclick="hideDrawer('bottomDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <div class="drawer-content">
                    <h4>操作面板</h4>
                    <p>底部抽屉常用于移动端的操作面板、分享菜单等场景。</p>
                    <p>这种设计符合移动端用户的操作习惯，便于单手操作。</p>
                </div>
            </div>
            <div class="form-drawer-footer">
                <button class="form-button form-button-secondary" onclick="hideDrawer('bottomDrawer')">取消</button>
                <button class="form-button form-button-primary">确定</button>
            </div>
        </div>
    </div>

    <!-- 表单抽屉 -->
    <div class="form-drawer-overlay" id="formDrawer">
        <div class="form-drawer-container form-drawer-right">
            <div class="form-drawer-header">
                <div class="form-drawer-title">新建用户</div>
                <button class="form-drawer-close" onclick="hideDrawer('formDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <form class="drawer-form">
                    <div class="drawer-form-group">
                        <label class="drawer-form-label">用户名</label>
                        <input type="text" class="drawer-form-input" placeholder="请输入用户名">
                    </div>
                    <div class="drawer-form-group">
                        <label class="drawer-form-label">邮箱</label>
                        <input type="email" class="drawer-form-input" placeholder="请输入邮箱地址">
                    </div>
                    <div class="drawer-form-group">
                        <label class="drawer-form-label">角色</label>
                        <select class="drawer-form-input">
                            <option>管理员</option>
                            <option>编辑者</option>
                            <option>查看者</option>
                        </select>
                    </div>
                    <div class="drawer-form-group">
                        <label class="drawer-form-label">备注</label>
                        <textarea class="drawer-form-input drawer-form-textarea" placeholder="请输入备注信息"></textarea>
                    </div>
                </form>
            </div>
            <div class="form-drawer-footer">
                <button class="form-button form-button-secondary" onclick="hideDrawer('formDrawer')">取消</button>
                <button class="form-button form-button-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 菜单抽屉 -->
    <div class="form-drawer-overlay" id="menuDrawer">
        <div class="form-drawer-container form-drawer-left">
            <div class="form-drawer-header">
                <div class="form-drawer-title">导航菜单</div>
                <button class="form-drawer-close" onclick="hideDrawer('menuDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <ul class="drawer-menu">
                    <li class="drawer-menu-item">
                        <a href="#" class="drawer-menu-link">
                            <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3,13H11V3H3M3,21H11V15H3M13,21H21V11H13M13,3V9H21V3"/>
                            </svg>
                            仪表盘
                        </a>
                    </li>
                    <li class="drawer-menu-item">
                        <a href="#" class="drawer-menu-link">
                            <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16,4C16.88,4 17.67,4.84 17.67,5.84C17.67,6.84 16.88,7.68 16,7.68C15.12,7.68 14.33,6.84 14.33,5.84C14.33,4.84 15.12,4 16,4M16,8.5C18.07,8.5 19.75,6.73 19.75,4.5C19.75,2.27 18.07,0.5 16,0.5C13.93,0.5 12.25,2.27 12.25,4.5C12.25,6.73 13.93,8.5 16,8.5M16,10.5C13.93,10.5 12.25,12.27 12.25,14.5C12.25,16.73 13.93,18.5 16,18.5C18.07,18.5 19.75,16.73 19.75,14.5C19.75,12.27 18.07,10.5 16,10.5M16,17C15.12,17 14.33,16.16 14.33,15.16C14.33,14.16 15.12,13.32 16,13.32C16.88,13.32 17.67,14.16 17.67,15.16C17.67,16.16 16.88,17 16,17M8,4C8.88,4 9.67,4.84 9.67,5.84C9.67,6.84 8.88,7.68 8,7.68C7.12,7.68 6.33,6.84 6.33,5.84C6.33,4.84 7.12,4 8,4M8,8.5C10.07,8.5 11.75,6.73 11.75,4.5C11.75,2.27 10.07,0.5 8,0.5C5.93,0.5 4.25,2.27 4.25,4.5C4.25,6.73 5.93,8.5 8,8.5M8,10.5C5.93,10.5 4.25,12.27 4.25,14.5C4.25,16.73 5.93,18.5 8,18.5C10.07,18.5 11.75,16.73 11.75,14.5C11.75,12.27 10.07,10.5 8,10.5M8,17C7.12,17 6.33,16.16 6.33,15.16C6.33,14.16 7.12,13.32 8,13.32C8.88,13.32 9.67,14.16 9.67,15.16C9.67,16.16 8.88,17 8,17Z"/>
                            </svg>
                            用户管理
                        </a>
                    </li>
                    <li class="drawer-menu-item">
                        <a href="#" class="drawer-menu-link">
                            <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
                            </svg>
                            内容管理
                        </a>
                    </li>
                    <li class="drawer-menu-item">
                        <a href="#" class="drawer-menu-link">
                            <svg class="drawer-menu-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                            </svg>
                            系统设置
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 信息抽屉 -->
    <div class="form-drawer-overlay" id="infoDrawer">
        <div class="form-drawer-container form-drawer-right">
            <div class="form-drawer-header">
                <div class="form-drawer-title">详细信息</div>
                <button class="form-drawer-close" onclick="hideDrawer('infoDrawer')">
                    <svg class="form-drawer-close-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="form-drawer-body">
                <div class="drawer-content">
                    <h4>组件特性</h4>
                    <p>抽屉组件是一个非常实用的UI组件，具有以下特性：</p>
                    <ul>
                        <li><strong>多方向支持</strong>：支持从上、下、左、右四个方向滑出</li>
                        <li><strong>响应式设计</strong>：在移动端自动适配屏幕尺寸</li>
                        <li><strong>灵活内容</strong>：可以放置任何类型的内容</li>
                        <li><strong>交互友好</strong>：支持点击遮罩关闭、ESC键关闭</li>
                        <li><strong>动画流畅</strong>：使用CSS3动画，性能优秀</li>
                    </ul>
                    
                    <h4>使用场景</h4>
                    <p>抽屉组件适用于以下场景：</p>
                    <ul>
                        <li>导航菜单</li>
                        <li>表单填写</li>
                        <li>详细信息展示</li>
                        <li>设置面板</li>
                        <li>筛选器</li>
                        <li>购物车</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示抽屉
        function showDrawer(type) {
            let drawerId;
            
            switch (type) {
                case 'right':
                    drawerId = 'rightDrawer';
                    break;
                case 'left':
                    drawerId = 'leftDrawer';
                    break;
                case 'top':
                    drawerId = 'topDrawer';
                    break;
                case 'bottom':
                    drawerId = 'bottomDrawer';
                    break;
                case 'form':
                    drawerId = 'formDrawer';
                    break;
                case 'menu':
                    drawerId = 'menuDrawer';
                    break;
                case 'info':
                    drawerId = 'infoDrawer';
                    break;
                default:
                    return;
            }
            
            const drawer = document.getElementById(drawerId);
            if (drawer) {
                drawer.classList.add('show');
                document.body.style.overflow = 'hidden';
                
                // 添加ESC键关闭功能
                document.addEventListener('keydown', handleEscKey);
                
                // 点击遮罩关闭
                drawer.addEventListener('click', handleOverlayClick);
            }
        }

        // 隐藏抽屉
        function hideDrawer(drawerId) {
            const drawer = document.getElementById(drawerId);
            if (drawer) {
                drawer.classList.remove('show');
                document.body.style.overflow = '';
                
                // 移除事件监听
                document.removeEventListener('keydown', handleEscKey);
                drawer.removeEventListener('click', handleOverlayClick);
            }
        }

        // ESC键关闭
        function handleEscKey(e) {
            if (e.key === 'Escape') {
                const openDrawer = document.querySelector('.form-drawer-overlay.show');
                if (openDrawer) {
                    hideDrawer(openDrawer.id);
                }
            }
        }

        // 点击遮罩关闭
        function handleOverlayClick(e) {
            if (e.target === e.currentTarget) {
                hideDrawer(e.currentTarget.id);
            }
        }

        // 阻止抽屉内容区域的点击事件冒泡
        document.addEventListener('DOMContentLoaded', () => {
            const drawerContainers = document.querySelectorAll('.form-drawer-container');
            drawerContainers.forEach(container => {
                container.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            });
        });
    </script>
</body>
</html>
