<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签选择器组件</title>
</head>
<body>
    <!-- 标签选择器组件 -->
    <div class="form-tag-input">
        <style>
            .form-tag-input {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-tag-group {
                margin-bottom: 20px;
            }

            .form-tag-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            .form-tag-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-tag-container {
                position: relative;
                min-height: 44px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                background-color: #ffffff;
                padding: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                align-items: center;
                transition: all 0.2s ease;
                cursor: text;
            }

            .form-tag-container:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-tag-container:hover:not(:focus-within) {
                border-color: #9ca3af;
            }

            .form-tag-item {
                display: inline-flex;
                align-items: center;
                background-color: #f3f4f6;
                color: #374151;
                padding: 4px 8px;
                border-radius: 6px;
                font-size: 14px;
                line-height: 1.2;
                max-width: 200px;
                user-select: none;
                transition: all 0.2s ease;
            }

            .form-tag-item:hover {
                background-color: #e5e7eb;
            }

            .form-tag-text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .form-tag-remove {
                margin-left: 6px;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #9ca3af;
                color: white;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                line-height: 1;
                transition: background-color 0.2s ease;
                flex-shrink: 0;
            }

            .form-tag-remove:hover {
                background-color: #6b7280;
            }

            .form-tag-input-field {
                border: none;
                outline: none;
                background: transparent;
                font-size: 14px;
                color: #374151;
                flex: 1;
                min-width: 120px;
                padding: 4px 0;
            }

            .form-tag-input-field::placeholder {
                color: #9ca3af;
            }

            .form-tag-suggestions {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 2px solid #e5e7eb;
                border-top: none;
                border-radius: 0 0 8px 8px;
                max-height: 200px;
                overflow-y: auto;
                z-index: 10;
                display: none;
            }

            .form-tag-suggestions.show {
                display: block;
            }

            .form-tag-suggestion {
                padding: 8px 12px;
                cursor: pointer;
                font-size: 14px;
                color: #374151;
                border-bottom: 1px solid #f3f4f6;
                transition: background-color 0.2s ease;
            }

            .form-tag-suggestion:hover,
            .form-tag-suggestion.highlighted {
                background-color: #f3f4f6;
            }

            .form-tag-suggestion:last-child {
                border-bottom: none;
            }

            .form-tag-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-tag-message.error {
                color: #ef4444;
            }

            .form-tag-message.success {
                color: #10b981;
            }

            .form-tag-message.help {
                color: #6b7280;
            }

            /* 错误状态 */
            .form-tag-container.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 成功状态 */
            .form-tag-container.success {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            /* 不同颜色主题的标签 */
            .form-tag-blue .form-tag-item {
                background-color: #dbeafe;
                color: #1e40af;
            }

            .form-tag-blue .form-tag-item:hover {
                background-color: #bfdbfe;
            }

            .form-tag-green .form-tag-item {
                background-color: #d1fae5;
                color: #065f46;
            }

            .form-tag-green .form-tag-item:hover {
                background-color: #a7f3d0;
            }

            .form-tag-purple .form-tag-item {
                background-color: #e9d5ff;
                color: #6b21a8;
            }

            .form-tag-purple .form-tag-item:hover {
                background-color: #ddd6fe;
            }

            .form-tag-red .form-tag-item {
                background-color: #fee2e2;
                color: #991b1b;
            }

            .form-tag-red .form-tag-item:hover {
                background-color: #fecaca;
            }

            /* 紧凑样式 */
            .form-tag-compact .form-tag-container {
                min-height: 36px;
                padding: 6px;
            }

            .form-tag-compact .form-tag-item {
                padding: 2px 6px;
                font-size: 12px;
            }

            .form-tag-compact .form-tag-remove {
                width: 14px;
                height: 14px;
                margin-left: 4px;
                font-size: 10px;
            }

            .form-tag-compact .form-tag-input-field {
                font-size: 12px;
                min-width: 100px;
            }

            /* 只读状态 */
            .form-tag-readonly .form-tag-container {
                background-color: #f9fafb;
                cursor: default;
            }

            .form-tag-readonly .form-tag-input-field {
                display: none;
            }

            .form-tag-readonly .form-tag-remove {
                display: none;
            }

            /* 预设标签选择 */
            .form-tag-presets {
                margin-top: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
            }

            .form-tag-preset {
                padding: 4px 8px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: #ffffff;
                color: #374151;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                user-select: none;
            }

            .form-tag-preset:hover {
                border-color: #3b82f6;
                background-color: #f0f9ff;
                color: #3b82f6;
            }

            .form-tag-preset.selected {
                border-color: #3b82f6;
                background-color: #3b82f6;
                color: white;
            }

            @media (max-width: 480px) {
                .form-tag-input {
                    margin: 12px 0;
                }

                .form-tag-container {
                    min-height: 48px;
                    padding: 10px;
                }

                .form-tag-input-field {
                    font-size: 16px;
                    min-width: 100px;
                }

                .form-tag-suggestions {
                    max-height: 150px;
                }

                .form-tag-presets {
                    justify-content: center;
                }
            }
        </style>

        <!-- 基础标签输入 -->
        <div class="form-tag-group">
            <label class="form-tag-label">
                技能标签 <span class="form-tag-required">*</span>
            </label>
            <div class="form-tag-container" data-tag-input="skills">
                <input type="text" class="form-tag-input-field" placeholder="输入技能并按回车添加">
            </div>
            <div class="form-tag-message help">输入您擅长的技能，按回车键添加标签</div>
            <div class="form-tag-presets">
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'JavaScript')">JavaScript</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'Python')">Python</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'React')">React</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'Vue.js')">Vue.js</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'Node.js')">Node.js</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'CSS')">CSS</span>
                <span class="form-tag-preset" onclick="addPresetTag('skills', 'HTML')">HTML</span>
            </div>
        </div>

        <!-- 带建议的标签输入 -->
        <div class="form-tag-group">
            <label class="form-tag-label">兴趣爱好</label>
            <div class="form-tag-container form-tag-blue" data-tag-input="hobbies">
                <div class="form-tag-item">
                    <span class="form-tag-text">阅读</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">音乐</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <input type="text" class="form-tag-input-field" placeholder="输入兴趣爱好">
                <div class="form-tag-suggestions">
                    <div class="form-tag-suggestion">运动</div>
                    <div class="form-tag-suggestion">旅行</div>
                    <div class="form-tag-suggestion">摄影</div>
                    <div class="form-tag-suggestion">绘画</div>
                    <div class="form-tag-suggestion">游戏</div>
                </div>
            </div>
            <div class="form-tag-message help">输入关键词查看建议，或直接输入新的爱好</div>
        </div>

        <!-- 紧凑样式标签输入 -->
        <div class="form-tag-group">
            <label class="form-tag-label">项目标签</label>
            <div class="form-tag-container form-tag-compact form-tag-green" data-tag-input="projects">
                <div class="form-tag-item">
                    <span class="form-tag-text">前端开发</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">响应式设计</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">用户体验</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <input type="text" class="form-tag-input-field" placeholder="添加标签">
            </div>
        </div>

        <!-- 错误状态示例 -->
        <div class="form-tag-group">
            <label class="form-tag-label">
                必填标签 <span class="form-tag-required">*</span>
            </label>
            <div class="form-tag-container error" data-tag-input="required-tags">
                <input type="text" class="form-tag-input-field" placeholder="至少添加一个标签">
            </div>
            <div class="form-tag-message error">请至少添加一个标签</div>
        </div>

        <!-- 成功状态示例 -->
        <div class="form-tag-group">
            <label class="form-tag-label">语言能力</label>
            <div class="form-tag-container success form-tag-purple" data-tag-input="languages">
                <div class="form-tag-item">
                    <span class="form-tag-text">中文</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">英语</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                </div>
                <input type="text" class="form-tag-input-field" placeholder="添加语言">
            </div>
            <div class="form-tag-message success">语言标签添加完成</div>
        </div>

        <!-- 只读标签显示 -->
        <div class="form-tag-group">
            <label class="form-tag-label">已选择的标签（只读）</label>
            <div class="form-tag-container form-tag-readonly form-tag-red" data-tag-input="readonly-tags">
                <div class="form-tag-item">
                    <span class="form-tag-text">重要</span>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">紧急</span>
                </div>
                <div class="form-tag-item">
                    <span class="form-tag-text">高优先级</span>
                </div>
            </div>
            <div class="form-tag-message help">这些标签是系统自动生成的，不可编辑</div>
        </div>

        <script>
            // 建议数据
            const suggestions = {
                hobbies: ['运动', '旅行', '摄影', '绘画', '游戏', '电影', '音乐', '阅读', '写作', '烹饪', '园艺', '瑜伽'],
                skills: ['JavaScript', 'Python', 'Java', 'React', 'Vue.js', 'Angular', 'Node.js', 'CSS', 'HTML', 'TypeScript', 'PHP', 'Go'],
                projects: ['前端开发', '后端开发', '移动应用', '桌面应用', '数据分析', '机器学习', '人工智能', '区块链', '物联网', '云计算'],
                languages: ['中文', '英语', '日语', '韩语', '法语', '德语', '西班牙语', '俄语', '阿拉伯语', '意大利语']
            };

            // 初始化标签输入
            function initTagInputs() {
                const tagContainers = document.querySelectorAll('[data-tag-input]');
                
                tagContainers.forEach(container => {
                    const input = container.querySelector('.form-tag-input-field');
                    const suggestionsEl = container.querySelector('.form-tag-suggestions');
                    const tagType = container.getAttribute('data-tag-input');
                    
                    if (!input) return;
                    
                    // 输入事件
                    input.addEventListener('input', (e) => {
                        const value = e.target.value.trim();
                        if (value && suggestionsEl) {
                            showSuggestions(container, value, tagType);
                        } else if (suggestionsEl) {
                            hideSuggestions(container);
                        }
                    });
                    
                    // 键盘事件
                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            const value = input.value.trim();
                            if (value) {
                                addTag(container, value);
                                input.value = '';
                                hideSuggestions(container);
                            }
                        } else if (e.key === 'Backspace' && input.value === '') {
                            // 删除最后一个标签
                            const lastTag = container.querySelector('.form-tag-item:last-of-type');
                            if (lastTag) {
                                removeTag(lastTag.querySelector('.form-tag-remove'));
                            }
                        } else if (e.key === 'Escape') {
                            hideSuggestions(container);
                        } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                            e.preventDefault();
                            navigateSuggestions(container, e.key === 'ArrowDown');
                        }
                    });
                    
                    // 失焦事件
                    input.addEventListener('blur', (e) => {
                        // 延迟隐藏建议，允许点击建议项
                        setTimeout(() => {
                            hideSuggestions(container);
                        }, 200);
                    });
                    
                    // 容器点击事件
                    container.addEventListener('click', (e) => {
                        if (e.target === container || e.target.classList.contains('form-tag-input-field')) {
                            input.focus();
                        }
                    });
                });
            }
            
            // 添加标签
            function addTag(container, text) {
                // 检查是否已存在
                const existingTags = container.querySelectorAll('.form-tag-text');
                for (let tag of existingTags) {
                    if (tag.textContent === text) {
                        return; // 标签已存在
                    }
                }
                
                const tagElement = document.createElement('div');
                tagElement.className = 'form-tag-item';
                tagElement.innerHTML = `
                    <span class="form-tag-text">${text}</span>
                    <button class="form-tag-remove" onclick="removeTag(this)">×</button>
                `;
                
                const input = container.querySelector('.form-tag-input-field');
                container.insertBefore(tagElement, input);
                
                // 清除错误状态
                container.classList.remove('error');
                const errorMessage = container.parentElement.querySelector('.form-tag-message.error');
                if (errorMessage) {
                    errorMessage.classList.remove('error');
                    errorMessage.classList.add('help');
                    errorMessage.textContent = '';
                }
                
                // 触发变化事件
                triggerTagChange(container);
            }
            
            // 删除标签
            function removeTag(button) {
                const tagItem = button.parentElement;
                const container = tagItem.parentElement;
                tagItem.remove();
                
                // 触发变化事件
                triggerTagChange(container);
            }
            
            // 显示建议
            function showSuggestions(container, query, tagType) {
                const suggestionsEl = container.querySelector('.form-tag-suggestions');
                if (!suggestionsEl || !suggestions[tagType]) return;
                
                const filtered = suggestions[tagType].filter(item => 
                    item.toLowerCase().includes(query.toLowerCase())
                );
                
                if (filtered.length === 0) {
                    hideSuggestions(container);
                    return;
                }
                
                suggestionsEl.innerHTML = filtered.map(item => 
                    `<div class="form-tag-suggestion" onclick="selectSuggestion(this, '${item}')">${item}</div>`
                ).join('');
                
                suggestionsEl.classList.add('show');
            }
            
            // 隐藏建议
            function hideSuggestions(container) {
                const suggestionsEl = container.querySelector('.form-tag-suggestions');
                if (suggestionsEl) {
                    suggestionsEl.classList.remove('show');
                }
            }
            
            // 选择建议
            function selectSuggestion(element, text) {
                const container = element.closest('[data-tag-input]');
                const input = container.querySelector('.form-tag-input-field');
                
                addTag(container, text);
                input.value = '';
                hideSuggestions(container);
                input.focus();
            }
            
            // 导航建议
            function navigateSuggestions(container, down) {
                const suggestionsEl = container.querySelector('.form-tag-suggestions');
                if (!suggestionsEl || !suggestionsEl.classList.contains('show')) return;
                
                const suggestions = suggestionsEl.querySelectorAll('.form-tag-suggestion');
                const current = suggestionsEl.querySelector('.form-tag-suggestion.highlighted');
                
                let index = -1;
                if (current) {
                    index = Array.from(suggestions).indexOf(current);
                    current.classList.remove('highlighted');
                }
                
                if (down) {
                    index = (index + 1) % suggestions.length;
                } else {
                    index = index <= 0 ? suggestions.length - 1 : index - 1;
                }
                
                suggestions[index].classList.add('highlighted');
            }
            
            // 添加预设标签
            function addPresetTag(tagType, text) {
                const container = document.querySelector(`[data-tag-input="${tagType}"]`);
                if (container) {
                    addTag(container, text);
                    
                    // 更新预设按钮状态
                    const preset = event.target;
                    preset.classList.add('selected');
                    setTimeout(() => {
                        preset.classList.remove('selected');
                    }, 300);
                }
            }
            
            // 获取标签值
            function getTagValues(tagType) {
                const container = document.querySelector(`[data-tag-input="${tagType}"]`);
                if (!container) return [];
                
                const tags = container.querySelectorAll('.form-tag-text');
                return Array.from(tags).map(tag => tag.textContent);
            }
            
            // 设置标签值
            function setTagValues(tagType, values) {
                const container = document.querySelector(`[data-tag-input="${tagType}"]`);
                if (!container) return;
                
                // 清除现有标签
                const existingTags = container.querySelectorAll('.form-tag-item');
                existingTags.forEach(tag => tag.remove());
                
                // 添加新标签
                values.forEach(value => {
                    addTag(container, value);
                });
            }
            
            // 验证标签
            function validateTags(tagType, minCount = 0, maxCount = Infinity) {
                const values = getTagValues(tagType);
                const container = document.querySelector(`[data-tag-input="${tagType}"]`);
                
                if (values.length < minCount) {
                    container.classList.add('error');
                    return false;
                } else if (values.length > maxCount) {
                    container.classList.add('error');
                    return false;
                } else {
                    container.classList.remove('error');
                    return true;
                }
            }
            
            // 触发变化事件
            function triggerTagChange(container) {
                const event = new CustomEvent('tagchange', {
                    detail: {
                        tags: getTagValues(container.getAttribute('data-tag-input')),
                        container: container
                    }
                });
                container.dispatchEvent(event);
            }
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', () => {
                initTagInputs();
                
                // 监听标签变化事件
                document.addEventListener('tagchange', (e) => {
                    console.log('标签已更改:', e.detail.tags);
                });
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础标签输入 --&gt;
&lt;div class="form-tag-group"&gt;
    &lt;label class="form-tag-label"&gt;
        技能标签 &lt;span class="form-tag-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-tag-container" data-tag-input="skills"&gt;
        &lt;input type="text" class="form-tag-input-field" placeholder="输入技能并按回车添加"&gt;
    &lt;/div&gt;
    &lt;div class="form-tag-message help"&gt;输入您擅长的技能&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 带预设标签的输入 --&gt;
&lt;div class="form-tag-presets"&gt;
    &lt;span class="form-tag-preset" onclick="addPresetTag('skills', 'JavaScript')"&gt;JavaScript&lt;/span&gt;
    &lt;span class="form-tag-preset" onclick="addPresetTag('skills', 'Python')"&gt;Python&lt;/span&gt;
&lt;/div&gt;

&lt;!-- 带建议的标签输入 --&gt;
&lt;div class="form-tag-container" data-tag-input="hobbies"&gt;
    &lt;input type="text" class="form-tag-input-field" placeholder="输入兴趣爱好"&gt;
    &lt;div class="form-tag-suggestions"&gt;
        &lt;div class="form-tag-suggestion"&gt;运动&lt;/div&gt;
        &lt;div class="form-tag-suggestion"&gt;旅行&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 已有标签 --&gt;
&lt;div class="form-tag-container" data-tag-input="existing"&gt;
    &lt;div class="form-tag-item"&gt;
        &lt;span class="form-tag-text"&gt;JavaScript&lt;/span&gt;
        &lt;button class="form-tag-remove" onclick="removeTag(this)"&gt;×&lt;/button&gt;
    &lt;/div&gt;
    &lt;input type="text" class="form-tag-input-field" placeholder="添加更多标签"&gt;
&lt;/div&gt;

&lt;!-- 不同颜色主题 --&gt;
&lt;div class="form-tag-container form-tag-blue"&gt;&lt;!-- 蓝色主题 --&gt;&lt;/div&gt;
&lt;div class="form-tag-container form-tag-green"&gt;&lt;!-- 绿色主题 --&gt;&lt;/div&gt;
&lt;div class="form-tag-container form-tag-purple"&gt;&lt;!-- 紫色主题 --&gt;&lt;/div&gt;
&lt;div class="form-tag-container form-tag-red"&gt;&lt;!-- 红色主题 --&gt;&lt;/div&gt;

&lt;!-- 紧凑样式 --&gt;
&lt;div class="form-tag-container form-tag-compact"&gt;&lt;!-- 紧凑样式 --&gt;&lt;/div&gt;

&lt;!-- 只读状态 --&gt;
&lt;div class="form-tag-container form-tag-readonly"&gt;&lt;!-- 只读状态 --&gt;&lt;/div&gt;

&lt;!-- 错误状态 --&gt;
&lt;div class="form-tag-container error"&gt;&lt;!-- 错误状态 --&gt;&lt;/div&gt;

&lt;!-- 成功状态 --&gt;
&lt;div class="form-tag-container success"&gt;&lt;!-- 成功状态 --&gt;&lt;/div&gt;</code></pre>
    </div>
</body>
</html>