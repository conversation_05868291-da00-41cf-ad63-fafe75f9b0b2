# H5表单组件库

一套简单、可复用、移动端自适应的HTML/CSS表单组件集合，专为H5开发设计。

## ✨ 特性

- 🎯 **完全独立** - 每个组件都是自包含的，无外部依赖
- 📱 **移动端优化** - 完美适配H5移动端，支持触控操作
- 📐 **响应式设计** - 自动适应不同屏幕尺寸（最大支持480px）
- ♿ **可访问性友好** - 支持键盘导航和屏幕阅读器
- 🎨 **现代简洁** - 美观的视觉设计，支持多种状态
- 🔧 **易于集成** - 直接复制粘贴即可使用
- 🌐 **浏览器兼容** - 兼容Chrome、Safari、Firefox等主流浏览器

## 📁 文件结构

```
h5-form-components/
├── index.html          # 综合示例页面（推荐先查看）
├── checkbox.html       # 复选框组件
├── radio.html          # 单选按钮组件  
├── buttons.html        # 按钮变体组件
├── text-input.html     # 文本输入框组件
├── select.html         # 下拉选择框组件
├── textarea.html       # 多行文本框组件
├── datetime.html       # 日期时间选择器组件
├── rating.html         # 星级评分组件
├── fieldset.html       # 分组字段组件
├── progress.html       # 进度条组件
├── tag-input.html      # 标签选择器组件
├── modal.html          # 弹窗组件库
└── README.md          # 项目说明文档
```

## 🚀 快速开始

1. **查看综合示例**
   ```bash
   # 在浏览器中打开
   open index.html
   ```

2. **使用单个组件**
   - 打开对应的组件文件（如 `checkbox.html`）
   - 复制HTML结构和CSS样式
   - 根据需要修改类名和内容
   - 集成到您的项目中

## 📋 组件列表

### 1. 复选框组件 (checkbox.html)
- **描述**: 支持多选的复选框组件，包含自定义样式和标签
- **类名**: `.form-checkbox`、`.form-checkbox-horizontal`
- **特性**: 自定义选中样式、悬停效果、焦点状态、横排布局、按钮样式

```html
<!-- 垂直布局复选框 -->
<div class="form-checkbox">
    <div class="form-checkbox-title">选择您的兴趣爱好</div>
    <label class="form-checkbox-item">
        <input type="checkbox" name="hobbies" value="reading">
        <span class="form-checkbox-custom"></span>
        <span class="form-checkbox-label">阅读</span>
    </label>
</div>

<!-- 横排布局复选框 -->
<div class="form-checkbox">
    <div class="form-checkbox-title">选择技能（横排显示）</div>
    <div class="form-checkbox-horizontal">
        <label class="form-checkbox-item">
            <input type="checkbox" name="skills" value="html">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">HTML</span>
        </label>
        <label class="form-checkbox-item">
            <input type="checkbox" name="skills" value="css">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">CSS</span>
        </label>
    </div>
</div>

<!-- 按钮样式横排 -->
<div class="form-checkbox-horizontal form-checkbox-button-style">
    <label class="form-checkbox-item">
        <input type="checkbox" name="topics" value="technology">
        <span class="form-checkbox-label">科技</span>
    </label>
</div>
```

### 2. 单选按钮组件 (radio.html)
- **描述**: 分组的单选选项，仅允许选择一个
- **类名**: `.form-radio`、`.form-radio-horizontal`
- **特性**: 圆形自定义样式、分组选择、状态反馈、横排布局、按钮和卡片样式

```html
<!-- 垂直布局单选按钮 -->
<div class="form-radio">
    <div class="form-radio-title">选择您的性别</div>
    <label class="form-radio-item">
        <input type="radio" name="gender" value="male">
        <span class="form-radio-custom"></span>
        <span class="form-radio-label">男性</span>
    </label>
</div>

<!-- 横排布局单选按钮 -->
<div class="form-radio">
    <div class="form-radio-title">选择年龄段（横排显示）</div>
    <div class="form-radio-horizontal">
        <label class="form-radio-item">
            <input type="radio" name="age" value="18-25">
            <span class="form-radio-custom"></span>
            <span class="form-radio-label">18-25岁</span>
        </label>
        <label class="form-radio-item">
            <input type="radio" name="age" value="26-35">
            <span class="form-radio-custom"></span>
            <span class="form-radio-label">26-35岁</span>
        </label>
    </div>
</div>

<!-- 按钮样式横排 -->
<div class="form-radio-horizontal form-radio-button-style">
    <label class="form-radio-item">
        <input type="radio" name="plan" value="basic">
        <span class="form-radio-label">基础版</span>
    </label>
</div>

<!-- 卡片样式横排 -->
<div class="form-radio-horizontal form-radio-card-style">
    <label class="form-radio-item">
        <input type="radio" name="payment" value="alipay">
        <span class="form-radio-custom"></span>
        <span class="form-radio-label">支付宝</span>
    </label>
</div>
```

### 3. 按钮变体组件 (buttons.html)
- **描述**: 三种类型的按钮：主要、次要、危险
- **类名**: `.form-button`、`.form-button-primary`、`.form-button-secondary`、`.form-button-danger`
- **特性**: 悬停动画、禁用状态、移动端全宽

```html
<!-- 主要按钮 -->
<button class="form-button form-button-primary" type="submit">
    新增
</button>

<!-- 次要按钮 -->
<button class="form-button form-button-secondary" type="button">
    取消
</button>

<!-- 危险按钮 -->
<button class="form-button form-button-danger" type="button">
    删除
</button>
```

### 4. 文本输入框组件 (text-input.html)
- **描述**: 带标签和验证状态的文本输入框
- **类名**: `.form-input`、`.form-input-group`
- **特性**: 错误/成功状态、占位符、帮助信息

```html
<div class="form-input-group">
    <label class="form-input-label" for="username">
        用户名 <span class="form-input-required">*</span>
    </label>
    <input 
        type="text" 
        id="username" 
        name="username" 
        class="form-input" 
        placeholder="请输入您的用户名"
        required
    >
    <div class="form-input-message help">用户名长度应为3-20个字符</div>
</div>
```

### 5. 下拉选择框组件 (select.html)
- **描述**: 优化样式的原生select元素，支持分组选项
- **类名**: `.form-select`、`.form-select-input`
- **特性**: 自定义箭头、展开动画、选项分组

```html
<div class="form-select-group">
    <label class="form-select-label" for="city">
        所在城市 <span class="form-input-required">*</span>
    </label>
    <div class="form-select-wrapper">
        <select id="city" name="city" class="form-select-input" required>
            <option value="" disabled selected>请选择您的城市</option>
            <option value="beijing">北京</option>
            <option value="shanghai">上海</option>
        </select>
        <div class="form-select-arrow"></div>
    </div>
</div>
```

### 6. 多行文本框组件 (textarea.html)
- **描述**: 支持多行输入的文本区域，可自适应高度
- **类名**: `.form-textarea`、`.form-textarea-input`
- **特性**: 自动调整高度、字符计数、固定高度选项

### 7. 日期时间选择器组件 (datetime.html)
- **描述**: 支持日期、时间、日期时间选择的组件集合
- **类名**: `.form-datetime`、`.form-datetime-input`
- **特性**: 原生HTML5输入类型、自定义图标、范围选择、快捷操作、移动端优化

### 8. 星级评分组件 (rating.html)
- **描述**: 交互式星级评分输入组件
- **类名**: `.form-rating`、`.form-rating-stars`
- **特性**: 悬停预览、多种尺寸、颜色主题、只读模式、键盘导航

### 9. 分组字段组件 (fieldset.html)
- **描述**: 表单字段分组和组织容器
- **类名**: `.form-fieldset`、`.form-fieldset-container`
- **特性**: 可折叠、多列布局、主题样式、焦点管理、卡片样式

### 10. 进度条组件 (progress.html)
- **描述**: 多样式进度指示器组件集合
- **类名**: `.form-progress`、`.form-progress-bar`
- **特性**: 线性进度条、圆形进度条、步骤进度条、动画效果、多种颜色主题

### 11. 标签选择器组件 (tag-input.html)
- **描述**: 动态标签输入和管理组件
- **类名**: `.form-tag-input`、`.form-tag-container`
- **特性**: 动态添加删除、预设标签、自动建议、键盘导航、多种颜色主题

### 12. 弹窗组件库 (modal.html)
- **描述**: 完整的弹窗/对话框组件库，包含多种预设样式和功能
- **类名**: `.modal-overlay`、`.modal-container`、`.modal-header`、`.modal-body`、`.modal-footer`
- **特性**: 10种弹窗类型、响应式设计、动画效果、键盘导航、遮罩关闭、API调用

```html
<div class="form-textarea-group">
    <label class="form-textarea-label" for="description">
        项目描述 <span class="form-textarea-required">*</span>
    </label>
    <textarea 
        id="description" 
        name="description" 
        class="form-textarea-input" 
        placeholder="请详细描述您的项目内容..."
        required
    ></textarea>
    <div class="form-textarea-message help">请详细描述项目的背景和目标</div>
</div>
```

## 🎨 状态样式

所有输入组件都支持以下状态：

- **默认状态**: 正常的输入状态
- **焦点状态**: 获得焦点时的高亮效果
- **悬停状态**: 鼠标悬停时的视觉反馈
- **错误状态**: 添加 `.error` 类显示错误状态
- **成功状态**: 添加 `.success` 类显示成功状态

```html
<!-- 错误状态 -->
<input class="form-input error" ...>
<div class="form-input-message error">错误信息</div>

<!-- 成功状态 -->
<input class="form-input success" ...>
<div class="form-input-message success">成功信息</div>
```

## 📱 移动端适配

组件针对移动端进行了特别优化：

- **触控友好**: 最小44px的触控区域
- **自适应宽度**: 移动端组件占满容器宽度
- **合适的字体大小**: 16px防止iOS自动缩放
- **响应式布局**: 使用媒体查询适配小屏幕

```css
@media (max-width: 480px) {
    .form-button {
        width: 100%;
        min-height: 48px;
        font-size: 16px;
    }
}
```

## 🔧 自定义配置

### 修改主色调
```css
/* 将所有 #3b82f6 替换为您的品牌色 */
.form-input:focus {
    border-color: #your-brand-color;
    box-shadow: 0 0 0 3px rgba(your-rgb-values, 0.1);
}
```

### 调整组件间距
```css
.form-input-group {
    margin-bottom: 24px; /* 默认为20px */
}
```

### 修改边框圆角
```css
.form-input, .form-button {
    border-radius: 12px; /* 默认为8px */
}
```

## 🌐 浏览器兼容性

- ✅ Chrome 60+
- ✅ Safari 12+
- ✅ Firefox 60+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## 📝 使用注意事项

1. **CSS作用域**: 建议将组件样式包装在特定的命名空间中，防止样式冲突
2. **表单验证**: 组件提供视觉样式，需要自行实现JavaScript验证逻辑
3. **无障碍访问**: 确保为表单元素提供适当的`label`和`aria-*`属性
4. **性能优化**: 对于大量表单项，考虑按需加载CSS样式

## 🤝 贡献指南

欢迎提交问题和改进建议！在提交前请确保：

1. 测试在主流浏览器中的兼容性
2. 验证移动端的显示效果
3. 确保代码符合无障碍访问标准
4. 保持组件的独立性和可复用性

## 📄 许可证

MIT License - 可自由使用、修改和分发。

---

## 📅 日期时间组件示例

```html
<!-- 日期选择器 -->
<div class="form-datetime-group">
    <label class="form-datetime-label" for="birth-date">
        出生日期 <span class="form-datetime-required">*</span>
    </label>
    <div class="form-datetime-wrapper">
        <input 
            type="date" 
            id="birth-date" 
            name="birth-date" 
            class="form-datetime-input" 
            required
        >
    </div>
    <div class="form-datetime-message help">请选择您的出生日期</div>
</div>

<!-- 时间选择器 -->
<div class="form-datetime-group">
    <label class="form-datetime-label" for="meeting-time">
        会议时间
    </label>
    <div class="form-datetime-wrapper">
        <input 
            type="time" 
            id="meeting-time" 
            name="meeting-time" 
            class="form-datetime-input"
            value="14:30"
        >
    </div>
</div>

<!-- 日期时间选择器 -->
<div class="form-datetime-group">
    <label class="form-datetime-label" for="appointment">
        预约时间 <span class="form-datetime-required">*</span>
    </label>
    <div class="form-datetime-wrapper">
        <input 
            type="datetime-local" 
            id="appointment" 
            name="appointment" 
            class="form-datetime-input" 
            required
        >
    </div>
</div>

<!-- 日期范围选择 -->
<div class="form-datetime-group">
    <label class="form-datetime-label">
        请假期间 <span class="form-datetime-required">*</span>
    </label>
    <div class="form-datetime-range">
        <div class="form-datetime-wrapper">
            <input type="date" id="leave-start" name="leave-start" class="form-datetime-input" required>
        </div>
        <div class="form-datetime-separator">至</div>
        <div class="form-datetime-wrapper">
            <input type="date" id="leave-end" name="leave-end" class="form-datetime-input" required>
        </div>
    </div>
</div>

<!-- 带快捷选择的日期选择器 -->
<div class="form-datetime-group">
    <label class="form-datetime-label" for="report-date">
        报告日期
    </label>
    <input type="date" id="report-date" name="report-date" class="form-datetime-input">
    <div class="form-datetime-shortcuts">
        <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 0)">
            今天
        </button>
        <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 1)">
            明天
        </button>
        <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 7)">
            下周
        </button>
    </div>
</div>
```

### 日期时间组件特性

1. **多种输入类型**:
   - `type="date"` - 日期选择器
   - `type="time"` - 时间选择器  
   - `type="datetime-local"` - 日期时间选择器

2. **范围选择**: 支持开始和结束日期/时间的范围选择

3. **快捷操作**: 提供今天、明天、下周等快捷选择按钮

4. **验证功能**: 内置JavaScript验证，支持日期范围验证

5. **移动端优化**: 在移动设备上会调起原生的日期时间选择器

6. **自定义图标**: 使用SVG图标替换系统默认图标

## ⭐ 新增组件示例

### 星级评分组件

```html
<!-- 基础星级评分 -->
<div class="form-rating-group">
    <label class="form-rating-label">产品评分</label>
    <div class="form-rating-stars" data-rating="product-rating">
        <label class="form-rating-star">
            <input type="radio" name="product-rating" value="1">
            <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
        </label>
        <!-- 重复4个星星... -->
    </div>
    <div class="form-rating-text">
        <span class="form-rating-current" id="product-rating-text">请选择评分</span>
    </div>
</div>

<!-- 不同尺寸和颜色 -->
<div class="form-rating-stars form-rating-large form-rating-red">
    <!-- 大尺寸红色星星 -->
</div>
```

### 分组字段组件

```html
<!-- 基础分组字段 -->
<div class="form-fieldset-container">
    <legend class="form-fieldset-legend">个人信息</legend>
    <div class="form-fieldset-description">请填写您的基本信息</div>
    <div class="form-fieldset-content">
        <div class="form-fieldset-row two-columns">
            <div class="form-input-group">
                <label class="form-input-label">姓名</label>
                <input type="text" class="form-input" placeholder="请输入姓名">
            </div>
            <div class="form-input-group">
                <label class="form-input-label">邮箱</label>
                <input type="email" class="form-input" placeholder="请输入邮箱">
            </div>
        </div>
    </div>
</div>

<!-- 可折叠分组 -->
<div class="form-fieldset-collapsible" onclick="toggleFieldset(this)">
    <div class="form-fieldset-container">
        <legend class="form-fieldset-legend">
            高级设置
            <div class="form-fieldset-toggle"></div>
        </legend>
        <!-- 内容... -->
    </div>
</div>
```

### 进度条组件

```html
<!-- 线性进度条 -->
<div class="form-progress-group">
    <label class="form-progress-label">上传进度</label>
    <div class="form-progress-container">
        <div class="form-progress-bar" style="width: 65%"></div>
    </div>
    <div class="form-progress-text">
        <span class="form-progress-status">已上传 6.5MB / 10MB</span>
        <span class="form-progress-percentage">65%</span>
    </div>
</div>

<!-- 步骤进度条 -->
<div class="form-progress-steps">
    <div class="form-progress-step completed">
        <div class="form-progress-step-circle">✓</div>
        <div class="form-progress-step-title">步骤一</div>
    </div>
    <div class="form-progress-step active">
        <div class="form-progress-step-circle">2</div>
        <div class="form-progress-step-title">步骤二</div>
    </div>
    <div class="form-progress-step">
        <div class="form-progress-step-circle">3</div>
        <div class="form-progress-step-title">步骤三</div>
    </div>
</div>

<!-- 圆形进度条 -->
<div class="form-progress-circle">
    <div class="form-progress-circle-container">
        <svg class="form-progress-circle-svg" viewBox="0 0 80 80">
            <circle class="form-progress-circle-bg" cx="40" cy="40" r="36"></circle>
            <circle class="form-progress-circle-bar" cx="40" cy="40" r="36" style="stroke-dashoffset: 90.4;"></circle>
        </svg>
        <div class="form-progress-circle-text">60%</div>
    </div>
    <div class="form-progress-circle-info">
        <div class="form-progress-circle-title">学习进度</div>
        <div class="form-progress-circle-desc">已完成 12 / 20 个课程</div>
    </div>
</div>
```

### 标签选择器组件

```html
<!-- 基础标签输入 -->
<div class="form-tag-group">
    <label class="form-tag-label">技能标签</label>
    <div class="form-tag-container" data-tag-input="skills">
        <input type="text" class="form-tag-input-field" placeholder="输入技能并按回车添加">
    </div>
    <div class="form-tag-presets">
        <span class="form-tag-preset" onclick="addPresetTag('skills', 'JavaScript')">JavaScript</span>
        <span class="form-tag-preset" onclick="addPresetTag('skills', 'Python')">Python</span>
    </div>
</div>

<!-- 带已有标签 -->
<div class="form-tag-container form-tag-blue" data-tag-input="hobbies">
    <div class="form-tag-item">
        <span class="form-tag-text">阅读</span>
        <button class="form-tag-remove" onclick="removeTag(this)">×</button>
    </div>
    <div class="form-tag-item">
        <span class="form-tag-text">音乐</span>
        <button class="form-tag-remove" onclick="removeTag(this)">×</button>
    </div>
    <input type="text" class="form-tag-input-field" placeholder="添加更多标签">
</div>

<!-- 不同颜色主题 -->
<div class="form-tag-container form-tag-green"><!-- 绿色主题 --></div>
<div class="form-tag-container form-tag-purple"><!-- 紫色主题 --></div>
<div class="form-tag-container form-tag-red"><!-- 红色主题 --></div>
```

## 🔄 横排布局样式

### 复选框和单选按钮横排样式

我们为复选框和单选按钮组件新增了多种横排布局选项：

#### 📐 布局选项
- **基础横排**: `.form-checkbox-horizontal` / `.form-radio-horizontal`
- **紧凑横排**: 添加 `.form-checkbox-compact` / `.form-radio-compact`
- **两列布局**: 添加 `.form-checkbox-two-columns` / `.form-radio-two-columns`
- **三列布局**: 添加 `.form-checkbox-three-columns` / `.form-radio-three-columns`
- **等宽布局**: 添加 `.form-checkbox-equal` / `.form-radio-equal`

#### 🎨 样式变体
- **按钮样式**: `.form-checkbox-button-style` / `.form-radio-button-style`
- **卡片样式**: `.form-radio-card-style`（仅单选按钮）

#### 📱 响应式特性
- 在移动端（≤480px）自动切换为垂直布局
- 保持44px最小触控区域
- 自适应间距和字体大小

```html
<!-- 横排复选框示例 -->
<div class="form-checkbox-horizontal form-checkbox-two-columns">
    <label class="form-checkbox-item">
        <input type="checkbox" name="skills" value="html">
        <span class="form-checkbox-custom"></span>
        <span class="form-checkbox-label">HTML</span>
    </label>
    <!-- 更多选项... -->
</div>

<!-- 按钮样式横排单选 -->
<div class="form-radio-horizontal form-radio-button-style">
    <label class="form-radio-item">
        <input type="radio" name="plan" value="basic">
        <span class="form-radio-label">基础版</span>
    </label>
    <!-- 更多选项... -->
</div>
```

## 🎯 组件特性总结

### 新增组件亮点

1. **星级评分**:
   - 🌟 SVG星形图标，支持悬停预览
   - 📏 三种尺寸：小、中、大
   - 🎨 多种颜色主题：默认、红色、蓝色等
   - ⌨️ 完整的键盘导航支持
   - 📱 移动端触控优化

2. **分组字段**:
   - 📁 语义化的fieldset结构
   - 🔄 可折叠展开功能
   - 📐 灵活的多列布局（1-3列）
   - 🎨 多种视觉主题：主要、成功、警告、错误
   - 💳 卡片样式和紧凑样式

3. **进度条**:
   - 📊 三种类型：线性、圆形、步骤
   - 🎬 流畅的动画效果
   - 🌈 渐变和多色主题支持
   - 📱 响应式设计
   - ⚡ JavaScript API控制

4. **标签选择器**:
   - 🏷️ 动态添加删除标签
   - 💡 智能建议和预设标签
   - ⌨️ 键盘快捷操作（回车添加、退格删除）
   - 🎨 多种颜色主题
   - 📏 紧凑和只读模式

---

## 🔔 弹窗组件库详解

### 弹窗类型

弹窗组件库提供了10种不同类型的弹窗，满足各种业务场景需求：

#### 1. 警告弹框 (Warning Modal)
```html
<div id="warningModal" class="modal-overlay">
    <div class="modal-container modal-warning">
        <div class="modal-header">
            <h3 class="modal-title">操作警告</h3>
            <button class="modal-close" onclick="hideModal('warningModal')">×</button>
        </div>
        <div class="modal-body">
            <p class="modal-message">您即将执行一个重要操作...</p>
        </div>
        <div class="modal-footer">
            <button class="modal-button modal-button-secondary" onclick="hideModal('warningModal')">取消</button>
            <button class="modal-button modal-button-warning" onclick="confirmWarning()">确认执行</button>
        </div>
    </div>
</div>
```

#### 2. 成功弹框 (Success Modal)
- 自带成功图标和绿色主题
- 支持自动关闭（3秒）
- 适用于操作成功反馈

#### 3. 错误弹框 (Error Modal)
- 红色主题，带错误图标
- 支持错误详情显示
- 提供重试功能按钮

#### 4. 信息弹框 (Info Modal)
- 蓝色主题，用于系统通知
- 支持富文本内容
- 单按钮确认设计

#### 5. 确认弹框 (Confirm Modal)
- 通用确认对话框
- 支持动态内容更新
- 双按钮操作（取消/确认）

#### 6. 表单弹框 (Form Modal)
- 内置表单元素样式
- 支持表单验证
- 适用于快速数据录入

#### 7. 加载弹框 (Loading Modal)
- 半透明背景，居中加载动画
- 支持自定义加载文本
- 自动关闭或手动控制

#### 8. 自定义弹框 (Custom Modal)
- 灵活的内容布局
- 支持复杂UI结构
- 可自定义尺寸和样式

#### 9. 全屏弹框 (Fullscreen Modal)
- 占满整个屏幕
- 适用于复杂表单或详情页
- 移动端友好

#### 10. 抽屉式弹框 (Drawer Modal)
- 从底部、左侧或右侧滑入
- 移动端自动适配为底部抽屉
- 适用于设置面板或筛选器

### JavaScript API

#### 基础方法
```javascript
// 显示弹窗
showModal('modalId');

// 隐藏弹窗
hideModal('modalId');

// ESC键关闭支持
// 点击遮罩关闭支持
// 自动管理body滚动锁定
```

#### 高级API
```javascript
// 警告弹窗
Modal.warning('标题', '内容', onConfirm, onCancel);

// 成功提示
Modal.success('操作成功！', true); // 第二个参数控制是否自动关闭

// 错误提示
Modal.error('错误标题', '错误信息', onRetry, onClose);

// 确认对话框
Modal.confirm('确认标题', '确认内容', onConfirm, onCancel);

// 信息通知
Modal.info('通知标题', '通知内容');

// 加载状态
const loading = Modal.loading('正在处理中...');
// 手动关闭
loading.close();
```

### 弹窗特性

1. **响应式设计**:
   - 桌面端居中显示，移动端底部弹出
   - 自适应内容高度，最大90vh
   - 移动端按钮全宽显示

2. **交互体验**:
   - 流畅的显示/隐藏动画（0.3s缓动）
   - ESC键快速关闭
   - 点击遮罩关闭
   - 自动管理页面滚动

3. **无障碍访问**:
   - 语义化HTML结构
   - 键盘导航支持
   - 焦点管理
   - 屏幕阅读器友好

4. **样式定制**:
   - CSS变量支持
   - 主题色彩可配置
   - 圆角、间距可调整
   - 支持暗色模式

### 使用示例

```javascript
// 表单提交确认
function showSubmitConfirm() {
    Modal.confirm(
        '确认提交', 
        '确定要提交表单数据吗？', 
        () => {
            // 确认回调
            submitForm();
        },
        () => {
            // 取消回调
            console.log('用户取消提交');
        }
    );
}

// 删除操作警告
function showDeleteWarning(itemName) {
    Modal.warning(
        '删除警告',
        `确定要删除"${itemName}"吗？此操作不可撤销。`,
        () => {
            // 执行删除
            deleteItem(itemName);
            Modal.success('删除成功！');
        }
    );
}

// 异步操作加载
async function performAsyncOperation() {
    const loading = Modal.loading('正在处理中，请稍候...');
    
    try {
        await someAsyncOperation();
        loading.close();
        Modal.success('操作完成！');
    } catch (error) {
        loading.close();
        Modal.error('操作失败', error.message);
    }
}
```

### 自定义样式

```css
/* 修改弹窗主色调 */
.modal-container {
    --primary-color: #your-brand-color;
}

/* 调整弹窗圆角 */
.modal-container {
    border-radius: 16px; /* 默认12px */
}

/* 自定义动画时长 */
.modal-overlay {
    transition: all 0.5s ease; /* 默认0.3s */
}

/* 移动端全屏显示 */
@media (max-width: 480px) {
    .modal-container {
        max-height: 100vh; /* 占满屏幕 */
        border-radius: 0;
    }
}
```

---

💡 **提示**: 建议先打开 `index.html` 查看所有组件的综合示例，然后根据需要选择具体的组件进行集成。弹窗组件已完全集成到主示例页面中，可以直接体验各种弹窗效果。