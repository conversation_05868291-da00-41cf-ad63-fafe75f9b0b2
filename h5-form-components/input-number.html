<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计数器组件</title>
</head>
<body>
    <!-- 计数器组件 -->
    <div class="form-input-number">
        <style>
            .form-input-number {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-input-number-section {
                margin-bottom: 40px;
            }

            .form-input-number-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础计数器样式 */
            .form-input-number-group {
                margin-bottom: 20px;
            }

            .form-input-number-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-input-number-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-input-number-container {
                position: relative;
                display: inline-flex;
                align-items: center;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                background: white;
                transition: all 0.2s ease;
                overflow: hidden;
            }

            .form-input-number-container:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-input-number-container:hover:not(:focus-within) {
                border-color: #9ca3af;
            }

            .form-input-number-input {
                border: none;
                outline: none;
                padding: 12px 16px;
                font-size: 16px;
                color: #374151;
                background: transparent;
                text-align: center;
                min-width: 80px;
                -moz-appearance: textfield;
            }

            .form-input-number-input::-webkit-outer-spin-button,
            .form-input-number-input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            .form-input-number-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 44px;
                border: none;
                background: #f9fafb;
                color: #6b7280;
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 18px;
                font-weight: 500;
                user-select: none;
            }

            .form-input-number-button:hover:not(:disabled) {
                background: #e5e7eb;
                color: #374151;
            }

            .form-input-number-button:active:not(:disabled) {
                background: #d1d5db;
            }

            .form-input-number-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .form-input-number-button.decrease {
                border-right: 1px solid #e5e7eb;
            }

            .form-input-number-button.increase {
                border-left: 1px solid #e5e7eb;
            }

            /* 垂直布局 */
            .form-input-number-container.vertical {
                flex-direction: column;
                width: 120px;
            }

            .form-input-number-container.vertical .form-input-number-button {
                width: 100%;
                height: 32px;
            }

            .form-input-number-container.vertical .form-input-number-button.decrease {
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
                order: 2;
            }

            .form-input-number-container.vertical .form-input-number-button.increase {
                border-left: none;
                border-bottom: 1px solid #e5e7eb;
                order: 0;
            }

            .form-input-number-container.vertical .form-input-number-input {
                order: 1;
                padding: 8px 12px;
            }

            /* 尺寸变体 */
            .form-input-number-container.small {
                border-radius: 6px;
            }

            .form-input-number-container.small .form-input-number-input {
                padding: 8px 12px;
                font-size: 14px;
                min-width: 60px;
            }

            .form-input-number-container.small .form-input-number-button {
                width: 32px;
                height: 36px;
                font-size: 16px;
            }

            .form-input-number-container.large {
                border-radius: 10px;
            }

            .form-input-number-container.large .form-input-number-input {
                padding: 16px 20px;
                font-size: 18px;
                min-width: 100px;
            }

            .form-input-number-container.large .form-input-number-button {
                width: 48px;
                height: 52px;
                font-size: 20px;
            }

            /* 颜色主题 */
            .form-input-number-container.primary:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-input-number-container.success:focus-within {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-input-number-container.warning:focus-within {
                border-color: #f59e0b;
                box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
            }

            .form-input-number-container.danger:focus-within {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 禁用状态 */
            .form-input-number-container.disabled {
                opacity: 0.6;
                cursor: not-allowed;
                background: #f9fafb;
            }

            .form-input-number-container.disabled .form-input-number-input {
                cursor: not-allowed;
                background: transparent;
            }

            /* 只读状态 */
            .form-input-number-container.readonly {
                background: #f9fafb;
            }

            .form-input-number-container.readonly .form-input-number-button {
                display: none;
            }

            .form-input-number-container.readonly .form-input-number-input {
                cursor: default;
            }

            /* 错误状态 */
            .form-input-number-container.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 消息提示 */
            .form-input-number-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-input-number-message.error {
                color: #ef4444;
            }

            .form-input-number-message.success {
                color: #10b981;
            }

            .form-input-number-message.help {
                color: #6b7280;
            }

            /* 带前缀后缀 */
            .form-input-number-with-addon {
                display: flex;
                align-items: center;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                background: white;
                transition: all 0.2s ease;
                overflow: hidden;
            }

            .form-input-number-with-addon:focus-within {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-input-number-addon {
                padding: 12px 16px;
                background: #f9fafb;
                color: #6b7280;
                font-size: 16px;
                border-right: 1px solid #e5e7eb;
                white-space: nowrap;
            }

            .form-input-number-addon.suffix {
                border-right: none;
                border-left: 1px solid #e5e7eb;
            }

            .form-input-number-with-addon .form-input-number-container {
                border: none;
                border-radius: 0;
                flex: 1;
            }

            .form-input-number-with-addon .form-input-number-container:focus-within {
                border: none;
                box-shadow: none;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-input-number-input {
                    padding: 14px 16px;
                    font-size: 16px;
                    min-width: 70px;
                }

                .form-input-number-button {
                    width: 44px;
                    height: 48px;
                }

                .form-input-number-container.small .form-input-number-input {
                    padding: 10px 12px;
                    min-width: 50px;
                }

                .form-input-number-container.small .form-input-number-button {
                    width: 36px;
                    height: 40px;
                }
            }

            @media (max-width: 480px) {
                .form-input-number-container.vertical {
                    width: 100px;
                }

                .form-input-number-addon {
                    padding: 12px;
                    font-size: 14px;
                }
            }

            /* 演示样式 */
            .demo-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }

            .demo-row {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 16px;
                flex-wrap: wrap;
            }
        </style>

        <!-- 基础计数器 -->
        <div class="form-input-number-section">
            <h3 class="form-input-number-title">基础计数器</h3>
            <div class="demo-grid">
                <div class="form-input-number-group">
                    <label class="form-input-number-label">
                        数量 <span class="form-input-number-required">*</span>
                    </label>
                    <div class="form-input-number-container" data-min="1" data-max="100" data-step="1">
                        <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                        <input type="number" class="form-input-number-input" value="1" min="1" max="100" step="1">
                        <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                    </div>
                    <div class="form-input-number-message help">最小值: 1，最大值: 100</div>
                </div>

                <div class="form-input-number-group">
                    <label class="form-input-number-label">价格</label>
                    <div class="form-input-number-container" data-min="0" data-max="9999" data-step="0.01">
                        <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                        <input type="number" class="form-input-number-input" value="99.99" min="0" max="9999" step="0.01">
                        <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                    </div>
                    <div class="form-input-number-message help">支持小数，步长: 0.01</div>
                </div>
            </div>
        </div>

        <!-- 不同尺寸 -->
        <div class="form-input-number-section">
            <h3 class="form-input-number-title">不同尺寸</h3>
            <div class="demo-row">
                <div class="form-input-number-container small" data-min="0" data-max="10" data-step="1">
                    <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                    <input type="number" class="form-input-number-input" value="5" min="0" max="10" step="1">
                    <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                </div>
                <span style="font-size: 12px; color: #6b7280;">小尺寸</span>

                <div class="form-input-number-container" data-min="0" data-max="10" data-step="1">
                    <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                    <input type="number" class="form-input-number-input" value="5" min="0" max="10" step="1">
                    <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                </div>
                <span style="font-size: 12px; color: #6b7280;">默认尺寸</span>

                <div class="form-input-number-container large" data-min="0" data-max="10" data-step="1">
                    <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                    <input type="number" class="form-input-number-input" value="5" min="0" max="10" step="1">
                    <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                </div>
                <span style="font-size: 12px; color: #6b7280;">大尺寸</span>
            </div>
        </div>

        <!-- 垂直布局 -->
        <div class="form-input-number-section">
            <h3 class="form-input-number-title">垂直布局</h3>
            <div class="demo-row">
                <div class="form-input-number-container vertical" data-min="0" data-max="100" data-step="5">
                    <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                    <input type="number" class="form-input-number-input" value="50" min="0" max="100" step="5">
                    <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                </div>
                <span style="font-size: 12px; color: #6b7280;">垂直布局，步长: 5</span>
            </div>
        </div>

        <!-- 带前缀后缀 -->
        <div class="form-input-number-section">
            <h3 class="form-input-number-title">带前缀后缀</h3>
            <div class="demo-grid">
                <div class="form-input-number-group">
                    <label class="form-input-number-label">价格</label>
                    <div class="form-input-number-with-addon">
                        <div class="form-input-number-addon">¥</div>
                        <div class="form-input-number-container" data-min="0" data-max="9999" data-step="1">
                            <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                            <input type="number" class="form-input-number-input" value="299" min="0" max="9999" step="1">
                            <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                        </div>
                        <div class="form-input-number-addon suffix">元</div>
                    </div>
                </div>

                <div class="form-input-number-group">
                    <label class="form-input-number-label">重量</label>
                    <div class="form-input-number-with-addon">
                        <div class="form-input-number-container" data-min="0" data-max="1000" data-step="0.1">
                            <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                            <input type="number" class="form-input-number-input" value="2.5" min="0" max="1000" step="0.1">
                            <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                        </div>
                        <div class="form-input-number-addon suffix">kg</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 不同状态 -->
        <div class="form-input-number-section">
            <h3 class="form-input-number-title">不同状态</h3>
            <div class="demo-grid">
                <div class="form-input-number-group">
                    <label class="form-input-number-label">成功状态</label>
                    <div class="form-input-number-container success" data-min="0" data-max="10" data-step="1">
                        <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                        <input type="number" class="form-input-number-input" value="5" min="0" max="10" step="1">
                        <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                    </div>
                    <div class="form-input-number-message success">输入正确</div>
                </div>

                <div class="form-input-number-group">
                    <label class="form-input-number-label">错误状态</label>
                    <div class="form-input-number-container error" data-min="1" data-max="10" data-step="1">
                        <button class="form-input-number-button decrease" onclick="decreaseValue(this)">−</button>
                        <input type="number" class="form-input-number-input" value="0" min="1" max="10" step="1">
                        <button class="form-input-number-button increase" onclick="increaseValue(this)">+</button>
                    </div>
                    <div class="form-input-number-message error">值不能小于1</div>
                </div>

                <div class="form-input-number-group">
                    <label class="form-input-number-label">禁用状态</label>
                    <div class="form-input-number-container disabled">
                        <button class="form-input-number-button decrease" disabled>−</button>
                        <input type="number" class="form-input-number-input" value="5" disabled>
                        <button class="form-input-number-button increase" disabled>+</button>
                    </div>
                </div>

                <div class="form-input-number-group">
                    <label class="form-input-number-label">只读状态</label>
                    <div class="form-input-number-container readonly">
                        <input type="number" class="form-input-number-input" value="5" readonly>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 增加数值
            function increaseValue(button) {
                const container = button.closest('.form-input-number-container');
                const input = container.querySelector('.form-input-number-input');
                const step = parseFloat(container.dataset.step || input.step || 1);
                const max = parseFloat(container.dataset.max || input.max || Infinity);
                
                let currentValue = parseFloat(input.value) || 0;
                let newValue = currentValue + step;
                
                if (newValue <= max) {
                    input.value = formatValue(newValue, step);
                    updateButtonStates(container);
                    triggerChange(input);
                }
            }

            // 减少数值
            function decreaseValue(button) {
                const container = button.closest('.form-input-number-container');
                const input = container.querySelector('.form-input-number-input');
                const step = parseFloat(container.dataset.step || input.step || 1);
                const min = parseFloat(container.dataset.min || input.min || -Infinity);
                
                let currentValue = parseFloat(input.value) || 0;
                let newValue = currentValue - step;
                
                if (newValue >= min) {
                    input.value = formatValue(newValue, step);
                    updateButtonStates(container);
                    triggerChange(input);
                }
            }

            // 格式化数值
            function formatValue(value, step) {
                if (step < 1) {
                    const decimals = step.toString().split('.')[1]?.length || 0;
                    return value.toFixed(decimals);
                }
                return Math.round(value).toString();
            }

            // 更新按钮状态
            function updateButtonStates(container) {
                const input = container.querySelector('.form-input-number-input');
                const decreaseBtn = container.querySelector('.form-input-number-button.decrease');
                const increaseBtn = container.querySelector('.form-input-number-button.increase');
                
                const value = parseFloat(input.value) || 0;
                const min = parseFloat(container.dataset.min || input.min || -Infinity);
                const max = parseFloat(container.dataset.max || input.max || Infinity);
                
                if (decreaseBtn) {
                    decreaseBtn.disabled = value <= min;
                }
                
                if (increaseBtn) {
                    increaseBtn.disabled = value >= max;
                }
            }

            // 触发change事件
            function triggerChange(input) {
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            }

            // 验证输入值
            function validateInput(input) {
                const container = input.closest('.form-input-number-container');
                const value = parseFloat(input.value);
                const min = parseFloat(container.dataset.min || input.min || -Infinity);
                const max = parseFloat(container.dataset.max || input.max || Infinity);
                
                if (isNaN(value)) {
                    input.value = container.dataset.min || input.min || 0;
                } else if (value < min) {
                    input.value = min;
                } else if (value > max) {
                    input.value = max;
                }
                
                updateButtonStates(container);
            }

            // 初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 为所有计数器输入框添加事件监听
                const inputs = document.querySelectorAll('.form-input-number-input');
                inputs.forEach(input => {
                    const container = input.closest('.form-input-number-container');
                    
                    // 初始化按钮状态
                    updateButtonStates(container);
                    
                    // 输入事件
                    input.addEventListener('input', function() {
                        updateButtonStates(container);
                    });
                    
                    // 失焦验证
                    input.addEventListener('blur', function() {
                        validateInput(this);
                    });
                    
                    // 键盘事件
                    input.addEventListener('keydown', function(e) {
                        const step = parseFloat(container.dataset.step || this.step || 1);
                        
                        if (e.key === 'ArrowUp') {
                            e.preventDefault();
                            const increaseBtn = container.querySelector('.form-input-number-button.increase');
                            if (increaseBtn && !increaseBtn.disabled) {
                                increaseValue(increaseBtn);
                            }
                        } else if (e.key === 'ArrowDown') {
                            e.preventDefault();
                            const decreaseBtn = container.querySelector('.form-input-number-button.decrease');
                            if (decreaseBtn && !decreaseBtn.disabled) {
                                decreaseValue(decreaseBtn);
                            }
                        }
                    });
                });

                // 长按功能
                let pressTimer = null;
                let pressInterval = null;

                function startPress(button, action) {
                    action(button);
                    pressTimer = setTimeout(() => {
                        pressInterval = setInterval(() => {
                            action(button);
                        }, 100);
                    }, 500);
                }

                function endPress() {
                    if (pressTimer) {
                        clearTimeout(pressTimer);
                        pressTimer = null;
                    }
                    if (pressInterval) {
                        clearInterval(pressInterval);
                        pressInterval = null;
                    }
                }

                // 为所有按钮添加长按功能
                const buttons = document.querySelectorAll('.form-input-number-button');
                buttons.forEach(button => {
                    const action = button.classList.contains('increase') ? increaseValue : decreaseValue;
                    
                    button.addEventListener('mousedown', () => startPress(button, action));
                    button.addEventListener('mouseup', endPress);
                    button.addEventListener('mouseleave', endPress);
                    button.addEventListener('touchstart', () => startPress(button, action));
                    button.addEventListener('touchend', endPress);
                });
            });
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础计数器 --&gt;
&lt;div class="form-input-number-container" data-min="1" data-max="100" data-step="1"&gt;
    &lt;button class="form-input-number-button decrease" onclick="decreaseValue(this)"&gt;−&lt;/button&gt;
    &lt;input type="number" class="form-input-number-input" value="1" min="1" max="100" step="1"&gt;
    &lt;button class="form-input-number-button increase" onclick="increaseValue(this)"&gt;+&lt;/button&gt;
&lt;/div&gt;

&lt;!-- 垂直布局 --&gt;
&lt;div class="form-input-number-container vertical"&gt;
    &lt;button class="form-input-number-button increase"&gt;+&lt;/button&gt;
    &lt;input type="number" class="form-input-number-input" value="50"&gt;
    &lt;button class="form-input-number-button decrease"&gt;−&lt;/button&gt;
&lt;/div&gt;

&lt;!-- 带前缀后缀 --&gt;
&lt;div class="form-input-number-with-addon"&gt;
    &lt;div class="form-input-number-addon"&gt;¥&lt;/div&gt;
    &lt;div class="form-input-number-container"&gt;
        &lt;!-- 计数器内容 --&gt;
    &lt;/div&gt;
    &lt;div class="form-input-number-addon suffix"&gt;元&lt;/div&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
