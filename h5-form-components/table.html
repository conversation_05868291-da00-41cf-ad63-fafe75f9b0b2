<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格组件</title>
</head>
<body>
    <!-- 表格组件 -->
    <div class="form-table">
        <style>
            .form-table {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-table-container {
                background: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                margin-bottom: 20px;
            }

            .form-table-header {
                padding: 16px 20px;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 12px;
            }

            .form-table-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin: 0;
            }

            .form-table-actions {
                display: flex;
                gap: 8px;
                align-items: center;
            }

            .form-table-search {
                position: relative;
                min-width: 200px;
            }

            .form-table-search input {
                width: 100%;
                padding: 8px 32px 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                outline: none;
                transition: border-color 0.2s ease;
            }

            .form-table-search input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }

            .form-table-search-icon {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                color: #6b7280;
                pointer-events: none;
            }

            .form-table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .form-table-element {
                width: 100%;
                border-collapse: collapse;
                font-size: 14px;
            }

            .form-table-element th,
            .form-table-element td {
                padding: 12px 16px;
                text-align: left;
                border-bottom: 1px solid #f3f4f6;
                vertical-align: middle;
            }

            .form-table-element th {
                background-color: #f9fafb;
                font-weight: 600;
                color: #374151;
                position: sticky;
                top: 0;
                z-index: 10;
                white-space: nowrap;
                user-select: none;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .form-table-element th:hover {
                background-color: #f3f4f6;
            }

            .form-table-element th.sortable {
                position: relative;
                padding-right: 32px;
            }

            .form-table-element th.sortable::after {
                content: '↕';
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                color: #9ca3af;
                font-size: 12px;
                transition: color 0.2s ease;
            }

            .form-table-element th.sort-asc::after {
                content: '↑';
                color: #3b82f6;
            }

            .form-table-element th.sort-desc::after {
                content: '↓';
                color: #3b82f6;
            }

            .form-table-element td {
                color: #6b7280;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .form-table-element tr:hover {
                background-color: #f9fafb;
            }

            .form-table-element tr.selected {
                background-color: #eff6ff;
            }

            .form-table-checkbox {
                width: 16px;
                height: 16px;
                cursor: pointer;
            }

            .form-table-actions-cell {
                white-space: nowrap;
            }

            .form-table-action-btn {
                padding: 4px 8px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                margin-right: 4px;
                transition: all 0.2s ease;
            }

            .form-table-action-btn.edit {
                background-color: #dbeafe;
                color: #1e40af;
            }

            .form-table-action-btn.edit:hover {
                background-color: #bfdbfe;
            }

            .form-table-action-btn.delete {
                background-color: #fee2e2;
                color: #dc2626;
            }

            .form-table-action-btn.delete:hover {
                background-color: #fecaca;
            }

            .form-table-pagination {
                padding: 16px 20px;
                border-top: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 12px;
            }

            .form-table-pagination-info {
                font-size: 14px;
                color: #6b7280;
            }

            .form-table-pagination-controls {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .form-table-pagination-btn {
                padding: 6px 12px;
                border: 1px solid #d1d5db;
                background: white;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 32px;
                text-align: center;
            }

            .form-table-pagination-btn:hover:not(:disabled) {
                border-color: #3b82f6;
                color: #3b82f6;
            }

            .form-table-pagination-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .form-table-pagination-btn.active {
                background-color: #3b82f6;
                color: white;
                border-color: #3b82f6;
            }

            .form-table-status {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
            }

            .form-table-status.active {
                background-color: #dcfce7;
                color: #166534;
            }

            .form-table-status.inactive {
                background-color: #fee2e2;
                color: #dc2626;
            }

            .form-table-status.pending {
                background-color: #fef3c7;
                color: #d97706;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .form-table-header {
                    flex-direction: column;
                    align-items: stretch;
                    gap: 16px;
                }

                .form-table-actions {
                    justify-content: space-between;
                    flex-wrap: wrap;
                }

                .form-table-search {
                    min-width: auto;
                    flex: 1;
                    margin-right: 12px;
                }

                .form-table-add-btn,
                .form-table-export-btn {
                    padding: 12px 16px;
                    font-size: 14px;
                    min-height: 44px;
                }

                .form-table-add-btn {
                    flex: 1;
                    margin-right: 8px;
                }

                .form-table-export-btn {
                    flex: 0 0 auto;
                }

                .form-table-element th,
                .form-table-element td {
                    padding: 8px 6px;
                    font-size: 13px;
                }

                .form-table-element th:first-child,
                .form-table-element td:first-child {
                    padding-left: 12px;
                }

                .form-table-element th:last-child,
                .form-table-element td:last-child {
                    padding-right: 12px;
                }

                .form-table-action-btn {
                    padding: 6px 8px;
                    font-size: 11px;
                    margin-right: 2px;
                }

                .form-table-pagination {
                    flex-direction: column;
                    align-items: stretch;
                    text-align: center;
                    gap: 16px;
                }

                .form-table-pagination-controls {
                    justify-content: center;
                    flex-wrap: wrap;
                }

                .form-table-pagination-btn {
                    min-width: 40px;
                    padding: 8px 12px;
                }
            }

            @media (max-width: 480px) {
                .form-table-header {
                    padding: 12px 16px;
                }

                .form-table-actions {
                    flex-direction: column;
                    gap: 12px;
                }

                .form-table-search {
                    margin-right: 0;
                }

                .form-table-add-btn {
                    width: 100%;
                    margin-right: 0;
                    margin-bottom: 8px;
                }

                .form-table-export-btn {
                    width: 100%;
                }

                .form-table-element {
                    font-size: 12px;
                }

                .form-table-element th,
                .form-table-element td {
                    padding: 6px 4px;
                    max-width: 80px;
                }

                .form-table-actions-cell {
                    min-width: 80px;
                }

                .form-table-action-btn {
                    display: block;
                    width: 100%;
                    margin-bottom: 2px;
                    margin-right: 0;
                }

                /* 弹窗移动端适配 */
                .table-modal-overlay {
                    padding: 0;
                    align-items: flex-end;
                }

                .table-modal-container {
                    max-width: 100%;
                    width: 100%;
                    border-radius: 12px 12px 0 0;
                    max-height: 95vh;
                }

                .table-modal-header {
                    padding: 16px 20px 0 20px;
                }

                .table-modal-body {
                    padding: 16px 20px 20px 20px;
                }

                .table-modal-footer {
                    padding: 0 20px 20px 20px;
                    flex-direction: column;
                }

                .table-modal-button {
                    width: 100%;
                    min-height: 44px;
                }

                /* 表单移动端适配 */
                .form-table-search-form {
                    grid-template-columns: 1fr;
                    gap: 12px;
                }

                .table-modal-body > form > div[style*="grid"] {
                    grid-template-columns: 1fr !important;
                    gap: 12px;
                }

                .form-checkbox-horizontal.form-checkbox-two-columns {
                    grid-template-columns: 1fr;
                }

                .form-radio-horizontal {
                    flex-direction: column;
                    gap: 8px;
                }
            }

            /* 空状态 */
            .form-table-empty {
                text-align: center;
                padding: 40px 20px;
                color: #9ca3af;
            }

            .form-table-empty-icon {
                font-size: 48px;
                margin-bottom: 16px;
                opacity: 0.5;
            }

            .form-table-empty-text {
                font-size: 16px;
                margin-bottom: 8px;
            }

            .form-table-empty-desc {
                font-size: 14px;
                color: #6b7280;
            }

            /* 美化的新增用户按钮 */
            .form-table-add-btn {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 10px 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                position: relative;
                overflow: hidden;
            }

            .form-table-add-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .form-table-add-btn:hover::before {
                left: 100%;
            }

            .form-table-add-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
            }

            .form-table-add-btn:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }

            .form-table-add-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                font-size: 14px;
                font-weight: bold;
            }

            /* 导出按钮样式 */
            .form-table-export-btn {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 10px 18px;
                background: white;
                color: #374151;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .form-table-export-btn:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                background: #f8fafc;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }

            .form-table-export-icon {
                font-size: 16px;
            }
            
            /* 表格风格变体 */
            .form-table-striped tbody tr:nth-child(even) {
                background-color: #f9fafb;
            }

            .form-table-striped tbody tr:hover {
                background-color: #f3f4f6;
            }

            .form-table-bordered .form-table-element th,
            .form-table-bordered .form-table-element td {
                border: 1px solid #e5e7eb;
            }

            .form-table-bordered .form-table-element {
                border-collapse: separate;
                border-spacing: 0;
            }

            .form-table-bordered .form-table-element th:first-child,
            .form-table-bordered .form-table-element td:first-child {
                border-left: none;
            }

            .form-table-bordered .form-table-element th:last-child,
            .form-table-bordered .form-table-element td:last-child {
                border-right: none;
            }

            .form-table-compact .form-table-element th,
            .form-table-compact .form-table-element td {
                padding: 8px 12px;
                font-size: 13px;
            }

            .form-table-large .form-table-element th,
            .form-table-large .form-table-element td {
                padding: 16px 20px;
                font-size: 15px;
            }

            /* 深色主题 */
            .form-table-dark .form-table-container {
                background: #1f2937;
            }

            .form-table-dark .form-table-header {
                border-bottom-color: #374151;
            }

            .form-table-dark .form-table-title {
                color: #f9fafb;
            }

            .form-table-dark .form-table-element th {
                background-color: #374151;
                color: #f9fafb;
                border-bottom-color: #4b5563;
            }

            .form-table-dark .form-table-element td {
                color: #d1d5db;
                border-bottom-color: #374151;
            }

            .form-table-dark .form-table-element tr:hover {
                background-color: #374151;
            }

            .form-table-dark .form-table-pagination {
                border-top-color: #374151;
            }

            .form-table-dark .form-table-pagination-info {
                color: #d1d5db;
            }

            /* 彩色主题 */
            .form-table-colorful .form-table-element th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .form-table-colorful .form-table-element th:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }

            /* 卡片风格 */
            .form-table-card {
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .form-table-card .form-table-header {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-bottom: 2px solid #e5e7eb;
            }

            /* 简约风格 */
            .form-table-minimal .form-table-container {
                box-shadow: none;
                border: 1px solid #e5e7eb;
            }

            .form-table-minimal .form-table-element th {
                background-color: transparent;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 500;
            }

            .form-table-minimal .form-table-element td {
                border-bottom: 1px solid #f3f4f6;
            }

            /* 高级搜索样式 */
            .form-table-advanced-search {
                background: #f8fafc;
                border-bottom: 1px solid #e5e7eb;
                padding: 16px 20px;
                display: none;
            }

            .form-table-advanced-search.show {
                display: block;
            }

            .form-table-search-toggle {
                background: white;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                margin-left: 8px;
            }

            .form-table-search-toggle:hover {
                border-color: #3b82f6;
                color: #3b82f6;
            }

            .form-table-search-toggle.active {
                background: #3b82f6;
                color: white;
                border-color: #3b82f6;
            }

            .form-table-search-form {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
                margin-bottom: 16px;
            }

            .form-table-search-actions {
                display: flex;
                gap: 8px;
                justify-content: flex-end;
            }

            .form-table-search-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .form-table-search-btn.primary {
                background: #3b82f6;
                color: white;
            }

            .form-table-search-btn.primary:hover {
                background: #2563eb;
            }

            .form-table-search-btn.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .form-table-search-btn.secondary:hover {
                background: #e5e7eb;
            }

            /* 弹窗样式（复用之前的样式） */
            .table-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                padding: 20px;
                box-sizing: border-box;
            }

            .table-modal-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .table-modal-container {
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                max-width: 600px;
                width: 100%;
                max-height: 90vh;
                overflow: hidden;
                transform: scale(0.9) translateY(20px);
                transition: all 0.3s ease;
                position: relative;
            }

            .table-modal-overlay.show .table-modal-container {
                transform: scale(1) translateY(0);
            }

            .table-modal-header {
                padding: 24px 24px 0 24px;
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                border-bottom: 1px solid #e5e7eb;
                margin-bottom: 0;
                padding-bottom: 16px;
            }

            .table-modal-title {
                font-size: 20px;
                font-weight: 600;
                color: #1f2937;
                margin: 0;
                line-height: 1.4;
                flex: 1;
            }

            .table-modal-close {
                width: 32px;
                height: 32px;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #6b7280;
                transition: all 0.2s ease;
                flex-shrink: 0;
                margin-left: 16px;
                font-size: 20px;
            }

            .table-modal-close:hover {
                background-color: #f3f4f6;
                color: #374151;
            }

            .table-modal-body {
                padding: 24px;
                overflow-y: auto;
                max-height: calc(90vh - 140px);
            }

            .table-modal-footer {
                padding: 0 24px 24px 24px;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
                border-top: 1px solid #e5e7eb;
                margin-top: 0;
                padding-top: 16px;
            }

            .table-modal-button {
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
                min-height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                text-decoration: none;
                user-select: none;
            }

            .table-modal-button:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .table-modal-button.primary {
                background-color: #3b82f6;
                color: white;
            }

            .table-modal-button.primary:hover {
                background-color: #2563eb;
            }

            .table-modal-button.secondary {
                background-color: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .table-modal-button.secondary:hover {
                background-color: #e5e7eb;
            }

            /* 表单组件样式 */
            .form-input-group {
                margin-bottom: 16px;
            }

            .form-input-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-input-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-input {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
            }

            .form-input::placeholder {
                color: #9ca3af;
            }

            .form-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-input:hover:not(:focus) {
                border-color: #9ca3af;
            }

            .form-select-group {
                margin-bottom: 16px;
                position: relative;
            }

            .form-select-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-select-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
            }

            .form-select-input {
                width: 100%;
                padding: 10px 36px 10px 12px;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
                cursor: pointer;
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
            }

            .form-select-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-select-arrow {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
                pointer-events: none;
                transition: transform 0.2s ease;
            }

            .form-select-input:focus + .form-select-arrow {
                transform: translateY(-50%) rotate(180deg);
            }

            .form-textarea-group {
                margin-bottom: 16px;
            }

            .form-textarea-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-textarea-input {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
                resize: vertical;
                min-height: 80px;
                line-height: 1.5;
                font-family: inherit;
            }

            .form-textarea-input::placeholder {
                color: #9ca3af;
            }

            .form-textarea-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-radio {
                margin: 16px 0;
            }

            .form-radio input[type="radio"] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .form-radio-item {
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                cursor: pointer;
                padding: 6px 0;
            }

            .form-radio-custom {
                position: relative;
                width: 18px;
                height: 18px;
                border: 2px solid #d1d5db;
                border-radius: 50%;
                background-color: #ffffff;
                margin-right: 10px;
                transition: all 0.2s ease;
                flex-shrink: 0;
            }

            .form-radio-custom::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #3b82f6;
                transform: translate(-50%, -50%) scale(0);
                transition: transform 0.2s ease;
            }

            .form-radio input[type="radio"]:checked + .form-radio-custom {
                border-color: #3b82f6;
            }

            .form-radio input[type="radio"]:checked + .form-radio-custom::after {
                transform: translate(-50%, -50%) scale(1);
            }

            .form-radio input[type="radio"]:focus + .form-radio-custom {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                border-color: #3b82f6;
            }

            .form-radio-item:hover .form-radio-custom {
                border-color: #3b82f6;
            }

            .form-radio-label {
                font-size: 14px;
                color: #374151;
                line-height: 1.5;
                user-select: none;
            }

            .form-radio-title {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            .form-radio-horizontal {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }

            .form-radio-horizontal .form-radio-item {
                margin-bottom: 0;
                margin-right: 0;
                flex: 0 0 auto;
            }

            .form-checkbox {
                margin: 16px 0;
            }

            .form-checkbox input[type="checkbox"] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .form-checkbox-item {
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                cursor: pointer;
                padding: 6px 0;
            }

            .form-checkbox-custom {
                position: relative;
                width: 18px;
                height: 18px;
                border: 2px solid #d1d5db;
                border-radius: 4px;
                background-color: #ffffff;
                margin-right: 10px;
                transition: all 0.2s ease;
                flex-shrink: 0;
            }

            .form-checkbox-custom::after {
                content: '';
                position: absolute;
                left: 5px;
                top: 1px;
                width: 5px;
                height: 9px;
                border: solid #ffffff;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom {
                background-color: #3b82f6;
                border-color: #3b82f6;
            }

            .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom::after {
                opacity: 1;
            }

            .form-checkbox input[type="checkbox"]:focus + .form-checkbox-custom {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                border-color: #3b82f6;
            }

            .form-checkbox-item:hover .form-checkbox-custom {
                border-color: #3b82f6;
            }

            .form-checkbox-label {
                font-size: 14px;
                color: #374151;
                line-height: 1.5;
                user-select: none;
            }

            .form-checkbox-title {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            .form-checkbox-horizontal {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }

            .form-checkbox-horizontal .form-checkbox-item {
                margin-bottom: 0;
                margin-right: 0;
                flex: 0 0 auto;
            }

            .form-checkbox-horizontal.form-checkbox-two-columns {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 12px;
            }

            .form-datetime-group {
                margin-bottom: 16px;
            }

            .form-datetime-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-datetime-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
            }

            .form-datetime-input {
                width: 100%;
                padding: 10px 36px 10px 12px;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
                cursor: pointer;
            }

            .form-datetime-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-datetime-input:hover:not(:focus) {
                border-color: #9ca3af;
            }
        </style>

        <!-- 基础表格示例 -->
        <div class="form-table-container">
            <div class="form-table-header">
                <h3 class="form-table-title">用户列表</h3>
                <div class="form-table-actions">
                  <!--  <div class="form-table-search">
                        <input type="text" placeholder="搜索用户..." onkeyup="filterTable(this.value)" id="quickSearch">
                        <span class="form-table-search-icon">🔍</span>
                    </div>-->
                    <button class="form-table-search-toggle" onclick="toggleAdvancedSearch()">
                        高级搜索
                    </button>
                    <button class="form-table-add-btn" onclick="showAddUserModal()">
                        <span class="form-table-add-icon">+</span>
                        新增用户
                    </button>
                    <button class="form-table-export-btn" onclick="exportTable()">
                        <span class="form-table-export-icon">📊</span>
                        导出
                    </button>
                </div>
            </div>

            <!-- 高级搜索区域 -->
            <div class="form-table-advanced-search" id="advancedSearch">
                <form class="form-table-search-form" onsubmit="performAdvancedSearch(event)">
                    <div class="form-input-group">
                        <label class="form-input-label">用户姓名</label>
                        <input type="text" class="form-input" id="searchName" placeholder="请输入姓名">
                    </div>
                    <div class="form-select-group">
                        <label class="form-select-label">所属部门</label>
                        <div class="form-select-wrapper">
                            <select class="form-select-input" id="searchDepartment">
                                <option value="">全部部门</option>
                                <option value="技术部">技术部</option>
                                <option value="市场部">市场部</option>
                                <option value="人事部">人事部</option>
                                <option value="财务部">财务部</option>
                            </select>
                            <div class="form-select-arrow"></div>
                        </div>
                    </div>
                    <div class="form-select-group">
                        <label class="form-select-label">用户状态</label>
                        <div class="form-select-wrapper">
                            <select class="form-select-input" id="searchStatus">
                                <option value="">全部状态</option>
                                <option value="活跃">活跃</option>
                                <option value="待审核">待审核</option>
                                <option value="禁用">禁用</option>
                            </select>
                            <div class="form-select-arrow"></div>
                        </div>
                    </div>
                    <div class="form-datetime-group">
                        <label class="form-datetime-label">注册日期</label>
                        <div class="form-datetime-wrapper">
                            <input type="date" class="form-datetime-input" id="searchDate">
                        </div>
                    </div>
                </form>
                <div class="form-table-search-actions">
                    <button type="button" class="form-table-search-btn secondary" onclick="resetAdvancedSearch()">
                        重置
                    </button>
                    <button type="button" class="form-table-search-btn primary" onclick="performAdvancedSearch()">
                        搜索
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element" id="userTable">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <input type="checkbox" class="form-table-checkbox" onchange="toggleAllRows(this)">
                            </th>
                            <th class="sortable" onclick="sortTable(0)">ID</th>
                            <th class="sortable" onclick="sortTable(1)">姓名</th>
                            <th class="sortable" onclick="sortTable(2)">邮箱</th>
                            <th class="sortable" onclick="sortTable(3)">部门</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>001</td>
                            <td>张三</td>
                            <td><EMAIL></td>
                            <td>技术部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-15</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>002</td>
                            <td>李四</td>
                            <td><EMAIL></td>
                            <td>市场部</td>
                            <td><span class="form-table-status pending">待审核</span></td>
                            <td>2024-01-14</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>003</td>
                            <td>王五</td>
                            <td><EMAIL></td>
                            <td>人事部</td>
                            <td><span class="form-table-status inactive">禁用</span></td>
                            <td>2024-01-13</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>004</td>
                            <td>赵六</td>
                            <td><EMAIL></td>
                            <td>财务部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-12</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>005</td>
                            <td>孙七</td>
                            <td><EMAIL></td>
                            <td>技术部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-11</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="form-table-pagination">
                <div class="form-table-pagination-info">
                    显示 1-5 条，共 25 条记录
                </div>
                <div class="form-table-pagination-controls">
                    <button class="form-table-pagination-btn" disabled>上一页</button>
                    <button class="form-table-pagination-btn active">1</button>
                    <button class="form-table-pagination-btn">2</button>
                    <button class="form-table-pagination-btn">3</button>
                    <button class="form-table-pagination-btn">...</button>
                    <button class="form-table-pagination-btn">5</button>
                    <button class="form-table-pagination-btn">下一页</button>
                </div>
            </div>
        </div>

        <!-- 条纹表格示例 -->
        <div class="form-table-container form-table-striped">
            <div class="form-table-header">
                <h3 class="form-table-title">条纹表格</h3>
                <div class="form-table-actions">
                    <button class="form-table-add-btn">
                        <span class="form-table-add-icon">+</span>
                        添加记录
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>产品名称</th>
                            <th>分类</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>iPhone 15 Pro</td>
                            <td>手机</td>
                            <td>¥8,999</td>
                            <td>156</td>
                            <td><span class="form-table-status active">在售</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">下架</button>
                            </td>
                        </tr>
                        <tr>
                            <td>MacBook Pro</td>
                            <td>电脑</td>
                            <td>¥12,999</td>
                            <td>89</td>
                            <td><span class="form-table-status active">在售</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">下架</button>
                            </td>
                        </tr>
                        <tr>
                            <td>AirPods Pro</td>
                            <td>耳机</td>
                            <td>¥1,899</td>
                            <td>0</td>
                            <td><span class="form-table-status inactive">缺货</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">下架</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 边框表格示例 -->
        <div class="form-table-container form-table-bordered">
            <div class="form-table-header">
                <h3 class="form-table-title">边框表格</h3>
                <div class="form-table-actions">
                    <button class="form-table-export-btn">
                        <span class="form-table-export-icon">📊</span>
                        导出数据
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>客户</th>
                            <th>金额</th>
                            <th>支付方式</th>
                            <th>订单状态</th>
                            <th>下单时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>#20240101001</td>
                            <td>张三</td>
                            <td>¥2,580</td>
                            <td>微信支付</td>
                            <td><span class="form-table-status active">已支付</span></td>
                            <td>2024-01-15 14:30</td>
                        </tr>
                        <tr>
                            <td>#20240101002</td>
                            <td>李四</td>
                            <td>¥1,299</td>
                            <td>支付宝</td>
                            <td><span class="form-table-status pending">待发货</span></td>
                            <td>2024-01-15 15:20</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 紧凑表格示例 -->
        <div class="form-table-container form-table-compact">
            <div class="form-table-header">
                <h3 class="form-table-title">紧凑表格</h3>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>任务</th>
                            <th>负责人</th>
                            <th>优先级</th>
                            <th>进度</th>
                            <th>截止日期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>网站重构</td>
                            <td>张开发</td>
                            <td><span class="form-table-status inactive">高</span></td>
                            <td>85%</td>
                            <td>2024-01-20</td>
                        </tr>
                        <tr>
                            <td>API优化</td>
                            <td>李后端</td>
                            <td><span class="form-table-status pending">中</span></td>
                            <td>60%</td>
                            <td>2024-01-25</td>
                        </tr>
                        <tr>
                            <td>UI设计</td>
                            <td>王设计</td>
                            <td><span class="form-table-status active">低</span></td>
                            <td>95%</td>
                            <td>2024-01-18</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 深色主题表格示例 -->
        <div class="form-table-container form-table-dark">
            <div class="form-table-header">
                <h3 class="form-table-title">深色主题表格</h3>
                <div class="form-table-actions">
                    <button class="form-table-add-btn">
                        <span class="form-table-add-icon">+</span>
                        新增项目
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>项目名称</th>
                            <th>团队</th>
                            <th>预算</th>
                            <th>进度</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>移动应用开发</td>
                            <td>前端团队</td>
                            <td>¥50,000</td>
                            <td>72%</td>
                            <td><span class="form-table-status active">进行中</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">查看</button>
                                <button class="form-table-action-btn delete">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>数据分析平台</td>
                            <td>后端团队</td>
                            <td>¥80,000</td>
                            <td>45%</td>
                            <td><span class="form-table-status pending">计划中</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">查看</button>
                                <button class="form-table-action-btn delete">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="form-table-pagination">
                <div class="form-table-pagination-info">
                    显示 1-2 条，共 8 条记录
                </div>
                <div class="form-table-pagination-controls">
                    <button class="form-table-pagination-btn" disabled>上一页</button>
                    <button class="form-table-pagination-btn active">1</button>
                    <button class="form-table-pagination-btn">2</button>
                    <button class="form-table-pagination-btn">3</button>
                    <button class="form-table-pagination-btn">下一页</button>
                </div>
            </div>
        </div>

        <!-- 彩色主题表格示例 -->
        <div class="form-table-container form-table-colorful">
            <div class="form-table-header">
                <h3 class="form-table-title">彩色主题表格</h3>
                <div class="form-table-actions">
                    <button class="form-table-add-btn">
                        <span class="form-table-add-icon">+</span>
                        添加数据
                    </button>
                    <button class="form-table-export-btn">
                        <span class="form-table-export-icon">📊</span>
                        导出
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th class="sortable" onclick="sortTable(0)">学生姓名</th>
                            <th class="sortable" onclick="sortTable(1)">班级</th>
                            <th class="sortable" onclick="sortTable(2)">数学</th>
                            <th class="sortable" onclick="sortTable(3)">英语</th>
                            <th class="sortable" onclick="sortTable(4)">总分</th>
                            <th>等级</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>王小明</td>
                            <td>三年级A班</td>
                            <td>95</td>
                            <td>88</td>
                            <td>183</td>
                            <td><span class="form-table-status active">优秀</span></td>
                        </tr>
                        <tr>
                            <td>李小红</td>
                            <td>三年级B班</td>
                            <td>87</td>
                            <td>92</td>
                            <td>179</td>
                            <td><span class="form-table-status active">优秀</span></td>
                        </tr>
                        <tr>
                            <td>张小华</td>
                            <td>三年级A班</td>
                            <td>76</td>
                            <td>81</td>
                            <td>157</td>
                            <td><span class="form-table-status pending">良好</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 卡片风格表格示例 -->
        <div class="form-table-container form-table-card">
            <div class="form-table-header">
                <h3 class="form-table-title">卡片风格表格</h3>
                <div class="form-table-actions">
                    <div class="form-table-search">
                        <input type="text" placeholder="搜索文章..." onkeyup="filterTable(this.value)">
                        <span class="form-table-search-icon">🔍</span>
                    </div>
                    <button class="form-table-add-btn">
                        <span class="form-table-add-icon">+</span>
                        写文章
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>文章标题</th>
                            <th>作者</th>
                            <th>分类</th>
                            <th>阅读量</th>
                            <th>发布时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Vue 3.0 新特性详解</td>
                            <td>技术小编</td>
                            <td>前端开发</td>
                            <td>1,256</td>
                            <td>2024-01-15</td>
                            <td><span class="form-table-status active">已发布</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>React Hooks 最佳实践</td>
                            <td>技术小编</td>
                            <td>前端开发</td>
                            <td>892</td>
                            <td>2024-01-14</td>
                            <td><span class="form-table-status pending">草稿</span></td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">发布</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 简约风格表格示例 -->
        <div class="form-table-container form-table-minimal">
            <div class="form-table-header">
                <h3 class="form-table-title">简约风格表格</h3>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element">
                    <thead>
                        <tr>
                            <th>联系人</th>
                            <th>电话</th>
                            <th>邮箱</th>
                            <th>公司</th>
                            <th>职位</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>张经理</td>
                            <td>138-0000-0001</td>
                            <td><EMAIL></td>
                            <td>ABC科技</td>
                            <td>项目经理</td>
                        </tr>
                        <tr>
                            <td>李总监</td>
                            <td>138-0000-0002</td>
                            <td><EMAIL></td>
                            <td>XYZ集团</td>
                            <td>技术总监</td>
                        </tr>
                        <tr>
                            <td>王主管</td>
                            <td>138-0000-0003</td>
                            <td><EMAIL></td>
                            <td>DEF企业</td>
                            <td>销售主管</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 空状态表格示例 -->
        <div class="form-table-container">
            <div class="form-table-header">
                <h3 class="form-table-title">空数据表格</h3>
                <div class="form-table-actions">
                    <button class="form-table-add-btn">
                        <span class="form-table-add-icon">+</span>
                        添加第一条记录
                    </button>
                </div>
            </div>
            <div class="form-table-empty">
                <div class="form-table-empty-icon">📋</div>
                <div class="form-table-empty-text">暂无数据</div>
                <div class="form-table-empty-desc">当前没有任何记录，请添加新数据</div>
            </div>
        </div>

        <!-- 新增用户弹窗 -->
        <div id="addUserModal" class="table-modal-overlay">
            <div class="table-modal-container">
                <div class="table-modal-header">
                    <h3 class="table-modal-title">新增用户</h3>
                    <button class="table-modal-close" onclick="hideUserModal('addUserModal')">×</button>
                </div>
                <div class="table-modal-body">
                    <form id="addUserForm">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-input-group">
                                <label class="form-input-label">
                                    用户姓名 <span class="form-input-required">*</span>
                                </label>
                                <input type="text" class="form-input" name="name" placeholder="请输入用户姓名" required>
                            </div>
                            <div class="form-input-group">
                                <label class="form-input-label">
                                    邮箱地址 <span class="form-input-required">*</span>
                                </label>
                                <input type="email" class="form-input" name="email" placeholder="请输入邮箱地址" required>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-select-group">
                                <label class="form-select-label">
                                    所属部门 <span class="form-input-required">*</span>
                                </label>
                                <div class="form-select-wrapper">
                                    <select class="form-select-input" name="department" required>
                                        <option value="" disabled selected>请选择部门</option>
                                        <option value="技术部">技术部</option>
                                        <option value="市场部">市场部</option>
                                        <option value="人事部">人事部</option>
                                        <option value="财务部">财务部</option>
                                        <option value="运营部">运营部</option>
                                    </select>
                                    <div class="form-select-arrow"></div>
                                </div>
                            </div>
                            <div class="form-select-group">
                                <label class="form-select-label">用户状态</label>
                                <div class="form-select-wrapper">
                                    <select class="form-select-input" name="status">
                                        <option value="活跃" selected>活跃</option>
                                        <option value="待审核">待审核</option>
                                        <option value="禁用">禁用</option>
                                    </select>
                                    <div class="form-select-arrow"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-input-group" style="margin-bottom: 16px;">
                            <label class="form-input-label">手机号码</label>
                            <input type="tel" class="form-input" name="phone" placeholder="请输入手机号码">
                        </div>

                        <div class="form-radio" style="margin-bottom: 16px;">
                            <div class="form-radio-title">性别</div>
                            <div class="form-radio-horizontal">
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="男" checked>
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">男</span>
                                </label>
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="女">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">女</span>
                                </label>
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="其他">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">其他</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-checkbox" style="margin-bottom: 16px;">
                            <div class="form-checkbox-title">权限设置</div>
                            <div class="form-checkbox-horizontal form-checkbox-two-columns">
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="permissions" value="read">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">查看权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="permissions" value="write">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">编辑权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="permissions" value="delete">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">删除权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="permissions" value="admin">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">管理权限</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-textarea-group">
                            <label class="form-textarea-label">备注信息</label>
                            <textarea class="form-textarea-input" name="remarks" placeholder="请输入备注信息..." rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="table-modal-footer">
                    <button type="button" class="table-modal-button secondary" onclick="hideUserModal('addUserModal')">
                        取消
                    </button>
                    <button type="button" class="table-modal-button primary" onclick="saveUser('add')">
                        保存
                    </button>
                </div>
            </div>
        </div>

        <!-- 编辑用户弹窗 -->
        <div id="editUserModal" class="table-modal-overlay">
            <div class="table-modal-container">
                <div class="table-modal-header">
                    <h3 class="table-modal-title">编辑用户</h3>
                    <button class="table-modal-close" onclick="hideUserModal('editUserModal')">×</button>
                </div>
                <div class="table-modal-body">
                    <form id="editUserForm">
                        <input type="hidden" name="id" id="editUserId">
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-input-group">
                                <label class="form-input-label">
                                    用户姓名 <span class="form-input-required">*</span>
                                </label>
                                <input type="text" class="form-input" name="name" id="editUserName" placeholder="请输入用户姓名" required>
                            </div>
                            <div class="form-input-group">
                                <label class="form-input-label">
                                    邮箱地址 <span class="form-input-required">*</span>
                                </label>
                                <input type="email" class="form-input" name="email" id="editUserEmail" placeholder="请输入邮箱地址" required>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-select-group">
                                <label class="form-select-label">
                                    所属部门 <span class="form-input-required">*</span>
                                </label>
                                <div class="form-select-wrapper">
                                    <select class="form-select-input" name="department" id="editUserDepartment" required>
                                        <option value="" disabled>请选择部门</option>
                                        <option value="技术部">技术部</option>
                                        <option value="市场部">市场部</option>
                                        <option value="人事部">人事部</option>
                                        <option value="财务部">财务部</option>
                                        <option value="运营部">运营部</option>
                                    </select>
                                    <div class="form-select-arrow"></div>
                                </div>
                            </div>
                            <div class="form-select-group">
                                <label class="form-select-label">用户状态</label>
                                <div class="form-select-wrapper">
                                    <select class="form-select-input" name="status" id="editUserStatus">
                                        <option value="活跃">活跃</option>
                                        <option value="待审核">待审核</option>
                                        <option value="禁用">禁用</option>
                                    </select>
                                    <div class="form-select-arrow"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-input-group" style="margin-bottom: 16px;">
                            <label class="form-input-label">手机号码</label>
                            <input type="tel" class="form-input" name="phone" id="editUserPhone" placeholder="请输入手机号码">
                        </div>

                        <div class="form-radio" style="margin-bottom: 16px;">
                            <div class="form-radio-title">性别</div>
                            <div class="form-radio-horizontal">
                                <label class="form-radio-item">
                                    <input type="radio" name="editGender" value="男">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">男</span>
                                </label>
                                <label class="form-radio-item">
                                    <input type="radio" name="editGender" value="女">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">女</span>
                                </label>
                                <label class="form-radio-item">
                                    <input type="radio" name="editGender" value="其他">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">其他</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-checkbox" style="margin-bottom: 16px;">
                            <div class="form-checkbox-title">权限设置</div>
                            <div class="form-checkbox-horizontal form-checkbox-two-columns">
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="editPermissions" value="read">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">查看权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="editPermissions" value="write">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">编辑权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="editPermissions" value="delete">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">删除权限</span>
                                </label>
                                <label class="form-checkbox-item">
                                    <input type="checkbox" name="editPermissions" value="admin">
                                    <span class="form-checkbox-custom"></span>
                                    <span class="form-checkbox-label">管理权限</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-textarea-group">
                            <label class="form-textarea-label">备注信息</label>
                            <textarea class="form-textarea-input" name="remarks" id="editUserRemarks" placeholder="请输入备注信息..." rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="table-modal-footer">
                    <button type="button" class="table-modal-button secondary" onclick="hideUserModal('editUserModal')">
                        取消
                    </button>
                    <button type="button" class="table-modal-button primary" onclick="saveUser('edit')">
                        更新
                    </button>
                </div>
            </div>
        </div>

        <script>
            // 表格排序功能
            let sortDirection = {};
            
            function sortTable(columnIndex) {
                const table = document.getElementById('userTable');
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));
                const headers = table.querySelectorAll('th');
                
                // 重置所有列的排序状态
                headers.forEach(header => {
                    header.classList.remove('sort-asc', 'sort-desc');
                });
                
                // 确定排序方向
                const currentDirection = sortDirection[columnIndex] || 'asc';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                sortDirection[columnIndex] = newDirection;
                
                // 添加排序状态类
                headers[columnIndex + 1].classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                
                // 排序行
                rows.sort((a, b) => {
                    const aValue = a.cells[columnIndex + 1].textContent.trim();
                    const bValue = b.cells[columnIndex + 1].textContent.trim();
                    
                    // 数字排序
                    if (!isNaN(aValue) && !isNaN(bValue)) {
                        return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                    }
                    
                    // 文本排序
                    if (newDirection === 'asc') {
                        return aValue.localeCompare(bValue);
                    } else {
                        return bValue.localeCompare(aValue);
                    }
                });
                
                // 重新插入排序后的行
                rows.forEach(row => tbody.appendChild(row));
            }
            
            // 搜索过滤功能
            function filterTable(searchTerm) {
                const table = document.getElementById('userTable');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    row.style.display = matches ? '' : 'none';
                });
            }
            
            // 全选功能
            function toggleAllRows(checkbox) {
                const table = document.getElementById('userTable');
                const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
                
                checkboxes.forEach(cb => {
                    cb.checked = checkbox.checked;
                    toggleRow(cb);
                });
            }
            
            // 单行选择功能
            function toggleRow(checkbox) {
                const row = checkbox.closest('tr');
                if (checkbox.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
                
                // 更新全选状态
                const table = document.getElementById('userTable');
                const allCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
                const checkedCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
                const headerCheckbox = table.querySelector('thead input[type="checkbox"]');
                
                headerCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                headerCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
            
            // 高级搜索功能
            function toggleAdvancedSearch() {
                const searchPanel = document.getElementById('advancedSearch');
                const toggleBtn = document.querySelector('.form-table-search-toggle');
                
                searchPanel.classList.toggle('show');
                toggleBtn.classList.toggle('active');
                
                if (searchPanel.classList.contains('show')) {
                    toggleBtn.textContent = '收起搜索';
                } else {
                    toggleBtn.textContent = '高级搜索';
                }
            }

            function resetAdvancedSearch() {
                document.getElementById('searchName').value = '';
                document.getElementById('searchDepartment').value = '';
                document.getElementById('searchStatus').value = '';
                document.getElementById('searchDate').value = '';
                
                // 重置快速搜索
                document.getElementById('quickSearch').value = '';
                
                // 显示所有行
                const table = document.getElementById('userTable');
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.style.display = '';
                });
            }

            function performAdvancedSearch(event) {
                if (event) event.preventDefault();
                
                const searchName = document.getElementById('searchName').value.toLowerCase();
                const searchDepartment = document.getElementById('searchDepartment').value;
                const searchStatus = document.getElementById('searchStatus').value;
                const searchDate = document.getElementById('searchDate').value;
                
                const table = document.getElementById('userTable');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length < 6) return;
                    
                    const name = cells[2].textContent.toLowerCase();
                    const department = cells[4].textContent;
                    const status = cells[5].textContent.replace(/\s+/g, '');
                    const date = cells[6].textContent;
                    
                    let matches = true;
                    
                    // 姓名匹配
                    if (searchName && !name.includes(searchName)) {
                        matches = false;
                    }
                    
                    // 部门匹配
                    if (searchDepartment && department !== searchDepartment) {
                        matches = false;
                    }
                    
                    // 状态匹配
                    if (searchStatus && !status.includes(searchStatus)) {
                        matches = false;
                    }
                    
                    // 日期匹配
                    if (searchDate && date !== searchDate) {
                        matches = false;
                    }
                    
                    row.style.display = matches ? '' : 'none';
                });
            }

            // 新增用户弹窗功能
            function showAddUserModal() {
                // 重置表单
                document.getElementById('addUserForm').reset();
                
                // 显示弹窗
                showUserModal('addUserModal');
            }

            function showUserModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    
                    // 添加ESC键关闭功能
                    document.addEventListener('keydown', handleModalEscKey);
                    
                    // 点击遮罩关闭
                    modal.addEventListener('click', handleModalOverlayClick);
                }
            }

            function hideUserModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                    
                    // 移除事件监听
                    document.removeEventListener('keydown', handleModalEscKey);
                    modal.removeEventListener('click', handleModalOverlayClick);
                }
            }

            function handleModalEscKey(e) {
                if (e.key === 'Escape') {
                    const openModal = document.querySelector('.table-modal-overlay.show');
                    if (openModal) {
                        hideUserModal(openModal.id);
                    }
                }
            }

            function handleModalOverlayClick(e) {
                if (e.target === e.currentTarget) {
                    hideUserModal(e.currentTarget.id);
                }
            }
            
            // 导出表格功能
            function exportTable() {
                // 简单的CSV导出示例
                const table = document.getElementById('userTable');
                if (!table) {
                    alert('导出功能演示');
                    return;
                }
                
                let csv = '';
                const rows = table.querySelectorAll('tr');
                
                rows.forEach(row => {
                    const cols = row.querySelectorAll('th, td');
                    const rowData = [];
                    cols.forEach((col, index) => {
                        // 跳过复选框和操作列
                        if (index !== 0 && index !== cols.length - 1) {
                            rowData.push('"' + col.textContent.trim() + '"');
                        }
                    });
                    if (rowData.length > 0) {
                        csv += rowData.join(',') + '\n';
                    }
                });
                
                // 创建下载链接
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'table_data.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            
            // 表格行操作演示
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('form-table-action-btn')) {
                    e.preventDefault();
                    
                    const action = e.target.textContent.trim();
                    const row = e.target.closest('tr');
                    const name = row.cells[2] ? row.cells[2].textContent.trim() : '该项';
                    
                    switch(action) {
                        case '编辑':
                            showEditUserModal(row);
                            break;
                        case '删除':
                        case '下架':
                            if (confirm(`确定要${action} ${name} 吗？`)) {
                                row.style.opacity = '0.5';
                                setTimeout(() => {
                                    row.remove();
                                }, 300);
                            }
                            break;
                        case '查看':
                            alert(`查看 ${name} 的详细信息`);
                            break;
                        case '发布':
                            alert(`发布 ${name}`);
                            // 更新状态
                            const statusSpan = row.querySelector('.form-table-status');
                            if (statusSpan) {
                                statusSpan.textContent = '已发布';
                                statusSpan.className = 'form-table-status active';
                            }
                            break;
                        default:
                            alert(`执行 ${action} 操作`);
                    }
                }
            });

            // 编辑用户功能
            function showEditUserModal(row) {
                const cells = row.querySelectorAll('td');
                if (cells.length < 7) return;
                
                // 填充表单数据
                document.getElementById('editUserId').value = cells[1].textContent.trim();
                document.getElementById('editUserName').value = cells[2].textContent.trim();
                document.getElementById('editUserEmail').value = cells[3].textContent.trim();
                document.getElementById('editUserDepartment').value = cells[4].textContent.trim();
                
                // 处理状态
                const statusText = cells[5].textContent.trim();
                const statusMap = {
                    '活跃': '活跃',
                    '待审核': '待审核',
                    '禁用': '禁用'
                };
                document.getElementById('editUserStatus').value = statusMap[statusText] || '活跃';
                
                // 模拟一些额外数据
                document.getElementById('editUserPhone').value = '138-0000-' + cells[1].textContent.padStart(4, '0');
                document.getElementById('editUserRemarks').value = `${cells[2].textContent.trim()}的备注信息`;
                
                // 设置性别（随机）
                const genders = ['男', '女', '其他'];
                const randomGender = genders[Math.floor(Math.random() * genders.length)];
                const genderRadio = document.querySelector(`input[name="editGender"][value="${randomGender}"]`);
                if (genderRadio) genderRadio.checked = true;
                
                // 设置权限（随机）
                const permissions = ['read', 'write', 'delete', 'admin'];
                const randomPermissions = permissions.slice(0, Math.floor(Math.random() * 3) + 1);
                
                // 首先清除所有权限选择
                document.querySelectorAll('input[name="editPermissions"]').forEach(cb => cb.checked = false);
                
                // 设置随机权限
                randomPermissions.forEach(perm => {
                    const permCheckbox = document.querySelector(`input[name="editPermissions"][value="${perm}"]`);
                    if (permCheckbox) permCheckbox.checked = true;
                });
                
                // 显示编辑弹窗
                showUserModal('editUserModal');
            }

            // 保存用户功能
            function saveUser(mode) {
                const form = document.getElementById(mode === 'add' ? 'addUserForm' : 'editUserForm');
                const formData = new FormData(form);
                
                // 简单验证
                const name = formData.get('name');
                const email = formData.get('email');
                const department = formData.get('department');
                
                if (!name || !email || !department) {
                    alert('请填写必填字段！');
                    return;
                }
                
                // 邮箱格式验证
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    alert('请输入正确的邮箱格式！');
                    return;
                }
                
                if (mode === 'add') {
                    addUserToTable(formData);
                    hideUserModal('addUserModal');
                    showSuccessMessage('用户添加成功！');
                } else {
                    updateUserInTable(formData);
                    hideUserModal('editUserModal');
                    showSuccessMessage('用户更新成功！');
                }
            }

            // 添加用户到表格
            function addUserToTable(formData) {
                const table = document.getElementById('userTable');
                const tbody = table.querySelector('tbody');
                
                // 生成新的ID
                const rows = tbody.querySelectorAll('tr');
                const newId = (rows.length + 1).toString().padStart(3, '0');
                
                // 获取权限
                const permissions = formData.getAll('permissions');
                const permissionsText = permissions.length > 0 ? permissions.join(', ') : '无权限';
                
                // 创建新行
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                    <td>${newId}</td>
                    <td>${formData.get('name')}</td>
                    <td>${formData.get('email')}</td>
                    <td>${formData.get('department')}</td>
                    <td><span class="form-table-status ${getStatusClass(formData.get('status'))}">${formData.get('status')}</span></td>
                    <td>${new Date().toISOString().split('T')[0]}</td>
                    <td class="form-table-actions-cell">
                        <button class="form-table-action-btn edit">编辑</button>
                        <button class="form-table-action-btn delete">删除</button>
                    </td>
                `;
                
                // 添加到表格
                tbody.appendChild(newRow);
                
                // 添加动画效果
                newRow.style.opacity = '0';
                newRow.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    newRow.style.transition = 'all 0.3s ease';
                    newRow.style.opacity = '1';
                    newRow.style.transform = 'translateY(0)';
                }, 10);
            }

            // 更新表格中的用户
            function updateUserInTable(formData) {
                const userId = formData.get('id');
                const table = document.getElementById('userTable');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const idCell = row.querySelector('td:nth-child(2)');
                    if (idCell && idCell.textContent.trim() === userId) {
                        const cells = row.querySelectorAll('td');
                        
                        // 更新数据
                        cells[2].textContent = formData.get('name');
                        cells[3].textContent = formData.get('email');
                        cells[4].textContent = formData.get('department');
                        
                        // 更新状态
                        const status = formData.get('status');
                        const statusSpan = cells[5].querySelector('.form-table-status');
                        statusSpan.textContent = status;
                        statusSpan.className = `form-table-status ${getStatusClass(status)}`;
                        
                        // 添加更新动画
                        row.style.backgroundColor = '#f0f9ff';
                        setTimeout(() => {
                            row.style.transition = 'background-color 0.5s ease';
                            row.style.backgroundColor = '';
                        }, 100);
                    }
                });
            }

            // 获取状态对应的CSS类
            function getStatusClass(status) {
                const statusMap = {
                    '活跃': 'active',
                    '待审核': 'pending',
                    '禁用': 'inactive'
                };
                return statusMap[status] || 'pending';
            }

            // 显示成功消息
            function showSuccessMessage(message) {
                // 创建临时提示
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                    z-index: 10000;
                    font-size: 14px;
                    font-weight: 500;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                `;
                toast.textContent = message;
                
                document.body.appendChild(toast);
                
                // 显示动画
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 10);
                
                // 自动隐藏
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }
        </script>
    </div>

    <!-- 使用说明 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>表格组件特性</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
            <div>
                <h4 style="color: #374151; margin-bottom: 8px;">✨ 多种风格</h4>
                <ul style="margin: 0; padding-left: 20px; color: #6b7280;">
                    <li>基础表格：默认样式</li>
                    <li>条纹表格：交替行颜色</li>
                    <li>边框表格：完整边框</li>
                    <li>紧凑表格：较小间距</li>
                    <li>深色主题：暗色风格</li>
                    <li>彩色主题：渐变表头</li>
                    <li>卡片风格：圆角阴影</li>
                    <li>简约风格：极简设计</li>
                </ul>
            </div>
            <div>
                <h4 style="color: #374151; margin-bottom: 8px;">🚀 强大功能</h4>
                <ul style="margin: 0; padding-left: 20px; color: #6b7280;">
                    <li>列排序：点击表头排序</li>
                    <li>搜索过滤：实时筛选数据</li>
                    <li>行选择：单选和多选</li>
                    <li>分页控制：数据分页显示</li>
                    <li>响应式：移动端适配</li>
                    <li>导出功能：CSV格式导出</li>
                    <li>空状态：无数据提示</li>
                    <li>行操作：编辑删除等</li>
                    <li>多条件搜索：组合筛选</li>
                    <li>弹窗表单：新增编辑用户</li>
                </ul>
            </div>
            <div>
                <h4 style="color: #374151; margin-bottom: 8px;">🎨 美观按钮</h4>
                <ul style="margin: 0; padding-left: 20px; color: #6b7280;">
                    <li>渐变新增按钮：炫酷动效</li>
                    <li>悬停动画：光泽扫过效果</li>
                    <li>按钮提升：3D悬浮感</li>
                    <li>图标装饰：直观识别</li>
                    <li>导出按钮：简洁边框样式</li>
                    <li>操作按钮：彩色分类</li>
                </ul>
            </div>
        </div>

        <h3>样式类名说明</h3>
        <div style="background: white; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f3f4f6;">
                        <th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">类名</th>
                        <th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">说明</th>
                        <th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-striped</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">条纹表格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">偶数行背景色</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-bordered</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">边框表格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">完整单元格边框</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-compact</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">紧凑表格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">较小的内边距</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-large</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">大尺寸表格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">较大的内边距</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-dark</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">深色主题</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">暗色背景和文字</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-colorful</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">彩色主题</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">渐变色表头</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-card</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">卡片风格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">圆角和阴影</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;"><code>form-table-minimal</code></td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">简约风格</td>
                        <td style="padding: 8px; border: 1px solid #e5e7eb;">极简边框设计</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h3>基础用法示例</h3>
        <pre style="background: white; padding: 16px; border-radius: 6px; overflow-x: auto;"><code>&lt;!-- 基础表格 --&gt;
&lt;div class="form-table-container"&gt;
    &lt;div class="form-table-header"&gt;
        &lt;h3 class="form-table-title"&gt;数据表格&lt;/h3&gt;
        &lt;div class="form-table-actions"&gt;
            &lt;button class="form-table-add-btn"&gt;
                &lt;span class="form-table-add-icon"&gt;+&lt;/span&gt;
                新增数据
            &lt;/button&gt;
            &lt;button class="form-table-export-btn"&gt;
                &lt;span class="form-table-export-icon"&gt;📊&lt;/span&gt;
                导出
            &lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="form-table-wrapper"&gt;
        &lt;table class="form-table-element"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th class="sortable" onclick="sortTable(0)"&gt;列名&lt;/th&gt;
                    &lt;th&gt;其他列&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;tr&gt;
                    &lt;td&gt;数据&lt;/td&gt;
                    &lt;td&gt;其他数据&lt;/td&gt;
                &lt;/tr&gt;
            &lt;/tbody&gt;
        &lt;/table&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 不同风格示例 --&gt;
&lt;div class="form-table-container form-table-striped"&gt;&lt;!-- 条纹 --&gt;&lt;/div&gt;
&lt;div class="form-table-container form-table-bordered"&gt;&lt;!-- 边框 --&gt;&lt;/div&gt;
&lt;div class="form-table-container form-table-dark"&gt;&lt;!-- 深色 --&gt;&lt;/div&gt;
&lt;div class="form-table-container form-table-card"&gt;&lt;!-- 卡片 --&gt;&lt;/div&gt;</code></pre>
        
        <h3>JavaScript功能</h3>
        <pre style="background: white; padding: 16px; border-radius: 6px; overflow-x: auto;"><code>// 基础功能
sortTable(columnIndex)              // 排序功能
filterTable(searchTerm)             // 快速搜索过滤
toggleAllRows(checkbox)             // 全选/取消全选
toggleRow(checkbox)                 // 单行选择
exportTable()                       // 导出数据

// 高级搜索
toggleAdvancedSearch()              // 显示/隐藏高级搜索
performAdvancedSearch()             // 执行多条件搜索
resetAdvancedSearch()               // 重置搜索条件

// 用户管理
showAddUserModal()                  // 显示新增用户弹窗
showEditUserModal(row)              // 显示编辑用户弹窗
saveUser(mode)                      // 保存用户（add/edit）
showUserModal(modalId)              // 显示弹窗
hideUserModal(modalId)              // 隐藏弹窗</code></pre>

        <h3>新增功能特性</h3>
        <div style="background: white; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="color: #374151; margin-bottom: 12px;">🔍 多条件搜索</h4>
            <ul style="margin: 0 0 16px 20px; color: #6b7280;">
                <li><strong>姓名搜索</strong>：支持模糊匹配用户姓名</li>
                <li><strong>部门筛选</strong>：下拉选择特定部门</li>
                <li><strong>状态筛选</strong>：按用户状态过滤</li>
                <li><strong>日期筛选</strong>：按注册日期筛选</li>
                <li><strong>组合搜索</strong>：多个条件同时使用</li>
                <li><strong>一键重置</strong>：快速清空所有搜索条件</li>
            </ul>

            <h4 style="color: #374151; margin-bottom: 12px;">➕ 新增用户功能</h4>
            <ul style="margin: 0 0 16px 20px; color: #6b7280;">
                <li><strong>完整表单</strong>：使用现有组件构建的完整用户表单</li>
                <li><strong>字段验证</strong>：必填字段和邮箱格式验证</li>
                <li><strong>权限设置</strong>：多选权限配置</li>
                <li><strong>实时添加</strong>：新用户立即显示在表格中</li>
                <li><strong>动画效果</strong>：流畅的添加动画</li>
            </ul>

            <h4 style="color: #374151; margin-bottom: 12px;">✏️ 编辑用户功能</h4>
            <ul style="margin: 0 0 16px 20px; color: #6b7280;">
                <li><strong>数据回填</strong>：自动填充现有用户数据</li>
                <li><strong>实时更新</strong>：修改后立即更新表格显示</li>
                <li><strong>状态同步</strong>：状态变更同步到表格</li>
                <li><strong>视觉反馈</strong>：更新后的行高亮显示</li>
            </ul>

            <h4 style="color: #374151; margin-bottom: 12px;">🎨 用户体验增强</h4>
            <ul style="margin: 0 0 16px 20px; color: #6b7280;">
                <li><strong>成功提示</strong>：操作成功后的Toast提示</li>
                <li><strong>ESC关闭</strong>：键盘快捷键关闭弹窗</li>
                <li><strong>点击遮罩关闭</strong>：点击弹窗外部关闭</li>
                <li><strong>表单重置</strong>：弹窗打开时自动重置表单</li>
                <li><strong>响应式设计</strong>：完美适配移动端</li>
            </ul>
        </div>
    </div>
</body>
</html>
