<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格组件</title>
</head>
<body>
    <!-- 表格组件 -->
    <div class="form-table">
        <style>
            .form-table {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-table-container {
                background: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                margin-bottom: 20px;
            }

            .form-table-header {
                padding: 16px 20px;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 12px;
            }

            .form-table-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin: 0;
            }

            .form-table-actions {
                display: flex;
                gap: 8px;
                align-items: center;
            }

            .form-table-search {
                position: relative;
                min-width: 200px;
            }

            .form-table-search input {
                width: 100%;
                padding: 8px 32px 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                outline: none;
                transition: border-color 0.2s ease;
            }

            .form-table-search input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }

            .form-table-search-icon {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                color: #6b7280;
                pointer-events: none;
            }

            .form-table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .form-table-element {
                width: 100%;
                border-collapse: collapse;
                font-size: 14px;
            }

            .form-table-element th,
            .form-table-element td {
                padding: 12px 16px;
                text-align: left;
                border-bottom: 1px solid #f3f4f6;
                vertical-align: middle;
            }

            .form-table-element th {
                background-color: #f9fafb;
                font-weight: 600;
                color: #374151;
                position: sticky;
                top: 0;
                z-index: 10;
                white-space: nowrap;
                user-select: none;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .form-table-element th:hover {
                background-color: #f3f4f6;
            }

            .form-table-element th.sortable {
                position: relative;
                padding-right: 32px;
            }

            .form-table-element th.sortable::after {
                content: '↕';
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                color: #9ca3af;
                font-size: 12px;
                transition: color 0.2s ease;
            }

            .form-table-element th.sort-asc::after {
                content: '↑';
                color: #3b82f6;
            }

            .form-table-element th.sort-desc::after {
                content: '↓';
                color: #3b82f6;
            }

            .form-table-element td {
                color: #6b7280;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .form-table-element tr:hover {
                background-color: #f9fafb;
            }

            .form-table-element tr.selected {
                background-color: #eff6ff;
            }

            .form-table-checkbox {
                width: 16px;
                height: 16px;
                cursor: pointer;
            }

            .form-table-actions-cell {
                white-space: nowrap;
            }

            .form-table-action-btn {
                padding: 4px 8px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                margin-right: 4px;
                transition: all 0.2s ease;
            }

            .form-table-action-btn.edit {
                background-color: #dbeafe;
                color: #1e40af;
            }

            .form-table-action-btn.edit:hover {
                background-color: #bfdbfe;
            }

            .form-table-action-btn.delete {
                background-color: #fee2e2;
                color: #dc2626;
            }

            .form-table-action-btn.delete:hover {
                background-color: #fecaca;
            }

            .form-table-pagination {
                padding: 16px 20px;
                border-top: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 12px;
            }

            .form-table-pagination-info {
                font-size: 14px;
                color: #6b7280;
            }

            .form-table-pagination-controls {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .form-table-pagination-btn {
                padding: 6px 12px;
                border: 1px solid #d1d5db;
                background: white;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 32px;
                text-align: center;
            }

            .form-table-pagination-btn:hover:not(:disabled) {
                border-color: #3b82f6;
                color: #3b82f6;
            }

            .form-table-pagination-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .form-table-pagination-btn.active {
                background-color: #3b82f6;
                color: white;
                border-color: #3b82f6;
            }

            .form-table-status {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
            }

            .form-table-status.active {
                background-color: #dcfce7;
                color: #166534;
            }

            .form-table-status.inactive {
                background-color: #fee2e2;
                color: #dc2626;
            }

            .form-table-status.pending {
                background-color: #fef3c7;
                color: #d97706;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .form-table-header {
                    flex-direction: column;
                    align-items: stretch;
                }

                .form-table-search {
                    min-width: auto;
                }

                .form-table-element th,
                .form-table-element td {
                    padding: 8px 12px;
                    font-size: 13px;
                }

                .form-table-pagination {
                    flex-direction: column;
                    align-items: stretch;
                    text-align: center;
                }

                .form-table-pagination-controls {
                    justify-content: center;
                }
            }

            /* 空状态 */
            .form-table-empty {
                text-align: center;
                padding: 40px 20px;
                color: #9ca3af;
            }

            .form-table-empty-icon {
                font-size: 48px;
                margin-bottom: 16px;
                opacity: 0.5;
            }

            .form-table-empty-text {
                font-size: 16px;
                margin-bottom: 8px;
            }

            .form-table-empty-desc {
                font-size: 14px;
                color: #6b7280;
            }
        </style>

        <!-- 基础表格示例 -->
        <div class="form-table-container">
            <div class="form-table-header">
                <h3 class="form-table-title">用户列表</h3>
                <div class="form-table-actions">
                    <div class="form-table-search">
                        <input type="text" placeholder="搜索用户..." onkeyup="filterTable(this.value)">
                        <span class="form-table-search-icon">🔍</span>
                    </div>
                    <button class="form-button form-button-primary" style="padding: 8px 16px; font-size: 14px;">
                        新增用户
                    </button>
                </div>
            </div>
            
            <div class="form-table-wrapper">
                <table class="form-table-element" id="userTable">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <input type="checkbox" class="form-table-checkbox" onchange="toggleAllRows(this)">
                            </th>
                            <th class="sortable" onclick="sortTable(0)">ID</th>
                            <th class="sortable" onclick="sortTable(1)">姓名</th>
                            <th class="sortable" onclick="sortTable(2)">邮箱</th>
                            <th class="sortable" onclick="sortTable(3)">部门</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>001</td>
                            <td>张三</td>
                            <td><EMAIL></td>
                            <td>技术部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-15</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>002</td>
                            <td>李四</td>
                            <td><EMAIL></td>
                            <td>市场部</td>
                            <td><span class="form-table-status pending">待审核</span></td>
                            <td>2024-01-14</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>003</td>
                            <td>王五</td>
                            <td><EMAIL></td>
                            <td>人事部</td>
                            <td><span class="form-table-status inactive">禁用</span></td>
                            <td>2024-01-13</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>004</td>
                            <td>赵六</td>
                            <td><EMAIL></td>
                            <td>财务部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-12</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="form-table-checkbox" onchange="toggleRow(this)"></td>
                            <td>005</td>
                            <td>孙七</td>
                            <td><EMAIL></td>
                            <td>技术部</td>
                            <td><span class="form-table-status active">活跃</span></td>
                            <td>2024-01-11</td>
                            <td class="form-table-actions-cell">
                                <button class="form-table-action-btn edit">编辑</button>
                                <button class="form-table-action-btn delete">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="form-table-pagination">
                <div class="form-table-pagination-info">
                    显示 1-5 条，共 25 条记录
                </div>
                <div class="form-table-pagination-controls">
                    <button class="form-table-pagination-btn" disabled>上一页</button>
                    <button class="form-table-pagination-btn active">1</button>
                    <button class="form-table-pagination-btn">2</button>
                    <button class="form-table-pagination-btn">3</button>
                    <button class="form-table-pagination-btn">...</button>
                    <button class="form-table-pagination-btn">5</button>
                    <button class="form-table-pagination-btn">下一页</button>
                </div>
            </div>
        </div>

        <!-- 空状态表格示例 -->
        <div class="form-table-container">
            <div class="form-table-header">
                <h3 class="form-table-title">空数据表格</h3>
            </div>
            <div class="form-table-empty">
                <div class="form-table-empty-icon">📋</div>
                <div class="form-table-empty-text">暂无数据</div>
                <div class="form-table-empty-desc">当前没有任何记录，请添加新数据</div>
            </div>
        </div>

        <script>
            // 表格排序功能
            let sortDirection = {};
            
            function sortTable(columnIndex) {
                const table = document.getElementById('userTable');
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));
                const headers = table.querySelectorAll('th');
                
                // 重置所有列的排序状态
                headers.forEach(header => {
                    header.classList.remove('sort-asc', 'sort-desc');
                });
                
                // 确定排序方向
                const currentDirection = sortDirection[columnIndex] || 'asc';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                sortDirection[columnIndex] = newDirection;
                
                // 添加排序状态类
                headers[columnIndex + 1].classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                
                // 排序行
                rows.sort((a, b) => {
                    const aValue = a.cells[columnIndex + 1].textContent.trim();
                    const bValue = b.cells[columnIndex + 1].textContent.trim();
                    
                    // 数字排序
                    if (!isNaN(aValue) && !isNaN(bValue)) {
                        return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                    }
                    
                    // 文本排序
                    if (newDirection === 'asc') {
                        return aValue.localeCompare(bValue);
                    } else {
                        return bValue.localeCompare(aValue);
                    }
                });
                
                // 重新插入排序后的行
                rows.forEach(row => tbody.appendChild(row));
            }
            
            // 搜索过滤功能
            function filterTable(searchTerm) {
                const table = document.getElementById('userTable');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    row.style.display = matches ? '' : 'none';
                });
            }
            
            // 全选功能
            function toggleAllRows(checkbox) {
                const table = document.getElementById('userTable');
                const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
                
                checkboxes.forEach(cb => {
                    cb.checked = checkbox.checked;
                    toggleRow(cb);
                });
            }
            
            // 单行选择功能
            function toggleRow(checkbox) {
                const row = checkbox.closest('tr');
                if (checkbox.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
                
                // 更新全选状态
                const table = document.getElementById('userTable');
                const allCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
                const checkedCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
                const headerCheckbox = table.querySelector('thead input[type="checkbox"]');
                
                headerCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                headerCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-table-container"&gt;
    &lt;div class="form-table-header"&gt;
        &lt;h3 class="form-table-title"&gt;数据表格&lt;/h3&gt;
        &lt;div class="form-table-actions"&gt;
            &lt;div class="form-table-search"&gt;
                &lt;input type="text" placeholder="搜索..."&gt;
                &lt;span class="form-table-search-icon"&gt;🔍&lt;/span&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="form-table-wrapper"&gt;
        &lt;table class="form-table-element"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th class="sortable"&gt;列名&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;tr&gt;
                    &lt;td&gt;数据&lt;/td&gt;
                &lt;/tr&gt;
            &lt;/tbody&gt;
        &lt;/table&gt;
    &lt;/div&gt;
    
    &lt;div class="form-table-pagination"&gt;
        &lt;!-- 分页控件 --&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
