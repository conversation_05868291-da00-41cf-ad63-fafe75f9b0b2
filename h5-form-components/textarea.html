<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多行文本框组件</title>
</head>
<body>
    <!-- 多行文本框组件 -->
    <div class="form-textarea">
        <style>
            .form-textarea {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-textarea-group {
                margin-bottom: 20px;
            }

            .form-textarea-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-textarea-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-textarea-input {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                box-sizing: border-box;
                resize: vertical;
                min-height: 120px;
                line-height: 1.5;
                font-family: inherit;
            }

            .form-textarea-input::placeholder {
                color: #9ca3af;
            }

            .form-textarea-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-textarea-input:hover:not(:focus) {
                border-color: #9ca3af;
            }

            /* 错误状态 */
            .form-textarea-input.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            .form-textarea-input.error:focus {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 成功状态 */
            .form-textarea-input.success {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-textarea-input.success:focus {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-textarea-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-textarea-message.error {
                color: #ef4444;
            }

            .form-textarea-message.success {
                color: #10b981;
            }

            .form-textarea-message.help {
                color: #6b7280;
            }

            .form-textarea-counter {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 6px;
                font-size: 14px;
                color: #6b7280;
            }

            .form-textarea-counter.warning {
                color: #f59e0b;
            }

            .form-textarea-counter.error {
                color: #ef4444;
            }

            /* 自动调整高度的文本框 */
            .form-textarea-auto {
                min-height: 80px;
                max-height: 300px;
                overflow-y: auto;
            }

            /* 固定高度的文本框 */
            .form-textarea-fixed {
                height: 150px;
                resize: none;
            }

            @media (max-width: 480px) {
                .form-textarea {
                    margin: 12px 0;
                }

                .form-textarea-input {
                    padding: 14px 16px;
                    font-size: 16px;
                    min-height: 100px;
                }

                .form-textarea-label {
                    font-size: 16px;
                }

                .form-textarea-auto {
                    min-height: 80px;
                    max-height: 250px;
                }
            }
        </style>

        <!-- 普通多行文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="description">
                项目描述 <span class="form-textarea-required">*</span>
            </label>
            <textarea 
                id="description" 
                name="description" 
                class="form-textarea-input" 
                placeholder="请详细描述您的项目内容、目标和预期成果..."
                required
            ></textarea>
            <div class="form-textarea-message help">请详细描述项目的背景、目标和预期成果</div>
        </div>

        <!-- 带字符计数的文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="feedback">
                意见反馈
            </label>
            <textarea 
                id="feedback" 
                name="feedback" 
                class="form-textarea-input" 
                placeholder="请输入您的意见或建议..."
                maxlength="500"
                oninput="updateCounter(this, 'feedback-counter')"
            ></textarea>
            <div id="feedback-counter" class="form-textarea-counter">
                <span class="form-textarea-message help">您的反馈对我们很重要</span>
                <span>0/500</span>
            </div>
        </div>

        <!-- 错误状态文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="comment-error">
                评论内容 <span class="form-textarea-required">*</span>
            </label>
            <textarea 
                id="comment-error" 
                name="comment" 
                class="form-textarea-input error" 
                placeholder="请输入评论内容..."
                required
            >这是一个过短的评论</textarea>
            <div class="form-textarea-message error">评论内容至少需要20个字符</div>
        </div>

        <!-- 成功状态文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="review">
                产品评价
            </label>
            <textarea 
                id="review" 
                name="review" 
                class="form-textarea-input success" 
                placeholder="请分享您的使用体验..."
            >这个产品非常好用，界面简洁，功能强大，完全满足了我的需求。客服响应也很及时，整体体验非常满意！</textarea>
            <div class="form-textarea-message success">感谢您的详细评价</div>
        </div>

        <!-- 自动调整高度的文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="notes">
                备注信息
            </label>
            <textarea 
                id="notes" 
                name="notes" 
                class="form-textarea-input form-textarea-auto" 
                placeholder="请输入备注信息..."
                oninput="autoResize(this)"
            ></textarea>
            <div class="form-textarea-message help">文本框会根据内容自动调整高度</div>
        </div>

        <!-- 固定高度的文本框 -->
        <div class="form-textarea-group">
            <label class="form-textarea-label" for="content">
                文章内容
            </label>
            <textarea 
                id="content" 
                name="content" 
                class="form-textarea-input form-textarea-fixed" 
                placeholder="请输入文章内容..."
            ></textarea>
            <div class="form-textarea-message help">固定高度，不可调整大小</div>
        </div>

        <script>
            // 字符计数功能
            function updateCounter(textarea, counterId) {
                const counter = document.getElementById(counterId);
                const current = textarea.value.length;
                const max = textarea.maxLength;
                const counterSpan = counter.querySelector('span:last-child');
                
                counterSpan.textContent = current + '/' + max;
                
                // 根据字符数变化样式
                counter.classList.remove('warning', 'error');
                if (current > max * 0.9) {
                    counter.classList.add('error');
                } else if (current > max * 0.8) {
                    counter.classList.add('warning');
                }
            }
            
            // 自动调整高度功能
            function autoResize(textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 300) + 'px';
            }
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-textarea-group"&gt;
    &lt;label class="form-textarea-label" for="description"&gt;
        项目描述 &lt;span class="form-textarea-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;textarea 
        id="description" 
        name="description" 
        class="form-textarea-input" 
        placeholder="请详细描述您的项目内容..."
        required
    &gt;&lt;/textarea&gt;
    &lt;div class="form-textarea-message help"&gt;请详细描述项目的背景和目标&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 带字符计数 --&gt;
&lt;textarea 
    class="form-textarea-input" 
    maxlength="500"
    oninput="updateCounter(this, 'counter-id')"&gt;
&lt;/textarea&gt;
&lt;div id="counter-id" class="form-textarea-counter"&gt;
    &lt;span class="form-textarea-message help"&gt;帮助信息&lt;/span&gt;
    &lt;span&gt;0/500&lt;/span&gt;
&lt;/div&gt;

&lt;!-- 自动调整高度 --&gt;
&lt;textarea 
    class="form-textarea-input form-textarea-auto" 
    oninput="autoResize(this)"&gt;
&lt;/textarea&gt;

&lt;!-- 错误状态 --&gt;
&lt;textarea class="form-textarea-input error" ... &gt;&lt;/textarea&gt;
&lt;div class="form-textarea-message error"&gt;错误信息&lt;/div&gt;

&lt;!-- 成功状态 --&gt;
&lt;textarea class="form-textarea-input success" ... &gt;&lt;/textarea&gt;
&lt;div class="form-textarea-message success"&gt;成功信息&lt;/div&gt;</code></pre>
    </div>
</body>
</html>