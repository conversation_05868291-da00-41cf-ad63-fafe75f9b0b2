<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局组件</title>
</head>
<body>
    <!-- 布局组件 -->
    <div class="form-layout">
        <style>
            .form-layout {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-layout-section {
                margin-bottom: 40px;
            }

            .form-layout-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 容器 */
            .form-container {
                width: 100%;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 16px;
                box-sizing: border-box;
            }

            .form-container.fluid {
                max-width: none;
            }

            .form-container.narrow {
                max-width: 800px;
            }

            .form-container.wide {
                max-width: 1400px;
            }

            /* 栅格系统 */
            .form-row {
                display: flex;
                flex-wrap: wrap;
                margin: 0 -8px;
            }

            .form-col {
                flex: 1;
                padding: 0 8px;
                box-sizing: border-box;
            }

            /* 12列栅格 */
            .form-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
            .form-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
            .form-col-3 { flex: 0 0 25%; max-width: 25%; }
            .form-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
            .form-col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
            .form-col-6 { flex: 0 0 50%; max-width: 50%; }
            .form-col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
            .form-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
            .form-col-9 { flex: 0 0 75%; max-width: 75%; }
            .form-col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
            .form-col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
            .form-col-12 { flex: 0 0 100%; max-width: 100%; }

            /* 偏移 */
            .form-col-offset-1 { margin-left: 8.333333%; }
            .form-col-offset-2 { margin-left: 16.666667%; }
            .form-col-offset-3 { margin-left: 25%; }
            .form-col-offset-4 { margin-left: 33.333333%; }
            .form-col-offset-5 { margin-left: 41.666667%; }
            .form-col-offset-6 { margin-left: 50%; }
            .form-col-offset-7 { margin-left: 58.333333%; }
            .form-col-offset-8 { margin-left: 66.666667%; }
            .form-col-offset-9 { margin-left: 75%; }
            .form-col-offset-10 { margin-left: 83.333333%; }
            .form-col-offset-11 { margin-left: 91.666667%; }

            /* Flex布局 */
            .form-flex {
                display: flex;
            }

            .form-flex.justify-start { justify-content: flex-start; }
            .form-flex.justify-center { justify-content: center; }
            .form-flex.justify-end { justify-content: flex-end; }
            .form-flex.justify-between { justify-content: space-between; }
            .form-flex.justify-around { justify-content: space-around; }
            .form-flex.justify-evenly { justify-content: space-evenly; }

            .form-flex.align-start { align-items: flex-start; }
            .form-flex.align-center { align-items: center; }
            .form-flex.align-end { align-items: flex-end; }
            .form-flex.align-stretch { align-items: stretch; }

            .form-flex.direction-column { flex-direction: column; }
            .form-flex.direction-row-reverse { flex-direction: row-reverse; }
            .form-flex.direction-column-reverse { flex-direction: column-reverse; }

            .form-flex.wrap { flex-wrap: wrap; }
            .form-flex.nowrap { flex-wrap: nowrap; }

            .form-flex-item {
                flex: 1;
            }

            .form-flex-item.flex-none { flex: none; }
            .form-flex-item.flex-auto { flex: auto; }
            .form-flex-item.flex-grow { flex-grow: 1; }
            .form-flex-item.flex-shrink { flex-shrink: 1; }

            /* 间距 */
            .form-gap-0 { gap: 0; }
            .form-gap-1 { gap: 4px; }
            .form-gap-2 { gap: 8px; }
            .form-gap-3 { gap: 12px; }
            .form-gap-4 { gap: 16px; }
            .form-gap-5 { gap: 20px; }
            .form-gap-6 { gap: 24px; }
            .form-gap-8 { gap: 32px; }

            /* 卡片布局 */
            .form-card {
                background: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                transition: box-shadow 0.3s ease;
            }

            .form-card:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .form-card-header {
                padding: 16px 20px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .form-card-title {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin: 0;
            }

            .form-card-body {
                padding: 20px;
            }

            .form-card-footer {
                padding: 16px 20px;
                border-top: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            /* 面板布局 */
            .form-panel {
                display: flex;
                min-height: 400px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .form-panel-sidebar {
                width: 250px;
                background: #f9fafb;
                border-right: 1px solid #e5e7eb;
                padding: 20px;
                flex-shrink: 0;
            }

            .form-panel-main {
                flex: 1;
                background: white;
                padding: 20px;
            }

            .form-panel-aside {
                width: 200px;
                background: #f9fafb;
                border-left: 1px solid #e5e7eb;
                padding: 20px;
                flex-shrink: 0;
            }

            /* 头部布局 */
            .form-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                background: white;
                border-bottom: 1px solid #e5e7eb;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .form-header-logo {
                font-size: 20px;
                font-weight: 700;
                color: #374151;
            }

            .form-header-nav {
                display: flex;
                gap: 24px;
                align-items: center;
            }

            .form-header-nav a {
                color: #6b7280;
                text-decoration: none;
                font-weight: 500;
                transition: color 0.2s ease;
            }

            .form-header-nav a:hover,
            .form-header-nav a.active {
                color: #3b82f6;
            }

            .form-header-actions {
                display: flex;
                gap: 12px;
                align-items: center;
            }

            /* 底部布局 */
            .form-footer {
                background: #374151;
                color: white;
                padding: 40px 0 20px;
                margin-top: 40px;
            }

            .form-footer-content {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 32px;
                margin-bottom: 32px;
            }

            .form-footer-section h4 {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 16px;
                color: white;
            }

            .form-footer-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .form-footer-section li {
                margin-bottom: 8px;
            }

            .form-footer-section a {
                color: #d1d5db;
                text-decoration: none;
                transition: color 0.2s ease;
            }

            .form-footer-section a:hover {
                color: white;
            }

            .form-footer-bottom {
                border-top: 1px solid #4b5563;
                padding-top: 20px;
                text-align: center;
                color: #9ca3af;
                font-size: 14px;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .form-container {
                    padding: 0 12px;
                }

                .form-row {
                    margin: 0 -6px;
                }

                .form-col {
                    padding: 0 6px;
                }

                /* 移动端栅格 */
                .form-col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
                .form-col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
                .form-col-sm-3 { flex: 0 0 25%; max-width: 25%; }
                .form-col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
                .form-col-sm-6 { flex: 0 0 50%; max-width: 50%; }
                .form-col-sm-12 { flex: 0 0 100%; max-width: 100%; }

                .form-panel {
                    flex-direction: column;
                }

                .form-panel-sidebar,
                .form-panel-aside {
                    width: 100%;
                    border: none;
                    border-bottom: 1px solid #e5e7eb;
                }

                .form-header {
                    flex-direction: column;
                    gap: 16px;
                    text-align: center;
                }

                .form-header-nav {
                    flex-wrap: wrap;
                    justify-content: center;
                }
            }

            @media (max-width: 480px) {
                .form-container {
                    padding: 0 8px;
                }

                .form-row {
                    margin: 0 -4px;
                }

                .form-col {
                    padding: 0 4px;
                }

                .form-card-header,
                .form-card-body,
                .form-card-footer {
                    padding: 16px;
                }

                .form-panel-sidebar,
                .form-panel-main,
                .form-panel-aside {
                    padding: 16px;
                }
            }

            /* 演示样式 */
            .demo-box {
                background: #f3f4f6;
                border: 2px dashed #d1d5db;
                border-radius: 6px;
                padding: 20px;
                text-align: center;
                color: #6b7280;
                font-weight: 500;
                min-height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .demo-box.primary {
                background: #dbeafe;
                border-color: #3b82f6;
                color: #1e40af;
            }

            .demo-box.secondary {
                background: #f0fdf4;
                border-color: #10b981;
                color: #065f46;
            }

            .demo-box.accent {
                background: #fef3c7;
                border-color: #f59e0b;
                color: #92400e;
            }
        </style>

        <!-- 容器示例 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">容器布局</h3>
            <div class="form-container">
                <div class="demo-box">标准容器 (max-width: 1200px)</div>
            </div>
            <br>
            <div class="form-container narrow">
                <div class="demo-box">窄容器 (max-width: 800px)</div>
            </div>
            <br>
            <div class="form-container wide">
                <div class="demo-box">宽容器 (max-width: 1400px)</div>
            </div>
        </div>

        <!-- 栅格系统 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">栅格系统</h3>
            <div class="form-container">
                <div class="form-row">
                    <div class="form-col-12">
                        <div class="demo-box">12列</div>
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-col-6">
                        <div class="demo-box">6列</div>
                    </div>
                    <div class="form-col-6">
                        <div class="demo-box">6列</div>
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-col-4">
                        <div class="demo-box">4列</div>
                    </div>
                    <div class="form-col-4">
                        <div class="demo-box">4列</div>
                    </div>
                    <div class="form-col-4">
                        <div class="demo-box">4列</div>
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-col-3">
                        <div class="demo-box">3列</div>
                    </div>
                    <div class="form-col-6">
                        <div class="demo-box">6列</div>
                    </div>
                    <div class="form-col-3">
                        <div class="demo-box">3列</div>
                    </div>
                </div>
                <br>
                <div class="form-row">
                    <div class="form-col-8 form-col-offset-2">
                        <div class="demo-box">8列 + 偏移2列</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flex布局 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">Flex布局</h3>
            <div class="form-container">
                <div class="form-flex justify-between align-center form-gap-4" style="margin-bottom: 16px;">
                    <div class="demo-box primary">左侧</div>
                    <div class="demo-box secondary">中间</div>
                    <div class="demo-box accent">右侧</div>
                </div>
                
                <div class="form-flex justify-center align-center form-gap-4" style="margin-bottom: 16px;">
                    <div class="demo-box primary">居中1</div>
                    <div class="demo-box secondary">居中2</div>
                    <div class="demo-box accent">居中3</div>
                </div>
                
                <div class="form-flex direction-column form-gap-4">
                    <div class="demo-box primary">垂直1</div>
                    <div class="demo-box secondary">垂直2</div>
                    <div class="demo-box accent">垂直3</div>
                </div>
            </div>
        </div>

        <!-- 卡片布局 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">卡片布局</h3>
            <div class="form-container">
                <div class="form-row">
                    <div class="form-col-4">
                        <div class="form-card">
                            <div class="form-card-header">
                                <h4 class="form-card-title">卡片标题1</h4>
                            </div>
                            <div class="form-card-body">
                                <p>这是卡片的内容区域，可以放置任何内容。</p>
                            </div>
                            <div class="form-card-footer">
                                <button class="form-button form-button-primary" style="padding: 8px 16px; font-size: 14px;">操作</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-col-4">
                        <div class="form-card">
                            <div class="form-card-header">
                                <h4 class="form-card-title">卡片标题2</h4>
                            </div>
                            <div class="form-card-body">
                                <p>这是另一个卡片的内容，展示了卡片的灵活性。</p>
                            </div>
                            <div class="form-card-footer">
                                <button class="form-button form-button-secondary" style="padding: 8px 16px; font-size: 14px;">查看</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-col-4">
                        <div class="form-card">
                            <div class="form-card-header">
                                <h4 class="form-card-title">卡片标题3</h4>
                            </div>
                            <div class="form-card-body">
                                <p>第三个卡片，展示了统一的设计风格。</p>
                            </div>
                            <div class="form-card-footer">
                                <button class="form-button form-button-danger" style="padding: 8px 16px; font-size: 14px;">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 面板布局 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">面板布局</h3>
            <div class="form-container">
                <div class="form-panel">
                    <div class="form-panel-sidebar">
                        <h4>侧边栏</h4>
                        <ul style="list-style: none; padding: 0; margin: 16px 0 0 0;">
                            <li style="margin-bottom: 8px;"><a href="#" style="color: #6b7280; text-decoration: none;">菜单项1</a></li>
                            <li style="margin-bottom: 8px;"><a href="#" style="color: #6b7280; text-decoration: none;">菜单项2</a></li>
                            <li style="margin-bottom: 8px;"><a href="#" style="color: #6b7280; text-decoration: none;">菜单项3</a></li>
                            <li style="margin-bottom: 8px;"><a href="#" style="color: #6b7280; text-decoration: none;">菜单项4</a></li>
                        </ul>
                    </div>
                    <div class="form-panel-main">
                        <h4>主要内容区域</h4>
                        <p>这里是主要的内容区域，可以放置表单、表格、图表等各种内容。</p>
                        <p>面板布局适合用于管理后台、设置页面等需要侧边导航的场景。</p>
                    </div>
                    <div class="form-panel-aside">
                        <h4>辅助区域</h4>
                        <p>这里可以放置辅助信息、广告、相关链接等内容。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 头部布局 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">头部布局</h3>
            <div class="form-header">
                <div class="form-header-logo">Logo</div>
                <nav class="form-header-nav">
                    <a href="#" class="active">首页</a>
                    <a href="#">产品</a>
                    <a href="#">服务</a>
                    <a href="#">关于</a>
                </nav>
                <div class="form-header-actions">
                    <button class="form-button form-button-secondary" style="padding: 8px 16px; font-size: 14px;">登录</button>
                    <button class="form-button form-button-primary" style="padding: 8px 16px; font-size: 14px;">注册</button>
                </div>
            </div>
        </div>

        <!-- 底部布局 -->
        <div class="form-layout-section">
            <h3 class="form-layout-title">底部布局</h3>
            <div class="form-footer">
                <div class="form-container">
                    <div class="form-footer-content">
                        <div class="form-footer-section">
                            <h4>产品</h4>
                            <ul>
                                <li><a href="#">功能特性</a></li>
                                <li><a href="#">价格方案</a></li>
                                <li><a href="#">使用案例</a></li>
                            </ul>
                        </div>
                        <div class="form-footer-section">
                            <h4>支持</h4>
                            <ul>
                                <li><a href="#">帮助中心</a></li>
                                <li><a href="#">联系我们</a></li>
                                <li><a href="#">技术支持</a></li>
                            </ul>
                        </div>
                        <div class="form-footer-section">
                            <h4>公司</h4>
                            <ul>
                                <li><a href="#">关于我们</a></li>
                                <li><a href="#">新闻动态</a></li>
                                <li><a href="#">招聘信息</a></li>
                            </ul>
                        </div>
                        <div class="form-footer-section">
                            <h4>法律</h4>
                            <ul>
                                <li><a href="#">隐私政策</a></li>
                                <li><a href="#">服务条款</a></li>
                                <li><a href="#">Cookie政策</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-footer-bottom">
                        <p>&copy; 2024 公司名称. 保留所有权利.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 容器 --&gt;
&lt;div class="form-container"&gt;
    &lt;!-- 内容 --&gt;
&lt;/div&gt;

&lt;!-- 栅格系统 --&gt;
&lt;div class="form-row"&gt;
    &lt;div class="form-col-6"&gt;左侧&lt;/div&gt;
    &lt;div class="form-col-6"&gt;右侧&lt;/div&gt;
&lt;/div&gt;

&lt;!-- Flex布局 --&gt;
&lt;div class="form-flex justify-between align-center form-gap-4"&gt;
    &lt;div&gt;项目1&lt;/div&gt;
    &lt;div&gt;项目2&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 卡片 --&gt;
&lt;div class="form-card"&gt;
    &lt;div class="form-card-header"&gt;
        &lt;h4 class="form-card-title"&gt;标题&lt;/h4&gt;
    &lt;/div&gt;
    &lt;div class="form-card-body"&gt;
        内容
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
