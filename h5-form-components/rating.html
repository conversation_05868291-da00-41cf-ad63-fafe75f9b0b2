<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级评分组件</title>
</head>
<body>
    <!-- 星级评分组件 -->
    <div class="form-rating">
        <style>
            .form-rating {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-rating-group {
                margin-bottom: 20px;
            }

            .form-rating-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 8px;
            }

            .form-rating-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-rating-stars {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 8px;
            }

            .form-rating-star {
                position: relative;
                width: 32px;
                height: 32px;
                cursor: pointer;
                transition: transform 0.2s ease;
            }

            .form-rating-star:hover {
                transform: scale(1.1);
            }

            .form-rating-star input[type="radio"] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .form-rating-star-icon {
                width: 100%;
                height: 100%;
                fill: #d1d5db;
                transition: fill 0.2s ease;
            }

            .form-rating-star input[type="radio"]:checked ~ .form-rating-star-icon,
            .form-rating-star.active .form-rating-star-icon {
                fill: #fbbf24;
            }

            .form-rating-star.hover .form-rating-star-icon {
                fill: #fde047;
            }

            .form-rating-star input[type="radio"]:focus ~ .form-rating-star-icon {
                filter: drop-shadow(0 0 4px rgba(251, 191, 36, 0.5));
            }

            .form-rating-text {
                display: flex;
                align-items: center;
                gap: 12px;
                min-height: 24px;
            }

            .form-rating-current {
                font-size: 16px;
                font-weight: 500;
                color: #374151;
            }

            .form-rating-description {
                font-size: 14px;
                color: #6b7280;
            }

            .form-rating-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-rating-message.error {
                color: #ef4444;
            }

            .form-rating-message.success {
                color: #10b981;
            }

            .form-rating-message.help {
                color: #6b7280;
            }

            /* 大尺寸星星 */
            .form-rating-large .form-rating-star {
                width: 40px;
                height: 40px;
            }

            /* 小尺寸星星 */
            .form-rating-small .form-rating-star {
                width: 24px;
                height: 24px;
            }

            /* 不同颜色主题 */
            .form-rating-red .form-rating-star input[type="radio"]:checked ~ .form-rating-star-icon,
            .form-rating-red .form-rating-star.active .form-rating-star-icon {
                fill: #ef4444;
            }

            .form-rating-red .form-rating-star.hover .form-rating-star-icon {
                fill: #f87171;
            }

            .form-rating-blue .form-rating-star input[type="radio"]:checked ~ .form-rating-star-icon,
            .form-rating-blue .form-rating-star.active .form-rating-star-icon {
                fill: #3b82f6;
            }

            .form-rating-blue .form-rating-star.hover .form-rating-star-icon {
                fill: #60a5fa;
            }

            /* 只读状态 */
            .form-rating-readonly .form-rating-star {
                cursor: default;
                pointer-events: none;
            }

            .form-rating-readonly .form-rating-star:hover {
                transform: none;
            }

            /* 错误状态 */
            .form-rating.error .form-rating-stars {
                filter: drop-shadow(0 0 4px rgba(239, 68, 68, 0.3));
            }

            /* 成功状态 */
            .form-rating.success .form-rating-stars {
                filter: drop-shadow(0 0 4px rgba(16, 185, 129, 0.3));
            }

            @media (max-width: 480px) {
                .form-rating {
                    margin: 12px 0;
                }

                .form-rating-star {
                    width: 36px;
                    height: 36px;
                }

                .form-rating-large .form-rating-star {
                    width: 44px;
                    height: 44px;
                }

                .form-rating-small .form-rating-star {
                    width: 28px;
                    height: 28px;
                }

                .form-rating-text {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;
                }
            }
        </style>

        <!-- 基础星级评分 -->
        <div class="form-rating-group">
            <label class="form-rating-label">
                产品评分 <span class="form-rating-required">*</span>
            </label>
            <div class="form-rating-stars" data-rating="product-rating">
                <label class="form-rating-star">
                    <input type="radio" name="product-rating" value="1" required>
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="product-rating" value="2">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="product-rating" value="3">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="product-rating" value="4">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="product-rating" value="5">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
            </div>
            <div class="form-rating-text">
                <span class="form-rating-current" id="product-rating-text">请选择评分</span>
                <span class="form-rating-description" id="product-rating-desc"></span>
            </div>
            <div class="form-rating-message help">请为产品打分，1星最低，5星最高</div>
        </div>

        <!-- 大尺寸评分 -->
        <div class="form-rating-group">
            <label class="form-rating-label">
                服务评分
            </label>
            <div class="form-rating-stars form-rating-large" data-rating="service-rating">
                <label class="form-rating-star">
                    <input type="radio" name="service-rating" value="1">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="service-rating" value="2">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="service-rating" value="3">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="service-rating" value="4">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="service-rating" value="5">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
            </div>
            <div class="form-rating-text">
                <span class="form-rating-current" id="service-rating-text">未评分</span>
            </div>
        </div>

        <!-- 红色主题评分 -->
        <div class="form-rating-group">
            <label class="form-rating-label">
                满意度评分
            </label>
            <div class="form-rating-stars form-rating-red" data-rating="satisfaction-rating">
                <label class="form-rating-star">
                    <input type="radio" name="satisfaction-rating" value="1">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="satisfaction-rating" value="2">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="satisfaction-rating" value="3" checked>
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="satisfaction-rating" value="4">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="satisfaction-rating" value="5">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
            </div>
            <div class="form-rating-text">
                <span class="form-rating-current" id="satisfaction-rating-text">3 星</span>
                <span class="form-rating-description" id="satisfaction-rating-desc">一般</span>
            </div>
        </div>

        <!-- 只读评分显示 -->
        <div class="form-rating-group">
            <label class="form-rating-label">
                历史评分（只读）
            </label>
            <div class="form-rating-stars form-rating-readonly form-rating-blue" data-rating="readonly-rating">
                <label class="form-rating-star active">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star active">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star active">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star active">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
            </div>
            <div class="form-rating-text">
                <span class="form-rating-current">4 星</span>
                <span class="form-rating-description">很好</span>
            </div>
            <div class="form-rating-message help">这是您上次的评分记录</div>
        </div>

        <!-- 错误状态示例 -->
        <div class="form-rating-group">
            <label class="form-rating-label">
                必填评分 <span class="form-rating-required">*</span>
            </label>
            <div class="form-rating-stars error" data-rating="required-rating">
                <label class="form-rating-star">
                    <input type="radio" name="required-rating" value="1" required>
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="required-rating" value="2">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="required-rating" value="3">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="required-rating" value="4">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
                <label class="form-rating-star">
                    <input type="radio" name="required-rating" value="5">
                    <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </label>
            </div>
            <div class="form-rating-text">
                <span class="form-rating-current" id="required-rating-text">请选择评分</span>
            </div>
            <div class="form-rating-message error">请选择一个评分</div>
        </div>

        <script>
            // 评分描述文本
            const ratingDescriptions = {
                1: "很差",
                2: "较差", 
                3: "一般",
                4: "很好",
                5: "非常好"
            };

            // 初始化评分组件
            function initRating() {
                const ratingGroups = document.querySelectorAll('[data-rating]');
                
                ratingGroups.forEach(group => {
                    const stars = group.querySelectorAll('.form-rating-star');
                    const ratingName = group.getAttribute('data-rating');
                    const textElement = document.getElementById(ratingName + '-text');
                    const descElement = document.getElementById(ratingName + '-desc');
                    
                    // 跳过只读评分
                    if (group.classList.contains('form-rating-readonly')) {
                        return;
                    }
                    
                    stars.forEach((star, index) => {
                        const input = star.querySelector('input[type="radio"]');
                        const rating = index + 1;
                        
                        // 点击事件
                        star.addEventListener('click', () => {
                            if (input) {
                                input.checked = true;
                                updateRatingDisplay(group, rating, textElement, descElement);
                                clearError(group);
                            }
                        });
                        
                        // 悬停事件
                        star.addEventListener('mouseenter', () => {
                            highlightStars(stars, rating);
                        });
                        
                        // 键盘事件
                        if (input) {
                            input.addEventListener('change', () => {
                                updateRatingDisplay(group, rating, textElement, descElement);
                                clearError(group);
                            });
                        }
                    });
                    
                    // 鼠标离开时恢复选中状态
                    group.addEventListener('mouseleave', () => {
                        const checkedInput = group.querySelector('input[type="radio"]:checked');
                        if (checkedInput) {
                            const checkedRating = parseInt(checkedInput.value);
                            highlightStars(stars, checkedRating);
                        } else {
                            clearHighlight(stars);
                        }
                    });
                    
                    // 初始化显示
                    const checkedInput = group.querySelector('input[type="radio"]:checked');
                    if (checkedInput) {
                        const initialRating = parseInt(checkedInput.value);
                        updateRatingDisplay(group, initialRating, textElement, descElement);
                        highlightStars(stars, initialRating);
                    }
                });
            }
            
            // 高亮星星
            function highlightStars(stars, rating) {
                stars.forEach((star, index) => {
                    star.classList.remove('hover', 'active');
                    if (index < rating) {
                        star.classList.add('hover');
                    }
                });
            }
            
            // 清除高亮
            function clearHighlight(stars) {
                stars.forEach(star => {
                    star.classList.remove('hover');
                });
            }
            
            // 更新评分显示
            function updateRatingDisplay(group, rating, textElement, descElement) {
                if (textElement) {
                    textElement.textContent = `${rating} 星`;
                }
                if (descElement) {
                    descElement.textContent = ratingDescriptions[rating] || '';
                }
                
                // 更新星星状态
                const stars = group.querySelectorAll('.form-rating-star');
                stars.forEach((star, index) => {
                    star.classList.remove('active');
                    if (index < rating) {
                        star.classList.add('active');
                    }
                });
            }
            
            // 清除错误状态
            function clearError(group) {
                group.classList.remove('error');
                const errorMessage = group.parentElement.querySelector('.form-rating-message.error');
                if (errorMessage) {
                    errorMessage.classList.remove('error');
                    errorMessage.classList.add('help');
                    errorMessage.textContent = '';
                }
            }
            
            // 验证评分
            function validateRating(ratingName) {
                const group = document.querySelector(`[data-rating="${ratingName}"]`);
                const input = group.querySelector('input[type="radio"]:checked');
                
                if (!input) {
                    group.classList.add('error');
                    const message = group.parentElement.querySelector('.form-rating-message');
                    if (message) {
                        message.classList.add('error');
                        message.textContent = '请选择一个评分';
                    }
                    return false;
                }
                return true;
            }
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', initRating);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础星级评分 --&gt;
&lt;div class="form-rating-group"&gt;
    &lt;label class="form-rating-label"&gt;
        产品评分 &lt;span class="form-rating-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-rating-stars" data-rating="product-rating"&gt;
        &lt;label class="form-rating-star"&gt;
            &lt;input type="radio" name="product-rating" value="1" required&gt;
            &lt;svg class="form-rating-star-icon" viewBox="0 0 24 24"&gt;
                &lt;path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/&gt;
            &lt;/svg&gt;
        &lt;/label&gt;
        &lt;!-- 重复4个星星... --&gt;
    &lt;/div&gt;
    &lt;div class="form-rating-text"&gt;
        &lt;span class="form-rating-current" id="product-rating-text"&gt;请选择评分&lt;/span&gt;
        &lt;span class="form-rating-description" id="product-rating-desc"&gt;&lt;/span&gt;
    &lt;/div&gt;
    &lt;div class="form-rating-message help"&gt;请为产品打分&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 大尺寸评分 --&gt;
&lt;div class="form-rating-stars form-rating-large" data-rating="service-rating"&gt;
    &lt;!-- 星星内容... --&gt;
&lt;/div&gt;

&lt;!-- 小尺寸评分 --&gt;
&lt;div class="form-rating-stars form-rating-small" data-rating="compact-rating"&gt;
    &lt;!-- 星星内容... --&gt;
&lt;/div&gt;

&lt;!-- 红色主题 --&gt;
&lt;div class="form-rating-stars form-rating-red" data-rating="red-rating"&gt;
    &lt;!-- 星星内容... --&gt;
&lt;/div&gt;

&lt;!-- 蓝色主题 --&gt;
&lt;div class="form-rating-stars form-rating-blue" data-rating="blue-rating"&gt;
    &lt;!-- 星星内容... --&gt;
&lt;/div&gt;

&lt;!-- 只读评分 --&gt;
&lt;div class="form-rating-stars form-rating-readonly" data-rating="readonly-rating"&gt;
    &lt;label class="form-rating-star active"&gt;
        &lt;svg class="form-rating-star-icon" viewBox="0 0 24 24"&gt;
            &lt;path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/&gt;
        &lt;/svg&gt;
    &lt;/label&gt;
    &lt;!-- 更多星星... --&gt;
&lt;/div&gt;

&lt;!-- 错误状态 --&gt;
&lt;div class="form-rating-stars error" data-rating="error-rating"&gt;
    &lt;!-- 星星内容... --&gt;
&lt;/div&gt;
&lt;div class="form-rating-message error"&gt;请选择一个评分&lt;/div&gt;</code></pre>
    </div>
</body>
</html>