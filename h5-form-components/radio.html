<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单选按钮组件</title>
</head>
<body>
    <!-- 单选按钮组件 -->
    <div class="form-radio">
        <style>
            .form-radio {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-radio input[type="radio"] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .form-radio-item {
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                cursor: pointer;
                min-height: 44px;
                padding: 8px 0;
            }

            .form-radio-custom {
                position: relative;
                width: 20px;
                height: 20px;
                border: 2px solid #d1d5db;
                border-radius: 50%;
                background-color: #ffffff;
                margin-right: 12px;
                transition: all 0.2s ease;
                flex-shrink: 0;
            }

            .form-radio-custom::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: #3b82f6;
                transform: translate(-50%, -50%) scale(0);
                transition: transform 0.2s ease;
            }

            .form-radio input[type="radio"]:checked + .form-radio-custom {
                border-color: #3b82f6;
            }

            .form-radio input[type="radio"]:checked + .form-radio-custom::after {
                transform: translate(-50%, -50%) scale(1);
            }

            .form-radio input[type="radio"]:focus + .form-radio-custom {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                border-color: #3b82f6;
            }

            .form-radio-item:hover .form-radio-custom {
                border-color: #3b82f6;
            }

            .form-radio-label {
                font-size: 16px;
                color: #374151;
                line-height: 1.5;
                user-select: none;
            }

            .form-radio-title {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 8px;
            }

            /* 横排样式 */
            .form-radio-horizontal {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }

            .form-radio-horizontal .form-radio-item {
                margin-bottom: 0;
                margin-right: 0;
                flex: 0 0 auto;
            }

            /* 紧凑横排样式 */
            .form-radio-horizontal.form-radio-compact {
                gap: 12px;
            }

            .form-radio-horizontal.form-radio-compact .form-radio-item {
                min-height: 36px;
                padding: 6px 0;
            }

            .form-radio-horizontal.form-radio-compact .form-radio-custom {
                width: 18px;
                height: 18px;
                margin-right: 8px;
            }

            .form-radio-horizontal.form-radio-compact .form-radio-label {
                font-size: 14px;
            }

            /* 等宽横排样式 */
            .form-radio-horizontal.form-radio-equal {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
                gap: 16px;
            }

            /* 两列横排 */
            .form-radio-horizontal.form-radio-two-columns {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
            }

            /* 三列横排 */
            .form-radio-horizontal.form-radio-three-columns {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 16px;
            }

            /* 按钮样式横排 */
            .form-radio-button-style .form-radio-item {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px 16px;
                margin-bottom: 8px;
                background-color: #ffffff;
                transition: all 0.2s ease;
                min-height: auto;
                text-align: center;
            }

            .form-radio-button-style .form-radio-custom {
                display: none;
            }

            .form-radio-button-style .form-radio-item:hover {
                border-color: #3b82f6;
                background-color: #f8faff;
            }

            .form-radio-button-style .form-radio-item:has(input:checked) {
                border-color: #3b82f6;
                background-color: #3b82f6;
                color: white;
            }

            .form-radio-button-style .form-radio-item:has(input:checked) .form-radio-label {
                color: white;
            }

            .form-radio-button-style .form-radio-item:has(input:focus) {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            /* 卡片样式横排 */
            .form-radio-card-style .form-radio-item {
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 8px;
                background-color: #ffffff;
                transition: all 0.2s ease;
                min-height: auto;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                flex-direction: column;
                text-align: center;
                align-items: center;
            }

            .form-radio-card-style .form-radio-custom {
                margin-right: 0;
                margin-bottom: 8px;
            }

            .form-radio-card-style .form-radio-item:hover {
                border-color: #3b82f6;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                transform: translateY(-2px);
            }

            .form-radio-card-style .form-radio-item:has(input:checked) {
                border-color: #3b82f6;
                background-color: #f0f9ff;
            }

            .form-radio-card-style .form-radio-item:has(input:focus) {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            @media (max-width: 480px) {
                .form-radio {
                    margin: 12px 0;
                }
                
                .form-radio-item {
                    min-height: 44px;
                    padding: 10px 0;
                }
                
                .form-radio-custom {
                    width: 22px;
                    height: 22px;
                    margin-right: 14px;
                }
                
                .form-radio-label {
                    font-size: 16px;
                }

                /* 移动端横排适配 */
                .form-radio-horizontal {
                    gap: 12px;
                }

                .form-radio-horizontal.form-radio-two-columns,
                .form-radio-horizontal.form-radio-three-columns {
                    grid-template-columns: 1fr;
                    gap: 12px;
                }

                .form-radio-horizontal.form-radio-equal {
                    grid-template-columns: 1fr;
                }

                .form-radio-button-style .form-radio-item,
                .form-radio-card-style .form-radio-item {
                    padding: 14px 16px;
                }
            }
        </style>

        <div class="form-radio-title">选择您的性别</div>
        
        <label class="form-radio-item">
            <input type="radio" name="gender" value="male">
            <span class="form-radio-custom"></span>
            <span class="form-radio-label">男性</span>
        </label>
        
        <label class="form-radio-item">
            <input type="radio" name="gender" value="female">
            <span class="form-radio-custom"></span>
            <span class="form-radio-label">女性</span>
        </label>
        
        <label class="form-radio-item">
            <input type="radio" name="gender" value="other">
            <span class="form-radio-custom"></span>
            <span class="form-radio-label">其他</span>
        </label>
    </div>

    <!-- 横排单选按钮 -->
    <div class="form-radio">
        <div class="form-radio-title">选择您的年龄段（横排显示）</div>
        <div class="form-radio-horizontal">
            <label class="form-radio-item">
                <input type="radio" name="age" value="18-25">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">18-25岁</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="age" value="26-35">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">26-35岁</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="age" value="36-45">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">36-45岁</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="age" value="46+">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">46岁以上</span>
            </label>
        </div>
    </div>

    <!-- 紧凑横排单选按钮 -->
    <div class="form-radio">
        <div class="form-radio-title">选择优先级（紧凑横排）</div>
        <div class="form-radio-horizontal form-radio-compact">
            <label class="form-radio-item">
                <input type="radio" name="priority" value="low">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">低</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="priority" value="medium" checked>
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">中</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="priority" value="high">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">高</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="priority" value="urgent">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">紧急</span>
            </label>
        </div>
    </div>

    <!-- 按钮样式横排单选 -->
    <div class="form-radio">
        <div class="form-radio-title">选择订阅计划（按钮样式）</div>
        <div class="form-radio-horizontal form-radio-button-style">
            <label class="form-radio-item">
                <input type="radio" name="plan" value="basic">
                <span class="form-radio-label">基础版</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="plan" value="pro" checked>
                <span class="form-radio-label">专业版</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="plan" value="enterprise">
                <span class="form-radio-label">企业版</span>
            </label>
        </div>
    </div>

    <!-- 卡片样式横排单选 -->
    <div class="form-radio">
        <div class="form-radio-title">选择支付方式（卡片样式）</div>
        <div class="form-radio-horizontal form-radio-card-style form-radio-three-columns">
            <label class="form-radio-item">
                <input type="radio" name="payment" value="alipay">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">支付宝</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="payment" value="wechat">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">微信支付</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="payment" value="bank">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">银行卡</span>
            </label>
        </div>
    </div>

    <!-- 等宽横排单选 -->
    <div class="form-radio">
        <div class="form-radio-title">选择主题色彩（等宽布局）</div>
        <div class="form-radio-horizontal form-radio-equal">
            <label class="form-radio-item">
                <input type="radio" name="theme" value="blue">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">蓝色主题</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="theme" value="green">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">绿色主题</span>
            </label>
            
            <label class="form-radio-item">
                <input type="radio" name="theme" value="purple">
                <span class="form-radio-custom"></span>
                <span class="form-radio-label">紫色主题</span>
            </label>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-radio"&gt;
    &lt;div class="form-radio-title"&gt;选择您的性别&lt;/div&gt;
    
    &lt;label class="form-radio-item"&gt;
        &lt;input type="radio" name="gender" value="male"&gt;
        &lt;span class="form-radio-custom"&gt;&lt;/span&gt;
        &lt;span class="form-radio-label"&gt;男性&lt;/span&gt;
    &lt;/label&gt;
    
    &lt;!-- 更多选项... --&gt;
&lt;/div&gt;

&lt;!-- 横排单选按钮 --&gt;
&lt;div class="form-radio-horizontal"&gt;
    &lt;label class="form-radio-item"&gt;
        &lt;input type="radio" name="age" value="18-25"&gt;
        &lt;span class="form-radio-custom"&gt;&lt;/span&gt;
        &lt;span class="form-radio-label"&gt;18-25岁&lt;/span&gt;
    &lt;/label&gt;
    &lt;label class="form-radio-item"&gt;
        &lt;input type="radio" name="age" value="26-35"&gt;
        &lt;span class="form-radio-custom"&gt;&lt;/span&gt;
        &lt;span class="form-radio-label"&gt;26-35岁&lt;/span&gt;
    &lt;/label&gt;
&lt;/div&gt;

&lt;!-- 紧凑横排 --&gt;
&lt;div class="form-radio-horizontal form-radio-compact"&gt;
    &lt;!-- 单选项目... --&gt;
&lt;/div&gt;

&lt;!-- 两列布局 --&gt;
&lt;div class="form-radio-horizontal form-radio-two-columns"&gt;
    &lt;!-- 单选项目... --&gt;
&lt;/div&gt;

&lt;!-- 三列布局 --&gt;
&lt;div class="form-radio-horizontal form-radio-three-columns"&gt;
    &lt;!-- 单选项目... --&gt;
&lt;/div&gt;

&lt;!-- 按钮样式 --&gt;
&lt;div class="form-radio-horizontal form-radio-button-style"&gt;
    &lt;label class="form-radio-item"&gt;
        &lt;input type="radio" name="plan" value="basic"&gt;
        &lt;span class="form-radio-label"&gt;基础版&lt;/span&gt;
    &lt;/label&gt;
&lt;/div&gt;

&lt;!-- 卡片样式 --&gt;
&lt;div class="form-radio-horizontal form-radio-card-style"&gt;
    &lt;label class="form-radio-item"&gt;
        &lt;input type="radio" name="payment" value="alipay"&gt;
        &lt;span class="form-radio-custom"&gt;&lt;/span&gt;
        &lt;span class="form-radio-label"&gt;支付宝&lt;/span&gt;
    &lt;/label&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>