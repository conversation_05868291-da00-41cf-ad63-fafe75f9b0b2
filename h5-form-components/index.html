<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5表单组件库 - 综合示例</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #374151;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .form-demo {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .form-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 3px solid #e5e7eb;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .form-section-title {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 16px;
            padding-left: 12px;
            border-left: 4px solid #3b82f6;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .component-info {
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 30px;
        }

        .component-info h3 {
            color: #0369a1;
            margin-bottom: 8px;
        }

        .component-info p {
            color: #0f172a;
            font-size: 14px;
        }

        .demo-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 2px solid #e5e7eb;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }

            .header {
                padding: 24px 16px;
                margin-bottom: 24px;
            }

            .header h1 {
                font-size: 24px;
            }

            .header p {
                font-size: 16px;
            }

            .form-demo {
                padding: 20px;
                border-radius: 12px;
            }

            .form-title {
                font-size: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .demo-actions {
                flex-direction: column;
                gap: 12px;
            }
        }

        /* 引入所有组件样式 */
        
        /* 复选框组件样式 */
        .form-checkbox {
            margin: 16px 0;
        }

        .form-checkbox input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .form-checkbox-item {
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            cursor: pointer;
            min-height: 44px;
            padding: 8px 0;
        }

        .form-checkbox-custom {
            position: relative;
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            background-color: #ffffff;
            margin-right: 12px;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .form-checkbox-custom::after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid #ffffff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }

        .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom::after {
            opacity: 1;
        }

        .form-checkbox input[type="checkbox"]:focus + .form-checkbox-custom {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        .form-checkbox-item:hover .form-checkbox-custom {
            border-color: #3b82f6;
        }

        .form-checkbox-label {
            font-size: 16px;
            color: #374151;
            line-height: 1.5;
            user-select: none;
        }

        .form-checkbox-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        /* 复选框横排样式 */
        .form-checkbox-horizontal {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }

        .form-checkbox-horizontal .form-checkbox-item {
            margin-bottom: 0;
            margin-right: 0;
            flex: 0 0 auto;
        }

        .form-checkbox-horizontal.form-checkbox-two-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        /* 单选按钮组件样式 */
        .form-radio {
            margin: 16px 0;
        }

        .form-radio input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .form-radio-item {
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            cursor: pointer;
            min-height: 44px;
            padding: 8px 0;
        }

        .form-radio-custom {
            position: relative;
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            background-color: #ffffff;
            margin-right: 12px;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .form-radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #3b82f6;
            transform: translate(-50%, -50%) scale(0);
            transition: transform 0.2s ease;
        }

        .form-radio input[type="radio"]:checked + .form-radio-custom {
            border-color: #3b82f6;
        }

        .form-radio input[type="radio"]:checked + .form-radio-custom::after {
            transform: translate(-50%, -50%) scale(1);
        }

        .form-radio input[type="radio"]:focus + .form-radio-custom {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        .form-radio-item:hover .form-radio-custom {
            border-color: #3b82f6;
        }

        .form-radio-label {
            font-size: 16px;
            color: #374151;
            line-height: 1.5;
            user-select: none;
        }

        .form-radio-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        /* 单选按钮横排样式 */
        .form-radio-horizontal {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }

        .form-radio-horizontal .form-radio-item {
            margin-bottom: 0;
            margin-right: 0;
            flex: 0 0 auto;
        }

        /* 按钮组件样式 */
        .form-buttons {
            margin: 16px 0;
        }

        .form-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 44px;
            margin-right: 8px;
            margin-bottom: 8px;
            outline: none;
            user-select: none;
        }

        .form-button:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .form-button-primary {
            background-color: #3b82f6;
            color: #ffffff;
        }

        .form-button-primary:hover:not(:disabled) {
            background-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .form-button-secondary {
            background-color: #ffffff;
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .form-button-secondary:hover:not(:disabled) {
            background-color: #f9fafb;
            border-color: #9ca3af;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .form-button-danger {
            background-color: #ef4444;
            color: #ffffff;
        }

        .form-button-danger:hover:not(:disabled) {
            background-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .form-button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        /* 文本输入框组件样式 */
        .form-text-input {
            margin: 16px 0;
        }

        .form-input-group {
            margin-bottom: 20px;
        }

        .form-input-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input-required {
            color: #ef4444;
            margin-left: 2px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            color: #374151;
            background-color: #ffffff;
            transition: all 0.2s ease;
            outline: none;
            min-height: 44px;
            box-sizing: border-box;
        }

        .form-input::placeholder {
            color: #9ca3af;
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input:hover:not(:focus) {
            border-color: #9ca3af;
        }

        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-input-message {
            margin-top: 6px;
            font-size: 14px;
            line-height: 1.4;
        }

        .form-input-message.error {
            color: #ef4444;
        }

        .form-input-message.success {
            color: #10b981;
        }

        .form-input-message.help {
            color: #6b7280;
        }

        /* 下拉选择框组件样式 */
        .form-select {
            margin: 16px 0;
        }

        .form-select-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-select-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-select-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .form-select-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            color: #374151;
            background-color: #ffffff;
            transition: all 0.2s ease;
            outline: none;
            min-height: 44px;
            box-sizing: border-box;
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }

        .form-select-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #6b7280;
            pointer-events: none;
            transition: transform 0.2s ease;
        }

        .form-select-input:focus + .form-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .form-select-message {
            margin-top: 6px;
            font-size: 14px;
            line-height: 1.4;
        }

        /* 多行文本框组件样式 */
        .form-textarea {
            margin: 16px 0;
        }

        .form-textarea-group {
            margin-bottom: 20px;
        }

        .form-textarea-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-textarea-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            color: #374151;
            background-color: #ffffff;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
            font-family: inherit;
        }

        .form-textarea-input::placeholder {
            color: #9ca3af;
        }

        .form-textarea-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 日期时间组件样式 */
        .form-datetime {
            margin: 16px 0;
        }

        .form-datetime-group {
            margin-bottom: 20px;
        }

        .form-datetime-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-datetime-required {
            color: #ef4444;
            margin-left: 2px;
        }

        .form-datetime-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .form-datetime-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            color: #374151;
            background-color: #ffffff;
            transition: all 0.2s ease;
            outline: none;
            min-height: 44px;
            box-sizing: border-box;
            cursor: pointer;
        }

        .form-datetime-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-datetime-input:hover:not(:focus) {
            border-color: #9ca3af;
        }

        .form-datetime-input[type="date"]::-webkit-calendar-picker-indicator,
        .form-datetime-input[type="time"]::-webkit-calendar-picker-indicator,
        .form-datetime-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
            background-size: 16px 16px;
            width: 16px;
            height: 16px;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .form-datetime-input[type="time"]::-webkit-calendar-picker-indicator {
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg>') no-repeat center;
        }

        .form-datetime-input:hover::-webkit-calendar-picker-indicator {
            opacity: 1;
        }

        .form-datetime-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-datetime-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-datetime-message {
            margin-top: 6px;
            font-size: 14px;
            line-height: 1.4;
        }

        .form-datetime-message.error {
            color: #ef4444;
        }

        .form-datetime-message.success {
            color: #10b981;
        }

        .form-datetime-message.help {
            color: #6b7280;
        }

        /* 星级评分组件样式 */
        .form-rating {
            margin: 16px 0;
        }

        .form-rating-group {
            margin-bottom: 20px;
        }

        .form-rating-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-rating-stars {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: 8px;
        }

        .form-rating-star {
            position: relative;
            width: 32px;
            height: 32px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .form-rating-star:hover {
            transform: scale(1.1);
        }

        .form-rating-star input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .form-rating-star-icon {
            width: 100%;
            height: 100%;
            fill: #d1d5db;
            transition: fill 0.2s ease;
        }

        .form-rating-star input[type="radio"]:checked ~ .form-rating-star-icon,
        .form-rating-star.active .form-rating-star-icon {
            fill: #fbbf24;
        }

        .form-rating-star.hover .form-rating-star-icon {
            fill: #fde047;
        }

        .form-rating-text {
            display: flex;
            align-items: center;
            gap: 12px;
            min-height: 24px;
        }

        .form-rating-current {
            font-size: 16px;
            font-weight: 500;
            color: #374151;
        }

        /* 标签输入组件样式 */
        .form-tag-input {
            margin: 16px 0;
        }

        .form-tag-group {
            margin-bottom: 20px;
        }

        .form-tag-label {
            display: block;
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-tag-container {
            position: relative;
            min-height: 44px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            background-color: #ffffff;
            padding: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-items: center;
            transition: all 0.2s ease;
            cursor: text;
        }

        .form-tag-container:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-tag-item {
            display: inline-flex;
            align-items: center;
            background-color: #f3f4f6;
            color: #374151;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.2;
            max-width: 200px;
            user-select: none;
            transition: all 0.2s ease;
        }

        .form-tag-blue .form-tag-item {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .form-tag-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .form-tag-remove {
            margin-left: 6px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #9ca3af;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            line-height: 1;
            transition: background-color 0.2s ease;
            flex-shrink: 0;
        }

        .form-tag-input-field {
            border: none;
            outline: none;
            background: transparent;
            font-size: 14px;
            color: #374151;
            flex: 1;
            min-width: 120px;
            padding: 4px 0;
        }

        .form-tag-presets {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .form-tag-preset {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: #ffffff;
            color: #374151;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .form-tag-preset:hover {
            border-color: #3b82f6;
            background-color: #f0f9ff;
            color: #3b82f6;
        }

        /* 进度条组件样式 */
        .form-progress {
            margin: 16px 0;
        }

        .form-progress-group {
            margin-bottom: 24px;
        }

        .form-progress-container {
            position: relative;
            width: 100%;
            height: 8px;
            background-color: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }

        .form-progress-bar {
            height: 100%;
            background-color: #3b82f6;
            border-radius: 4px;
            transition: width 0.3s ease;
            position: relative;
        }

        .form-progress-text {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 14px;
        }

        .form-progress-percentage {
            font-weight: 500;
            color: #374151;
        }

        .form-progress-status {
            color: #6b7280;
        }

        .form-progress-steps {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .form-progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .form-progress-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 16px;
            left: 50%;
            right: -50%;
            height: 2px;
            background-color: #e5e7eb;
            z-index: 1;
        }

        .form-progress-step.completed:not(:last-child)::after {
            background-color: #10b981;
        }

        .form-progress-step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            position: relative;
            z-index: 2;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .form-progress-step.completed .form-progress-step-circle {
            background-color: #10b981;
            color: white;
        }

        .form-progress-step.active .form-progress-step-circle {
            background-color: #3b82f6;
            color: white;
            transform: scale(1.1);
        }

        .form-progress-step-title {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
            line-height: 1.3;
        }

        .form-progress-step.completed .form-progress-step-title,
        .form-progress-step.active .form-progress-step-title {
            color: #374151;
            font-weight: 500;
        }

        /* 弹窗组件样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: all 0.3s ease;
            position: relative;
        }

        .modal-overlay.show .modal-container {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: 24px 24px 0 24px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            line-height: 1.4;
            flex: 1;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            transition: all 0.2s ease;
            flex-shrink: 0;
            margin-left: 16px;
        }

        .modal-close:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 16px 24px 24px 24px;
            overflow-y: auto;
            max-height: calc(90vh - 140px);
        }

        .modal-message {
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
            margin: 0;
        }

        .modal-footer {
            padding: 0 24px 24px 24px;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            user-select: none;
        }

        .modal-button:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modal-button-warning {
            background-color: #f59e0b;
            color: white;
        }

        .modal-button-warning:hover {
            background-color: #d97706;
        }

        .modal-button-success {
            background-color: #10b981;
            color: white;
        }

        .modal-button-success:hover {
            background-color: #059669;
        }

        /* 警告弹框样式 */
        .modal-warning .modal-header {
            border-bottom: 3px solid #f59e0b;
        }

        .modal-warning .modal-title {
            color: #92400e;
            display: flex;
            align-items: center;
        }

        .modal-warning .modal-title::before {
            content: '⚠️';
            margin-right: 8px;
            font-size: 24px;
        }

        /* 成功弹框样式 */
        .modal-success .modal-header {
            border-bottom: 3px solid #10b981;
        }

        .modal-success .modal-title {
            color: #065f46;
            display: flex;
            align-items: center;
        }

        .modal-success .modal-title::before {
            content: '✅';
            margin-right: 8px;
            font-size: 24px;
        }

        /* 错误弹框样式 */
        .modal-error .modal-header {
            border-bottom: 3px solid #ef4444;
        }

        .modal-error .modal-title {
            color: #991b1b;
            display: flex;
            align-items: center;
        }

        .modal-error .modal-title::before {
            content: '❌';
            margin-right: 8px;
            font-size: 24px;
        }

        /* 信息弹框样式 */
        .modal-info .modal-header {
            border-bottom: 3px solid #3b82f6;
        }

        .modal-info .modal-title {
            color: #1e40af;
            display: flex;
            align-items: center;
        }

        .modal-info .modal-title::before {
            content: 'ℹ️';
            margin-right: 8px;
            font-size: 24px;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .form-button {
                width: 100%;
                margin-right: 0;
                margin-bottom: 12px;
                padding: 14px 24px;
                font-size: 16px;
                min-height: 48px;
            }

            .form-button-group {
                flex-direction: column;
                gap: 12px;
            }

            .form-input, .form-select-input, .form-textarea-input, .form-datetime-input {
                padding: 14px 16px;
                font-size: 16px;
                min-height: 48px;
            }

            .form-select-input, .form-datetime-input {
                padding: 14px 40px 14px 16px;
            }

            /* 移动端横排适配 */
            .form-checkbox-horizontal.form-checkbox-two-columns,
            .form-radio-horizontal {
                gap: 12px;
            }

            .form-checkbox-horizontal.form-checkbox-two-columns {
                grid-template-columns: 1fr;
            }

            /* 弹窗移动端适配 */
            .modal-overlay {
                padding: 0;
                align-items: flex-end;
            }

            .modal-container {
                max-width: 100%;
                width: 100%;
                border-radius: 12px 12px 0 0;
                max-height: 90vh;
            }

            .modal-header {
                padding: 20px 20px 0 20px;
            }

            .modal-body {
                padding: 16px 20px 20px 20px;
            }

            .modal-footer {
                padding: 0 20px 20px 20px;
                flex-direction: column;
            }

            .modal-button {
                width: 100%;
                min-height: 44px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>H5表单组件库</h1>
            <p>简单、可复用、移动端自适应的表单组件集合</p>
        </div>

        <!-- 组件导航 -->
        <div class="form-demo">
            <h2 class="form-title">H5表单组件库 - 完整组件列表</h2>

            <div class="component-info">
                <h3>组件库概览</h3>
                <p>
                    本组件库包含30个实用组件，涵盖表单输入、数据展示、交互控制、布局设计等多个方面，
                    为H5表单开发提供完整的解决方案。点击下方按钮可查看各组件的详细示例和使用方法。
                </p>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">基础表单组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="text-input.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📝 TextInput 文本输入
                    </a>
                    <a href="textarea.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📄 Textarea 多行文本
                    </a>
                    <a href="select.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📋 Select 下拉选择
                    </a>
                    <a href="checkbox.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        ☑️ Checkbox 复选框
                    </a>
                    <a href="radio.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔘 Radio 单选按钮
                    </a>
                    <a href="buttons.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔲 Buttons 按钮组
                    </a>
                </div>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">高级输入组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="input-number.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔢 InputNumber 计数器
                    </a>
                    <a href="datetime.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📅 DateTime 日期时间
                    </a>
                    <a href="switch.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔘 Switch 开关
                    </a>
                    <a href="slider.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🎚️ Slider 滑块
                    </a>
                    <a href="rating.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        ⭐ Rating 星级评分
                    </a>
                    <a href="cascader.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📋 Cascader 级联选择
                    </a>
                </div>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">数据展示组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="table.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📊 Table 表格
                    </a>
                    <a href="image.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🖼️ Image 图片展示
                    </a>
                    <a href="statistic.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📈 Statistic 统计数值
                    </a>
                    <a href="tree.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🌳 Tree 树形控件
                    </a>
                    <a href="progress.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📊 Progress 进度条
                    </a>
                    <a href="tag.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🏷️ Tag 标签
                    </a>
                </div>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">交互控制组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="modal.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔲 Modal 弹窗
                    </a>
                    <a href="drawer.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📱 Drawer 抽屉
                    </a>
                    <a href="carousel.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🎠 Carousel 走马灯
                    </a>
                    <a href="tag-input.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🏷️ TagInput 标签输入
                    </a>
                    <a href="transfer.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        ↔️ Transfer 穿梭框
                    </a>
                    <a href="upload.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📤 Upload 上传
                    </a>
                </div>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">布局设计组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="layout.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📐 Layout 布局
                    </a>
                    <a href="fieldset.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        📦 Fieldset 字段集
                    </a>
                    <a href="border.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔲 Border 边框
                    </a>
                </div>
            </div>

            <div class="form-section">
                <h3 class="form-section-title">功能辅助组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <a href="icon.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        ⭐ Icon 图标
                    </a>
                    <a href="link.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        🔗 Link 文字链接
                    </a>
                    <a href="backtop.html" class="form-button form-button-secondary" style="text-decoration: none; text-align: center;">
                        ⬆️ Backtop 回到顶部
                    </a>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="form-demo">
            <h2 class="form-title">使用说明</h2>

            <div class="form-section">
                <h3 class="form-section-title">快速开始</h3>
                <p style="margin-bottom: 16px;">只需复制对应组件的HTML和CSS代码，即可直接在您的项目中使用：</p>

                <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                    <code style="font-family: 'Courier New', monospace; font-size: 14px;">
                        1. 复制组件的HTML结构<br>
                        2. 复制对应的CSS样式<br>
                        3. 根据需要调整类名和内容<br>
                        4. 集成到您的表单中
                    </code>
                </div>

                <p>每个组件文件都包含完整的示例代码和使用说明，可以直接在浏览器中打开查看效果。</p>
            </div>
        </div>
        <!-- 用户注册表单示例 -->
        <div class="form-demo">
            <h2 class="form-title">用户注册表单</h2>
            
            <form id="registrationForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3 class="form-section-title">基本信息</h3>
                    
                    <div class="form-grid">
                        <div class="form-input-group">
                            <label class="form-input-label" for="username">
                                用户名 <span class="form-input-required">*</span>
                            </label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="form-input" 
                                placeholder="请输入用户名"
                                required
                            >
                            <div class="form-input-message help">用户名长度为3-20个字符</div>
                        </div>

                        <div class="form-input-group">
                            <label class="form-input-label" for="email">
                                邮箱地址 <span class="form-input-required">*</span>
                            </label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                class="form-input" 
                                placeholder="请输入邮箱地址"
                                required
                            >
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-input-group">
                            <label class="form-input-label" for="password">
                                密码 <span class="form-input-required">*</span>
                            </label>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="请输入密码"
                                required
                            >
                            <div class="form-input-message help">至少8位，包含字母和数字</div>
                        </div>

                        <div class="form-input-group">
                            <label class="form-input-label" for="phone">
                                手机号码
                            </label>
                            <input 
                                type="tel" 
                                id="phone" 
                                name="phone" 
                                class="form-input" 
                                placeholder="请输入手机号码"
                            >
                        </div>
                    </div>
                </div>

                <!-- 个人信息 -->
                <div class="form-section">
                    <h3 class="form-section-title">个人信息</h3>
                    
                    <div class="form-grid">
                        <div class="form-radio">
                            <div class="form-radio-title">性别</div>
                            <div class="form-radio-horizontal">
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="male">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">男性</span>
                                </label>
                                
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="female">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">女性</span>
                                </label>
                                
                                <label class="form-radio-item">
                                    <input type="radio" name="gender" value="other">
                                    <span class="form-radio-custom"></span>
                                    <span class="form-radio-label">其他</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-select-group">
                            <label class="form-select-label" for="city">
                                所在城市 <span class="form-input-required">*</span>
                            </label>
                            <div class="form-select-wrapper">
                                <select id="city" name="city" class="form-select-input" required>
                                    <option value="" disabled selected>请选择城市</option>
                                    <option value="beijing">北京</option>
                                    <option value="shanghai">上海</option>
                                    <option value="guangzhou">广州</option>
                                    <option value="shenzhen">深圳</option>
                                    <option value="hangzhou">杭州</option>
                                </select>
                                <div class="form-select-arrow"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-checkbox">
                        <div class="form-checkbox-title">兴趣爱好</div>
                        <div class="form-checkbox-horizontal form-checkbox-two-columns">
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="hobbies" value="reading">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">阅读</span>
                            </label>
                            
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="hobbies" value="music">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">音乐</span>
                            </label>
                            
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="hobbies" value="sports">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">运动</span>
                            </label>
                            
                            <label class="form-checkbox-item">
                                <input type="checkbox" name="hobbies" value="travel">
                                <span class="form-checkbox-custom"></span>
                                <span class="form-checkbox-label">旅行</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 时间信息 -->
                <div class="form-section">
                    <h3 class="form-section-title">时间信息</h3>
                    
                    <div class="form-grid">
                        <div class="form-datetime-group">
                            <label class="form-datetime-label" for="birth-date">
                                出生日期 <span class="form-datetime-required">*</span>
                            </label>
                            <div class="form-datetime-wrapper">
                                <input 
                                    type="date" 
                                    id="birth-date" 
                                    name="birth-date" 
                                    class="form-datetime-input" 
                                    required
                                >
                            </div>
                        </div>

                        <div class="form-datetime-group">
                            <label class="form-datetime-label" for="preferred-time">
                                联系时间
                            </label>
                            <div class="form-datetime-wrapper">
                                <input 
                                    type="time" 
                                    id="preferred-time" 
                                    name="preferred-time" 
                                    class="form-datetime-input"
                                    value="14:00"
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技能和评价 -->
                <div class="form-section">
                    <h3 class="form-section-title">技能和评价</h3>
                    
                    <div class="form-tag-group">
                        <label class="form-tag-label">技能标签</label>
                        <div class="form-tag-container form-tag-blue" data-tag-input="user-skills">
                            <input type="text" class="form-tag-input-field" placeholder="输入技能并按回车添加">
                        </div>
                        <div class="form-tag-presets">
                            <span class="form-tag-preset" onclick="addPresetTag('user-skills', 'JavaScript')">JavaScript</span>
                            <span class="form-tag-preset" onclick="addPresetTag('user-skills', 'Python')">Python</span>
                            <span class="form-tag-preset" onclick="addPresetTag('user-skills', 'React')">React</span>
                            <span class="form-tag-preset" onclick="addPresetTag('user-skills', 'Vue.js')">Vue.js</span>
                        </div>
                    </div>

                    <div class="form-rating-group">
                        <label class="form-rating-label">整体满意度</label>
                        <div class="form-rating-stars" data-rating="satisfaction">
                            <label class="form-rating-star">
                                <input type="radio" name="satisfaction" value="1">
                                <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </label>
                            <label class="form-rating-star">
                                <input type="radio" name="satisfaction" value="2">
                                <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </label>
                            <label class="form-rating-star">
                                <input type="radio" name="satisfaction" value="3">
                                <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </label>
                            <label class="form-rating-star">
                                <input type="radio" name="satisfaction" value="4">
                                <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </label>
                            <label class="form-rating-star">
                                <input type="radio" name="satisfaction" value="5">
                                <svg class="form-rating-star-icon" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </label>
                        </div>
                        <div class="form-rating-text">
                            <span class="form-rating-current" id="satisfaction-text">请选择评分</span>
                        </div>
                    </div>
                </div>

                <!-- 注册进度 -->
                <div class="form-section">
                    <h3 class="form-section-title">注册进度</h3>
                    
                    <div class="form-progress-steps">
                        <div class="form-progress-step completed">
                            <div class="form-progress-step-circle">✓</div>
                            <div class="form-progress-step-title">基本信息</div>
                        </div>
                        <div class="form-progress-step completed">
                            <div class="form-progress-step-circle">✓</div>
                            <div class="form-progress-step-title">个人信息</div>
                        </div>
                        <div class="form-progress-step active">
                            <div class="form-progress-step-circle">3</div>
                            <div class="form-progress-step-title">技能评价</div>
                        </div>
                        <div class="form-progress-step">
                            <div class="form-progress-step-circle">4</div>
                            <div class="form-progress-step-title">完成注册</div>
                        </div>
                    </div>
                    
                    <div class="form-progress-group">
                        <div class="form-progress-container">
                            <div class="form-progress-bar" style="width: 75%"></div>
                        </div>
                        <div class="form-progress-text">
                            <span class="form-progress-status">注册进度</span>
                            <span class="form-progress-percentage">75%</span>
                        </div>
                    </div>
                </div>

                <!-- 附加信息 -->
                <div class="form-section">
                    <h3 class="form-section-title">附加信息</h3>
                    
                    <div class="form-textarea-group">
                        <label class="form-textarea-label" for="bio">
                            个人简介
                        </label>
                        <textarea 
                            id="bio" 
                            name="bio" 
                            class="form-textarea-input" 
                            placeholder="请简单介绍一下自己..."
                        ></textarea>
                        <div class="form-input-message help">可选，让其他用户更好地了解您</div>
                    </div>
                </div>

                <!-- 表单操作按钮 -->
                <div class="demo-actions">
                    <button type="button" class="form-button form-button-primary" onclick="showSubmitConfirm()">
                        注册账户
                    </button>
                    <button type="reset" class="form-button form-button-secondary">
                        重置表单
                    </button>
                    <button type="button" class="form-button form-button-danger" onclick="showCancelConfirm()">
                        取消注册
                    </button>
                </div>
                
                <!-- 弹窗组件演示 -->
                <div class="form-section">
                    <h3 class="form-section-title">弹窗组件演示</h3>
                    <div class="component-info" style="margin-bottom: 20px;">
                        <h3>弹窗组件特性</h3>
                        <p>
                            ✅ <strong>多种类型</strong>：警告、成功、错误、信息等多种预设样式<br>
                            ✅ <strong>响应式设计</strong>：完美适配移动端和桌面端<br>
                            ✅ <strong>交互友好</strong>：支持ESC键关闭、点击遮罩关闭<br>
                            ✅ <strong>动画效果</strong>：流畅的显示和隐藏动画<br>
                            ✅ <strong>可定制</strong>：支持自定义内容和按钮配置
                        </p>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 12px;">
                        <button class="form-button form-button-warning" onclick="showWarningModal()">警告弹框</button>
                        <button class="form-button form-button-success" onclick="showSuccessModal()">成功弹框</button>
                        <button class="form-button form-button-danger" onclick="showErrorModal()">错误弹框</button>
                        <button class="form-button form-button-primary" onclick="showInfoModal()">信息弹框</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 组件说明 -->
       <!-- <div class="component-info">
            <h3>组件特性</h3>
            <p>
                ✅ <strong>完全独立</strong>：每个组件都是自包含的，无外部依赖<br>
                ✅ <strong>移动端优化</strong>：完美适配H5移动端，支持触控操作<br>
                ✅ <strong>响应式设计</strong>：自动适应不同屏幕尺寸<br>
                ✅ <strong>可访问性</strong>：支持键盘导航和屏幕阅读器<br>
                ✅ <strong>现代样式</strong>：简洁美观的视觉设计<br>
                ✅ <strong>状态反馈</strong>：支持错误、成功、焦点等多种状态<br>
                ✅ <strong>浏览器兼容</strong>：兼容主流现代浏览器
            </p>
        </div>-->



        <!-- 弹窗组件 -->
        <!-- 警告弹框 -->
        <div id="warningModal" class="modal-overlay">
            <div class="modal-container modal-warning">
                <div class="modal-header">
                    <h3 class="modal-title">操作警告</h3>
                    <button class="modal-close" onclick="hideModal('warningModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您即将执行一个重要操作，此操作可能会影响您的数据。请确认您要继续执行此操作。</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('warningModal')">取消</button>
                    <button class="modal-button modal-button-warning" onclick="confirmWarning()">确认执行</button>
                </div>
            </div>
        </div>

        <!-- 成功弹框 -->
        <div id="successModal" class="modal-overlay">
            <div class="modal-container modal-success">
                <div class="modal-header">
                    <h3 class="modal-title">操作成功</h3>
                    <button class="modal-close" onclick="hideModal('successModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您的操作已成功完成！数据已保存，您可以继续使用系统。</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-success" onclick="hideModal('successModal')">确定</button>
                </div>
            </div>
        </div>

        <!-- 错误弹框 -->
        <div id="errorModal" class="modal-overlay">
            <div class="modal-container modal-error">
                <div class="modal-header">
                    <h3 class="modal-title">操作失败</h3>
                    <button class="modal-close" onclick="hideModal('errorModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">操作执行失败，可能是网络连接问题或服务器暂时不可用。</p>
                    <div style="margin-top: 12px; padding: 12px; background-color: #fef2f2; border-radius: 6px; font-size: 14px; color: #991b1b;">
                        <strong>错误详情：</strong> 连接超时 (错误代码: 500)
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('errorModal')">关闭</button>
                    <button class="modal-button modal-button-danger" onclick="retryOperation()">重试</button>
                </div>
            </div>
        </div>

        <!-- 信息弹框 -->
        <div id="infoModal" class="modal-overlay">
            <div class="modal-container modal-info">
                <div class="modal-header">
                    <h3 class="modal-title">系统通知</h3>
                    <button class="modal-close" onclick="hideModal('infoModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message">系统将在今晚 23:00 - 01:00 进行维护升级，期间可能会影响部分功能的使用。我们会尽快完成维护，感谢您的理解。</p>
                    <div style="margin-top: 16px; padding: 12px; background-color: #eff6ff; border-radius: 6px; font-size: 14px; color: #1e40af;">
                        <strong>提示：</strong> 建议您提前保存重要数据，避免数据丢失。
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-primary" onclick="hideModal('infoModal')">我知道了</button>
                </div>
            </div>
        </div>

        <!-- 确认弹框 -->
        <div id="confirmModal" class="modal-overlay">
            <div class="modal-container modal-warning">
                <div class="modal-header">
                    <h3 class="modal-title">确认操作</h3>
                    <button class="modal-close" onclick="hideModal('confirmModal')">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-message" id="confirmMessage">您确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-button modal-button-secondary" onclick="hideModal('confirmModal')">取消</button>
                    <button class="modal-button modal-button-primary" id="confirmButton" onclick="executeConfirmedAction()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 表单提交处理
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 收集表单数据
            const formData = new FormData(this);
            const data = {};
            
            // 处理普通字段
            for (let [key, value] of formData.entries()) {
                if (key === 'hobbies') {
                    // 处理多选框
                    if (!data[key]) data[key] = [];
                    data[key].push(value);
                } else {
                    data[key] = value;
                }
            }
            
            // 显示收集到的数据
            alert('表单提交成功！\n\n收集到的数据：\n' + JSON.stringify(data, null, 2));
        });
        
        // 取消注册
        function cancelRegistration() {
            if (confirm('确定要取消注册吗？')) {
                history.back();
            }
        }
        
        // 表单验证示例
        function validateForm() {
            const username = document.getElementById('username');
            const email = document.getElementById('email');
            
            // 用户名验证
            if (username.value.length < 3) {
                username.classList.add('error');
                username.nextElementSibling.textContent = '用户名至少需要3个字符';
                username.nextElementSibling.className = 'form-input-message error';
            } else {
                username.classList.remove('error');
                username.classList.add('success');
                username.nextElementSibling.textContent = '用户名可用';
                username.nextElementSibling.className = 'form-input-message success';
            }
            
            // 邮箱验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email.value)) {
                email.classList.add('error');
            } else {
                email.classList.remove('error');
                email.classList.add('success');
            }
        }
        
        // 实时验证
        document.getElementById('username').addEventListener('input', validateForm);
        document.getElementById('email').addEventListener('input', validateForm);
        
        // 初始化星级评分
        initRating();
        
        // 初始化标签输入
        initTagInputs();
        
        // 星级评分初始化函数
        function initRating() {
            const ratingGroups = document.querySelectorAll('[data-rating]');
            
            ratingGroups.forEach(group => {
                const stars = group.querySelectorAll('.form-rating-star');
                const ratingName = group.getAttribute('data-rating');
                const textElement = document.getElementById(ratingName + '-text');
                
                stars.forEach((star, index) => {
                    const input = star.querySelector('input[type="radio"]');
                    const rating = index + 1;
                    
                    star.addEventListener('click', () => {
                        if (input) {
                            input.checked = true;
                            updateRatingDisplay(group, rating, textElement);
                        }
                    });
                    
                    star.addEventListener('mouseenter', () => {
                        highlightStars(stars, rating);
                    });
                    
                    if (input) {
                        input.addEventListener('change', () => {
                            updateRatingDisplay(group, rating, textElement);
                        });
                    }
                });
                
                group.addEventListener('mouseleave', () => {
                    const checkedInput = group.querySelector('input[type="radio"]:checked');
                    if (checkedInput) {
                        const checkedRating = parseInt(checkedInput.value);
                        highlightStars(stars, checkedRating);
                    } else {
                        clearHighlight(stars);
                    }
                });
            });
        }
        
        function highlightStars(stars, rating) {
            stars.forEach((star, index) => {
                star.classList.remove('hover', 'active');
                if (index < rating) {
                    star.classList.add('hover');
                }
            });
        }
        
        function clearHighlight(stars) {
            stars.forEach(star => {
                star.classList.remove('hover');
            });
        }
        
        function updateRatingDisplay(group, rating, textElement) {
            const descriptions = {1: "很差", 2: "较差", 3: "一般", 4: "很好", 5: "非常好"};
            
            if (textElement) {
                textElement.textContent = `${rating} 星 - ${descriptions[rating]}`;
            }
            
            const stars = group.querySelectorAll('.form-rating-star');
            stars.forEach((star, index) => {
                star.classList.remove('active');
                if (index < rating) {
                    star.classList.add('active');
                }
            });
        }
        
        // 标签输入初始化函数
        function initTagInputs() {
            const tagContainers = document.querySelectorAll('[data-tag-input]');
            
            tagContainers.forEach(container => {
                const input = container.querySelector('.form-tag-input-field');
                if (!input) return;
                
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const value = input.value.trim();
                        if (value) {
                            addTag(container, value);
                            input.value = '';
                        }
                    } else if (e.key === 'Backspace' && input.value === '') {
                        const lastTag = container.querySelector('.form-tag-item:last-of-type');
                        if (lastTag) {
                            removeTag(lastTag.querySelector('.form-tag-remove'));
                        }
                    }
                });
                
                container.addEventListener('click', (e) => {
                    if (e.target === container || e.target.classList.contains('form-tag-input-field')) {
                        input.focus();
                    }
                });
            });
        }
        
        function addTag(container, text) {
            const existingTags = container.querySelectorAll('.form-tag-text');
            for (let tag of existingTags) {
                if (tag.textContent === text) {
                    return;
                }
            }
            
            const tagElement = document.createElement('div');
            tagElement.className = 'form-tag-item';
            tagElement.innerHTML = `
                <span class="form-tag-text">${text}</span>
                <button class="form-tag-remove" onclick="removeTag(this)">×</button>
            `;
            
            const input = container.querySelector('.form-tag-input-field');
            container.insertBefore(tagElement, input);
        }
        
        function removeTag(button) {
            const tagItem = button.parentElement;
            tagItem.remove();
        }
        
        function addPresetTag(tagType, text) {
            const container = document.querySelector(`[data-tag-input="${tagType}"]`);
            if (container) {
                addTag(container, text);
            }
        }

        // 弹窗相关函数
        // 显示弹窗
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                
                // 添加ESC键关闭功能
                document.addEventListener('keydown', handleEscKey);
                
                // 点击遮罩关闭
                modal.addEventListener('click', handleOverlayClick);
            }
        }

        // 隐藏弹窗
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';
                
                // 移除事件监听
                document.removeEventListener('keydown', handleEscKey);
                modal.removeEventListener('click', handleOverlayClick);
            }
        }

        // ESC键关闭
        function handleEscKey(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal-overlay.show');
                if (openModal) {
                    hideModal(openModal.id);
                }
            }
        }

        // 点击遮罩关闭
        function handleOverlayClick(e) {
            if (e.target === e.currentTarget) {
                hideModal(e.currentTarget.id);
            }
        }

        // 具体弹窗显示函数
        function showWarningModal() {
            showModal('warningModal');
        }

        function showSuccessModal() {
            showModal('successModal');
            // 3秒后自动关闭
            setTimeout(() => {
                hideModal('successModal');
            }, 3000);
        }

        function showErrorModal() {
            showModal('errorModal');
        }

        function showInfoModal() {
            showModal('infoModal');
        }

        // 表单操作相关弹窗
        function showSubmitConfirm() {
            document.getElementById('confirmMessage').textContent = '确定要提交注册表单吗？提交后将创建您的账户。';
            document.getElementById('confirmButton').textContent = '确认注册';
            document.getElementById('confirmButton').onclick = function() {
                submitRegistrationForm();
            };
            showModal('confirmModal');
        }

        function showCancelConfirm() {
            document.getElementById('confirmMessage').textContent = '确定要取消注册吗？已填写的信息将会丢失。';
            document.getElementById('confirmButton').textContent = '确认取消';
            document.getElementById('confirmButton').onclick = function() {
                cancelRegistration();
            };
            showModal('confirmModal');
        }

        // 业务逻辑函数
        function confirmWarning() {
            showSuccessModal();
            hideModal('warningModal');
        }

        function retryOperation() {
            hideModal('errorModal');
            showSuccessModal();
        }

        function executeConfirmedAction() {
            hideModal('confirmModal');
        }

        function submitRegistrationForm() {
            hideModal('confirmModal');
            // 模拟提交过程
            setTimeout(() => {
                showSuccessModal();
            }, 500);
        }

        function cancelRegistration() {
            hideModal('confirmModal');
            document.getElementById('registrationForm').reset();
            showInfoModal();
        }
    </script>
</body>
</html>