<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复选框组件</title>
</head>
<body>
    <!-- 复选框组件 -->
    <div class="form-checkbox">
        <style>
            .form-checkbox {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-checkbox input[type="checkbox"] {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .form-checkbox-item {
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                cursor: pointer;
                min-height: 44px;
                padding: 8px 0;
            }

            .form-checkbox-custom {
                position: relative;
                width: 20px;
                height: 20px;
                border: 2px solid #d1d5db;
                border-radius: 4px;
                background-color: #ffffff;
                margin-right: 12px;
                transition: all 0.2s ease;
                flex-shrink: 0;
            }

            .form-checkbox-custom::after {
                content: '';
                position: absolute;
                left: 6px;
                top: 2px;
                width: 6px;
                height: 10px;
                border: solid #ffffff;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom {
                background-color: #3b82f6;
                border-color: #3b82f6;
            }

            .form-checkbox input[type="checkbox"]:checked + .form-checkbox-custom::after {
                opacity: 1;
            }

            .form-checkbox input[type="checkbox"]:focus + .form-checkbox-custom {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                border-color: #3b82f6;
            }

            .form-checkbox-item:hover .form-checkbox-custom {
                border-color: #3b82f6;
            }

            .form-checkbox-label {
                font-size: 16px;
                color: #374151;
                line-height: 1.5;
                user-select: none;
            }

            .form-checkbox-title {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 8px;
            }

            /* 横排样式 */
            .form-checkbox-horizontal {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }

            .form-checkbox-horizontal .form-checkbox-item {
                margin-bottom: 0;
                margin-right: 0;
                flex: 0 0 auto;
            }

            /* 紧凑横排样式 */
            .form-checkbox-horizontal.form-checkbox-compact {
                gap: 12px;
            }

            .form-checkbox-horizontal.form-checkbox-compact .form-checkbox-item {
                min-height: 36px;
                padding: 6px 0;
            }

            .form-checkbox-horizontal.form-checkbox-compact .form-checkbox-custom {
                width: 18px;
                height: 18px;
                margin-right: 8px;
            }

            .form-checkbox-horizontal.form-checkbox-compact .form-checkbox-label {
                font-size: 14px;
            }

            /* 等宽横排样式 */
            .form-checkbox-horizontal.form-checkbox-equal {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 16px;
            }

            /* 两列横排 */
            .form-checkbox-horizontal.form-checkbox-two-columns {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
            }

            /* 三列横排 */
            .form-checkbox-horizontal.form-checkbox-three-columns {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 16px;
            }

            /* 按钮样式横排 */
            .form-checkbox-button-style .form-checkbox-item {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px 16px;
                margin-bottom: 8px;
                background-color: #ffffff;
                transition: all 0.2s ease;
                min-height: auto;
            }

            .form-checkbox-button-style .form-checkbox-custom {
                display: none;
            }

            .form-checkbox-button-style .form-checkbox-item:hover {
                border-color: #3b82f6;
                background-color: #f8faff;
            }

            .form-checkbox-button-style .form-checkbox-item:has(input:checked) {
                border-color: #3b82f6;
                background-color: #3b82f6;
                color: white;
            }

            .form-checkbox-button-style .form-checkbox-item:has(input:checked) .form-checkbox-label {
                color: white;
            }

            .form-checkbox-button-style .form-checkbox-item:has(input:focus) {
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            @media (max-width: 480px) {
                .form-checkbox {
                    margin: 12px 0;
                }
                
                .form-checkbox-item {
                    min-height: 44px;
                    padding: 10px 0;
                }
                
                .form-checkbox-custom {
                    width: 22px;
                    height: 22px;
                    margin-right: 14px;
                }
                
                .form-checkbox-label {
                    font-size: 16px;
                }

                /* 移动端横排适配 */
                .form-checkbox-horizontal {
                    gap: 12px;
                }

                .form-checkbox-horizontal.form-checkbox-two-columns,
                .form-checkbox-horizontal.form-checkbox-three-columns {
                    grid-template-columns: 1fr;
                    gap: 12px;
                }

                .form-checkbox-horizontal.form-checkbox-equal {
                    grid-template-columns: 1fr;
                }

                .form-checkbox-button-style .form-checkbox-item {
                    padding: 14px 16px;
                }
            }
        </style>

        <div class="form-checkbox-title">选择您的兴趣爱好</div>

        <label class="form-checkbox-item">
            <input type="checkbox" name="hobbies" value="reading">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">阅读</span>
        </label>
        
        <label class="form-checkbox-item">
            <input type="checkbox" name="hobbies" value="music">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">音乐</span>
        </label>
        
        <label class="form-checkbox-item">
            <input type="checkbox" name="hobbies" value="sports">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">运动</span>
        </label>
        
        <label class="form-checkbox-item">
            <input type="checkbox" name="hobbies" value="travel">
            <span class="form-checkbox-custom"></span>
            <span class="form-checkbox-label">旅行</span>
        </label>
    </div>

    <!-- 横排复选框 -->
    <div class="form-checkbox">
        <div class="form-checkbox-title">选择您的技能（横排显示）</div>
        <div class="form-checkbox-horizontal">
            <label class="form-checkbox-item">
                <input type="checkbox" name="skills" value="html">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">HTML</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="skills" value="css">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">CSS</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="skills" value="javascript">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">JavaScript</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="skills" value="react">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">React</span>
            </label>
        </div>
    </div>

    <!-- 紧凑横排复选框 -->
    <div class="form-checkbox">
        <div class="form-checkbox-title">选择通知方式（紧凑横排）</div>
        <div class="form-checkbox-horizontal form-checkbox-compact">
            <label class="form-checkbox-item">
                <input type="checkbox" name="notifications" value="email">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">邮件</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="notifications" value="sms">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">短信</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="notifications" value="push">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">推送</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="notifications" value="wechat">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">微信</span>
            </label>
        </div>
    </div>

    <!-- 两列横排复选框 -->
    <div class="form-checkbox">
        <div class="form-checkbox-title">选择您的偏好（两列布局）</div>
        <div class="form-checkbox-horizontal form-checkbox-two-columns">
            <label class="form-checkbox-item">
                <input type="checkbox" name="preferences" value="dark-mode">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">深色模式</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="preferences" value="auto-save">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">自动保存</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="preferences" value="notifications">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">桌面通知</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="preferences" value="sound">
                <span class="form-checkbox-custom"></span>
                <span class="form-checkbox-label">提示音效</span>
            </label>
        </div>
    </div>

    <!-- 按钮样式横排复选框 -->
    <div class="form-checkbox">
        <div class="form-checkbox-title">选择您感兴趣的话题（按钮样式）</div>
        <div class="form-checkbox-horizontal form-checkbox-button-style">
            <label class="form-checkbox-item">
                <input type="checkbox" name="topics" value="technology">
                <span class="form-checkbox-label">科技</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="topics" value="design">
                <span class="form-checkbox-label">设计</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="topics" value="business">
                <span class="form-checkbox-label">商业</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="topics" value="health">
                <span class="form-checkbox-label">健康</span>
            </label>
            
            <label class="form-checkbox-item">
                <input type="checkbox" name="topics" value="travel">
                <span class="form-checkbox-label">旅行</span>
            </label>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;div class="form-checkbox"&gt;
    &lt;div class="form-checkbox-title"&gt;选择您的兴趣爱好&lt;/div&gt;
    
    &lt;label class="form-checkbox-item"&gt;
        &lt;input type="checkbox" name="hobbies" value="reading"&gt;
        &lt;span class="form-checkbox-custom"&gt;&lt;/span&gt;
        &lt;span class="form-checkbox-label"&gt;阅读&lt;/span&gt;
    &lt;/label&gt;
    
    &lt;!-- 更多选项... --&gt;
&lt;/div&gt;

&lt;!-- 横排复选框 --&gt;
&lt;div class="form-checkbox-horizontal"&gt;
    &lt;label class="form-checkbox-item"&gt;
        &lt;input type="checkbox" name="skills" value="html"&gt;
        &lt;span class="form-checkbox-custom"&gt;&lt;/span&gt;
        &lt;span class="form-checkbox-label"&gt;HTML&lt;/span&gt;
    &lt;/label&gt;
    &lt;label class="form-checkbox-item"&gt;
        &lt;input type="checkbox" name="skills" value="css"&gt;
        &lt;span class="form-checkbox-custom"&gt;&lt;/span&gt;
        &lt;span class="form-checkbox-label"&gt;CSS&lt;/span&gt;
    &lt;/label&gt;
&lt;/div&gt;

&lt;!-- 紧凑横排 --&gt;
&lt;div class="form-checkbox-horizontal form-checkbox-compact"&gt;
    &lt;!-- 复选框项目... --&gt;
&lt;/div&gt;

&lt;!-- 两列布局 --&gt;
&lt;div class="form-checkbox-horizontal form-checkbox-two-columns"&gt;
    &lt;!-- 复选框项目... --&gt;
&lt;/div&gt;

&lt;!-- 三列布局 --&gt;
&lt;div class="form-checkbox-horizontal form-checkbox-three-columns"&gt;
    &lt;!-- 复选框项目... --&gt;
&lt;/div&gt;

&lt;!-- 按钮样式 --&gt;
&lt;div class="form-checkbox-horizontal form-checkbox-button-style"&gt;
    &lt;label class="form-checkbox-item"&gt;
        &lt;input type="checkbox" name="topics" value="technology"&gt;
        &lt;span class="form-checkbox-label"&gt;科技&lt;/span&gt;
    &lt;/label&gt;
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>