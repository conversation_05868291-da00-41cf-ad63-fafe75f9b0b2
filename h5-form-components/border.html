<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>边框组件</title>
</head>
<body>
    <!-- 边框组件 -->
    <div class="form-border">
        <style>
            .form-border {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-border-section {
                margin-bottom: 40px;
            }

            .form-border-title {
                font-size: 18px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e5e7eb;
            }

            /* 基础边框样式 */
            .form-border-basic {
                border: 1px solid #d1d5db;
                padding: 16px;
                margin-bottom: 16px;
                border-radius: 4px;
            }

            /* 边框宽度 */
            .form-border-thin { border-width: 1px; }
            .form-border-medium { border-width: 2px; }
            .form-border-thick { border-width: 4px; }
            .form-border-extra-thick { border-width: 8px; }

            /* 边框样式 */
            .form-border-solid { border-style: solid; }
            .form-border-dashed { border-style: dashed; }
            .form-border-dotted { border-style: dotted; }
            .form-border-double { border-style: double; }
            .form-border-groove { border-style: groove; }
            .form-border-ridge { border-style: ridge; }
            .form-border-inset { border-style: inset; }
            .form-border-outset { border-style: outset; }

            /* 边框颜色 */
            .form-border-gray { border-color: #d1d5db; }
            .form-border-blue { border-color: #3b82f6; }
            .form-border-green { border-color: #10b981; }
            .form-border-red { border-color: #ef4444; }
            .form-border-yellow { border-color: #f59e0b; }
            .form-border-purple { border-color: #8b5cf6; }
            .form-border-pink { border-color: #ec4899; }
            .form-border-indigo { border-color: #6366f1; }

            /* 单边边框 */
            .form-border-top { border-top: 2px solid #3b82f6; }
            .form-border-right { border-right: 2px solid #3b82f6; }
            .form-border-bottom { border-bottom: 2px solid #3b82f6; }
            .form-border-left { border-left: 2px solid #3b82f6; }

            /* 圆角边框 */
            .form-border-rounded-none { border-radius: 0; }
            .form-border-rounded-sm { border-radius: 2px; }
            .form-border-rounded { border-radius: 4px; }
            .form-border-rounded-md { border-radius: 6px; }
            .form-border-rounded-lg { border-radius: 8px; }
            .form-border-rounded-xl { border-radius: 12px; }
            .form-border-rounded-2xl { border-radius: 16px; }
            .form-border-rounded-3xl { border-radius: 24px; }
            .form-border-rounded-full { border-radius: 9999px; }

            /* 渐变边框 */
            .form-border-gradient {
                position: relative;
                background: white;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .form-border-gradient::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
                border-radius: 10px;
                z-index: -1;
            }

            .form-border-gradient.blue::before {
                background: linear-gradient(45deg, #3b82f6, #1e40af);
            }

            .form-border-gradient.green::before {
                background: linear-gradient(45deg, #10b981, #065f46);
            }

            .form-border-gradient.red::before {
                background: linear-gradient(45deg, #ef4444, #991b1b);
            }

            .form-border-gradient.rainbow::before {
                background: linear-gradient(45deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ec4899);
            }

            /* 阴影边框 */
            .form-border-shadow {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
                transition: all 0.3s ease;
            }

            .form-border-shadow.sm {
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            }

            .form-border-shadow.md {
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            .form-border-shadow.lg {
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
            }

            .form-border-shadow.xl {
                box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
            }

            .form-border-shadow.colored {
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            .form-border-shadow:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }

            /* 发光边框 */
            .form-border-glow {
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
                transition: all 0.3s ease;
            }

            .form-border-glow.blue {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-border-glow.green {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-border-glow.red {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            .form-border-glow.yellow {
                border-color: #f59e0b;
                box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
            }

            .form-border-glow:hover {
                box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2);
            }

            .form-border-glow.green:hover {
                box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.2);
            }

            .form-border-glow.red:hover {
                box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.2);
            }

            .form-border-glow.yellow:hover {
                box-shadow: 0 0 0 6px rgba(245, 158, 11, 0.2);
            }

            /* 动画边框 */
            .form-border-animated {
                position: relative;
                border: 2px solid transparent;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 16px;
                background: white;
                overflow: hidden;
            }

            .form-border-animated::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
                background-size: 400% 400%;
                border-radius: 10px;
                z-index: -1;
                animation: borderAnimation 3s ease infinite;
            }

            @keyframes borderAnimation {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .form-border-animated.pulse::before {
                animation: borderPulse 2s ease-in-out infinite;
            }

            @keyframes borderPulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .form-border-animated.rotate::before {
                animation: borderRotate 3s linear infinite;
            }

            @keyframes borderRotate {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 装饰性边框 */
            .form-border-decorative {
                position: relative;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 16px;
                background: white;
            }

            .form-border-decorative.corner-dots::before,
            .form-border-decorative.corner-dots::after {
                content: '';
                position: absolute;
                width: 8px;
                height: 8px;
                background: #3b82f6;
                border-radius: 50%;
            }

            .form-border-decorative.corner-dots::before {
                top: -4px;
                left: -4px;
            }

            .form-border-decorative.corner-dots::after {
                bottom: -4px;
                right: -4px;
            }

            .form-border-decorative.corner-lines::before,
            .form-border-decorative.corner-lines::after {
                content: '';
                position: absolute;
                width: 20px;
                height: 20px;
                border: 2px solid #3b82f6;
            }

            .form-border-decorative.corner-lines::before {
                top: -1px;
                left: -1px;
                border-right: none;
                border-bottom: none;
            }

            .form-border-decorative.corner-lines::after {
                bottom: -1px;
                right: -1px;
                border-left: none;
                border-top: none;
            }

            /* 卡片边框 */
            .form-border-card {
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                padding: 24px;
                margin-bottom: 16px;
                background: white;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .form-border-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            }

            .form-border-card:hover {
                border-color: #3b82f6;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .form-border-card.green::before {
                background: linear-gradient(90deg, #10b981, #059669);
            }

            .form-border-card.red::before {
                background: linear-gradient(90deg, #ef4444, #dc2626);
            }

            .form-border-card.yellow::before {
                background: linear-gradient(90deg, #f59e0b, #d97706);
            }

            /* 演示内容样式 */
            .demo-content {
                color: #6b7280;
                font-size: 14px;
                line-height: 1.5;
            }

            .demo-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 16px;
                margin-bottom: 20px;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .demo-grid {
                    grid-template-columns: 1fr;
                }

                .form-border-basic,
                .form-border-gradient,
                .form-border-shadow,
                .form-border-glow,
                .form-border-animated,
                .form-border-decorative,
                .form-border-card {
                    padding: 12px;
                }
            }
        </style>

        <!-- 基础边框样式 -->
        <div class="form-border-section">
            <h3 class="form-border-title">基础边框样式</h3>
            <div class="demo-grid">
                <div class="form-border-basic form-border-solid form-border-gray">
                    <div class="demo-content">实线边框 (solid)</div>
                </div>
                <div class="form-border-basic form-border-dashed form-border-blue">
                    <div class="demo-content">虚线边框 (dashed)</div>
                </div>
                <div class="form-border-basic form-border-dotted form-border-green">
                    <div class="demo-content">点线边框 (dotted)</div>
                </div>
                <div class="form-border-basic form-border-double form-border-red">
                    <div class="demo-content">双线边框 (double)</div>
                </div>
            </div>
        </div>

        <!-- 边框宽度 -->
        <div class="form-border-section">
            <h3 class="form-border-title">边框宽度</h3>
            <div class="demo-grid">
                <div class="form-border-basic form-border-thin form-border-blue">
                    <div class="demo-content">细边框 (1px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue">
                    <div class="demo-content">中等边框 (2px)</div>
                </div>
                <div class="form-border-basic form-border-thick form-border-blue">
                    <div class="demo-content">粗边框 (4px)</div>
                </div>
                <div class="form-border-basic form-border-extra-thick form-border-blue">
                    <div class="demo-content">超粗边框 (8px)</div>
                </div>
            </div>
        </div>

        <!-- 边框颜色 -->
        <div class="form-border-section">
            <h3 class="form-border-title">边框颜色</h3>
            <div class="demo-grid">
                <div class="form-border-basic form-border-medium form-border-blue">
                    <div class="demo-content">蓝色边框</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-green">
                    <div class="demo-content">绿色边框</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-red">
                    <div class="demo-content">红色边框</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-yellow">
                    <div class="demo-content">黄色边框</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-purple">
                    <div class="demo-content">紫色边框</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-pink">
                    <div class="demo-content">粉色边框</div>
                </div>
            </div>
        </div>

        <!-- 单边边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">单边边框</h3>
            <div class="demo-grid">
                <div class="form-border-basic form-border-top">
                    <div class="demo-content">顶部边框</div>
                </div>
                <div class="form-border-basic form-border-right">
                    <div class="demo-content">右侧边框</div>
                </div>
                <div class="form-border-basic form-border-bottom">
                    <div class="demo-content">底部边框</div>
                </div>
                <div class="form-border-basic form-border-left">
                    <div class="demo-content">左侧边框</div>
                </div>
            </div>
        </div>

        <!-- 圆角边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">圆角边框</h3>
            <div class="demo-grid">
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded-none">
                    <div class="demo-content">无圆角 (0px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded">
                    <div class="demo-content">小圆角 (4px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded-lg">
                    <div class="demo-content">中圆角 (8px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded-xl">
                    <div class="demo-content">大圆角 (12px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded-2xl">
                    <div class="demo-content">超大圆角 (16px)</div>
                </div>
                <div class="form-border-basic form-border-medium form-border-blue form-border-rounded-full" style="text-align: center;">
                    <div class="demo-content">圆形</div>
                </div>
            </div>
        </div>

        <!-- 渐变边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">渐变边框</h3>
            <div class="demo-grid">
                <div class="form-border-gradient">
                    <div class="demo-content">彩虹渐变边框</div>
                </div>
                <div class="form-border-gradient blue">
                    <div class="demo-content">蓝色渐变边框</div>
                </div>
                <div class="form-border-gradient green">
                    <div class="demo-content">绿色渐变边框</div>
                </div>
                <div class="form-border-gradient red">
                    <div class="demo-content">红色渐变边框</div>
                </div>
            </div>
        </div>

        <!-- 阴影边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">阴影边框</h3>
            <div class="demo-grid">
                <div class="form-border-shadow sm">
                    <div class="demo-content">小阴影</div>
                </div>
                <div class="form-border-shadow md">
                    <div class="demo-content">中等阴影</div>
                </div>
                <div class="form-border-shadow lg">
                    <div class="demo-content">大阴影</div>
                </div>
                <div class="form-border-shadow colored">
                    <div class="demo-content">彩色阴影</div>
                </div>
            </div>
        </div>

        <!-- 发光边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">发光边框</h3>
            <div class="demo-grid">
                <div class="form-border-glow blue">
                    <div class="demo-content">蓝色发光边框</div>
                </div>
                <div class="form-border-glow green">
                    <div class="demo-content">绿色发光边框</div>
                </div>
                <div class="form-border-glow red">
                    <div class="demo-content">红色发光边框</div>
                </div>
                <div class="form-border-glow yellow">
                    <div class="demo-content">黄色发光边框</div>
                </div>
            </div>
        </div>

        <!-- 动画边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">动画边框</h3>
            <div class="demo-grid">
                <div class="form-border-animated">
                    <div class="demo-content">流动渐变边框</div>
                </div>
                <div class="form-border-animated pulse">
                    <div class="demo-content">脉冲边框</div>
                </div>
                <div class="form-border-animated rotate">
                    <div class="demo-content">旋转边框</div>
                </div>
            </div>
        </div>

        <!-- 装饰性边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">装饰性边框</h3>
            <div class="demo-grid">
                <div class="form-border-decorative corner-dots">
                    <div class="demo-content">角点装饰边框</div>
                </div>
                <div class="form-border-decorative corner-lines">
                    <div class="demo-content">角线装饰边框</div>
                </div>
            </div>
        </div>

        <!-- 卡片边框 -->
        <div class="form-border-section">
            <h3 class="form-border-title">卡片边框</h3>
            <div class="demo-grid">
                <div class="form-border-card">
                    <div class="demo-content">
                        <strong>默认卡片</strong><br>
                        这是一个带有顶部装饰条的卡片边框，悬停时会有动画效果。
                    </div>
                </div>
                <div class="form-border-card green">
                    <div class="demo-content">
                        <strong>绿色卡片</strong><br>
                        绿色主题的卡片边框，适合成功状态或环保主题。
                    </div>
                </div>
                <div class="form-border-card red">
                    <div class="demo-content">
                        <strong>红色卡片</strong><br>
                        红色主题的卡片边框，适合警告或重要信息。
                    </div>
                </div>
                <div class="form-border-card yellow">
                    <div class="demo-content">
                        <strong>黄色卡片</strong><br>
                        黄色主题的卡片边框，适合提示或注意事项。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 基础边框 --&gt;
&lt;div class="form-border-basic form-border-medium form-border-blue"&gt;
    内容
&lt;/div&gt;

&lt;!-- 渐变边框 --&gt;
&lt;div class="form-border-gradient blue"&gt;
    内容
&lt;/div&gt;

&lt;!-- 发光边框 --&gt;
&lt;div class="form-border-glow green"&gt;
    内容
&lt;/div&gt;

&lt;!-- 动画边框 --&gt;
&lt;div class="form-border-animated pulse"&gt;
    内容
&lt;/div&gt;

&lt;!-- 卡片边框 --&gt;
&lt;div class="form-border-card green"&gt;
    内容
&lt;/div&gt;</code></pre>
    </div>
</body>
</html>
