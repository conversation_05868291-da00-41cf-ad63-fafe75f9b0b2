<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期和时间选择器组件</title>
</head>
<body>
    <!-- 日期和时间选择器组件 -->
    <div class="form-datetime">
        <style>
            .form-datetime {
                margin: 16px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .form-datetime-group {
                margin-bottom: 20px;
            }

            .form-datetime-label {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                margin-bottom: 6px;
            }

            .form-datetime-required {
                color: #ef4444;
                margin-left: 2px;
            }

            .form-datetime-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
            }

            .form-datetime-input {
                width: 100%;
                padding: 12px 40px 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                min-height: 44px;
                box-sizing: border-box;
                cursor: pointer;
            }

            .form-datetime-input:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-datetime-input:hover:not(:focus) {
                border-color: #9ca3af;
            }

            /* 日期输入框特殊样式 */
            .form-datetime-input[type="date"]::-webkit-calendar-picker-indicator,
            .form-datetime-input[type="time"]::-webkit-calendar-picker-indicator,
            .form-datetime-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
                background-size: 16px 16px;
                width: 16px;
                height: 16px;
                opacity: 0.7;
                transition: opacity 0.2s ease;
            }

            .form-datetime-input[type="time"]::-webkit-calendar-picker-indicator {
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg>') no-repeat center;
            }

            .form-datetime-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line><circle cx="12" cy="16" r="2"></circle></svg>') no-repeat center;
            }

            .form-datetime-input:hover::-webkit-calendar-picker-indicator {
                opacity: 1;
            }

            /* 错误状态 */
            .form-datetime-input.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            .form-datetime-input.error:focus {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            /* 成功状态 */
            .form-datetime-input.success {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-datetime-input.success:focus {
                border-color: #10b981;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            }

            .form-datetime-message {
                margin-top: 6px;
                font-size: 14px;
                line-height: 1.4;
            }

            .form-datetime-message.error {
                color: #ef4444;
            }

            .form-datetime-message.success {
                color: #10b981;
            }

            .form-datetime-message.help {
                color: #6b7280;
            }

            /* 日期时间范围选择 */
            .form-datetime-range {
                display: grid;
                grid-template-columns: 1fr auto 1fr;
                gap: 12px;
                align-items: end;
            }

            .form-datetime-separator {
                font-size: 16px;
                color: #6b7280;
                font-weight: 500;
                padding-bottom: 12px;
                text-align: center;
            }

            /* 自定义日期选择器样式 */
            .form-datetime-custom {
                position: relative;
            }

            .form-datetime-display {
                width: 100%;
                padding: 12px 40px 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                color: #374151;
                background-color: #ffffff;
                transition: all 0.2s ease;
                outline: none;
                min-height: 44px;
                box-sizing: border-box;
                cursor: pointer;
                user-select: none;
            }

            .form-datetime-display:focus,
            .form-datetime-display.active {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-datetime-display:hover:not(:focus):not(.active) {
                border-color: #9ca3af;
            }

            .form-datetime-icon {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                opacity: 0.7;
                pointer-events: none;
                transition: opacity 0.2s ease;
            }

            .form-datetime-display:hover + .form-datetime-icon {
                opacity: 1;
            }

            /* 快捷选择按钮 */
            .form-datetime-shortcuts {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-top: 8px;
            }

            .form-datetime-shortcut {
                padding: 6px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: #ffffff;
                color: #374151;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                user-select: none;
            }

            .form-datetime-shortcut:hover {
                border-color: #3b82f6;
                background-color: #f0f9ff;
                color: #3b82f6;
            }

            .form-datetime-shortcut.active {
                border-color: #3b82f6;
                background-color: #3b82f6;
                color: #ffffff;
            }

            @media (max-width: 480px) {
                .form-datetime {
                    margin: 12px 0;
                }

                .form-datetime-input,
                .form-datetime-display {
                    padding: 14px 40px 14px 16px;
                    font-size: 16px;
                    min-height: 48px;
                }

                .form-datetime-label {
                    font-size: 16px;
                }

                .form-datetime-range {
                    grid-template-columns: 1fr;
                    gap: 16px;
                }

                .form-datetime-separator {
                    display: none;
                }

                .form-datetime-shortcuts {
                    justify-content: center;
                }

                .form-datetime-shortcut {
                    flex: 1;
                    text-align: center;
                    min-width: 80px;
                }
            }
        </style>

        <!-- 日期选择器 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="birth-date">
                出生日期 <span class="form-datetime-required">*</span>
            </label>
            <div class="form-datetime-wrapper">
                <input 
                    type="date" 
                    id="birth-date" 
                    name="birth-date" 
                    class="form-datetime-input" 
                    required
                >
            </div>
            <div class="form-datetime-message help">请选择您的出生日期</div>
        </div>

        <!-- 时间选择器 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="meeting-time">
                会议时间
            </label>
            <div class="form-datetime-wrapper">
                <input 
                    type="time" 
                    id="meeting-time" 
                    name="meeting-time" 
                    class="form-datetime-input"
                    value="14:30"
                >
            </div>
            <div class="form-datetime-message help">选择会议开始时间</div>
        </div>

        <!-- 日期时间选择器 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="appointment">
                预约时间 <span class="form-datetime-required">*</span>
            </label>
            <div class="form-datetime-wrapper">
                <input 
                    type="datetime-local" 
                    id="appointment" 
                    name="appointment" 
                    class="form-datetime-input" 
                    required
                >
            </div>
            <div class="form-datetime-message help">请选择预约的具体日期和时间</div>
        </div>

        <!-- 错误状态示例 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="deadline-error">
                截止日期 <span class="form-datetime-required">*</span>
            </label>
            <div class="form-datetime-wrapper">
                <input 
                    type="date" 
                    id="deadline-error" 
                    name="deadline" 
                    class="form-datetime-input error" 
                    value="2023-01-01"
                    required
                >
            </div>
            <div class="form-datetime-message error">截止日期不能早于今天</div>
        </div>

        <!-- 成功状态示例 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="event-date">
                活动日期
            </label>
            <div class="form-datetime-wrapper">
                <input 
                    type="date" 
                    id="event-date" 
                    name="event-date" 
                    class="form-datetime-input success" 
                    value="2024-12-25"
                >
            </div>
            <div class="form-datetime-message success">日期选择完成</div>
        </div>

        <!-- 日期范围选择 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label">
                请假期间 <span class="form-datetime-required">*</span>
            </label>
            <div class="form-datetime-range">
                <div class="form-datetime-wrapper">
                    <input 
                        type="date" 
                        id="leave-start" 
                        name="leave-start" 
                        class="form-datetime-input" 
                        required
                    >
                </div>
                <div class="form-datetime-separator">至</div>
                <div class="form-datetime-wrapper">
                    <input 
                        type="date" 
                        id="leave-end" 
                        name="leave-end" 
                        class="form-datetime-input" 
                        required
                    >
                </div>
            </div>
            <div class="form-datetime-message help">请选择请假的开始和结束日期</div>
        </div>

        <!-- 带快捷选择的日期选择器 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label" for="report-date">
                报告日期
            </label>
            <div class="form-datetime-custom">
                <input 
                    type="date" 
                    id="report-date" 
                    name="report-date" 
                    class="form-datetime-input"
                >
                <div class="form-datetime-shortcuts">
                    <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 0)">
                        今天
                    </button>
                    <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 1)">
                        明天
                    </button>
                    <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 7)">
                        下周
                    </button>
                    <button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('report-date', 30)">
                        下月
                    </button>
                </div>
            </div>
            <div class="form-datetime-message help">可通过快捷按钮快速选择日期</div>
        </div>

        <!-- 时间范围选择 -->
        <div class="form-datetime-group">
            <label class="form-datetime-label">
                工作时间
            </label>
            <div class="form-datetime-range">
                <div class="form-datetime-wrapper">
                    <input 
                        type="time" 
                        id="work-start" 
                        name="work-start" 
                        class="form-datetime-input" 
                        value="09:00"
                    >
                </div>
                <div class="form-datetime-separator">至</div>
                <div class="form-datetime-wrapper">
                    <input 
                        type="time" 
                        id="work-end" 
                        name="work-end" 
                        class="form-datetime-input" 
                        value="18:00"
                    >
                </div>
            </div>
            <div class="form-datetime-message help">请设置您的工作时间段</div>
        </div>

        <script>
            // 日期快捷选择功能
            function setDateShortcut(inputId, daysOffset) {
                const input = document.getElementById(inputId);
                const date = new Date();
                date.setDate(date.getDate() + daysOffset);
                
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                
                input.value = `${year}-${month}-${day}`;
                
                // 更新快捷按钮状态
                const shortcuts = input.parentElement.querySelectorAll('.form-datetime-shortcut');
                shortcuts.forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
                
                // 触发change事件
                input.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            // 日期范围验证
            function validateDateRange() {
                const startDate = document.getElementById('leave-start');
                const endDate = document.getElementById('leave-end');
                
                if (startDate.value && endDate.value) {
                    const start = new Date(startDate.value);
                    const end = new Date(endDate.value);
                    
                    if (start > end) {
                        endDate.classList.add('error');
                        endDate.parentElement.parentElement.querySelector('.form-datetime-message').textContent = '结束日期不能早于开始日期';
                        endDate.parentElement.parentElement.querySelector('.form-datetime-message').className = 'form-datetime-message error';
                    } else {
                        startDate.classList.remove('error');
                        endDate.classList.remove('error');
                        endDate.parentElement.parentElement.querySelector('.form-datetime-message').textContent = '请选择请假的开始和结束日期';
                        endDate.parentElement.parentElement.querySelector('.form-datetime-message').className = 'form-datetime-message help';
                    }
                }
            }
            
            // 添加日期范围验证事件监听
            document.getElementById('leave-start').addEventListener('change', validateDateRange);
            document.getElementById('leave-end').addEventListener('change', validateDateRange);
            
            // 设置日期输入的最小值为今天
            function setMinDate() {
                const today = new Date();
                const year = today.getFullYear();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                const todayString = `${year}-${month}-${day}`;
                
                // 为需要限制最小日期的输入框设置min属性
                const futureInputs = ['appointment', 'leave-start', 'leave-end', 'report-date'];
                futureInputs.forEach(id => {
                    const input = document.getElementById(id);
                    if (input) {
                        input.setAttribute('min', todayString);
                    }
                });
            }
            
            // 页面加载时设置最小日期
            document.addEventListener('DOMContentLoaded', setMinDate);
            
            // 时间范围验证
            function validateTimeRange() {
                const startTime = document.getElementById('work-start');
                const endTime = document.getElementById('work-end');
                
                if (startTime.value && endTime.value) {
                    if (startTime.value >= endTime.value) {
                        endTime.classList.add('error');
                        endTime.parentElement.parentElement.querySelector('.form-datetime-message').textContent = '结束时间必须晚于开始时间';
                        endTime.parentElement.parentElement.querySelector('.form-datetime-message').className = 'form-datetime-message error';
                    } else {
                        startTime.classList.remove('error');
                        endTime.classList.remove('error');
                        endTime.parentElement.parentElement.querySelector('.form-datetime-message').textContent = '请设置您的工作时间段';
                        endTime.parentElement.parentElement.querySelector('.form-datetime-message').className = 'form-datetime-message help';
                    }
                }
            }
            
            // 添加时间范围验证事件监听
            document.getElementById('work-start').addEventListener('change', validateTimeRange);
            document.getElementById('work-end').addEventListener('change', validateTimeRange);
        </script>
    </div>

    <!-- 示例用法 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
        <h3>示例用法</h3>
        <pre><code>&lt;!-- 日期选择器 --&gt;
&lt;div class="form-datetime-group"&gt;
    &lt;label class="form-datetime-label" for="birth-date"&gt;
        出生日期 &lt;span class="form-datetime-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-datetime-wrapper"&gt;
        &lt;input 
            type="date" 
            id="birth-date" 
            name="birth-date" 
            class="form-datetime-input" 
            required
        &gt;
    &lt;/div&gt;
    &lt;div class="form-datetime-message help"&gt;请选择您的出生日期&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 时间选择器 --&gt;
&lt;div class="form-datetime-group"&gt;
    &lt;label class="form-datetime-label" for="meeting-time"&gt;
        会议时间
    &lt;/label&gt;
    &lt;div class="form-datetime-wrapper"&gt;
        &lt;input 
            type="time" 
            id="meeting-time" 
            name="meeting-time" 
            class="form-datetime-input"
            value="14:30"
        &gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 日期时间选择器 --&gt;
&lt;div class="form-datetime-group"&gt;
    &lt;label class="form-datetime-label" for="appointment"&gt;
        预约时间 &lt;span class="form-datetime-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-datetime-wrapper"&gt;
        &lt;input 
            type="datetime-local" 
            id="appointment" 
            name="appointment" 
            class="form-datetime-input" 
            required
        &gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 日期范围选择 --&gt;
&lt;div class="form-datetime-group"&gt;
    &lt;label class="form-datetime-label"&gt;
        请假期间 &lt;span class="form-datetime-required"&gt;*&lt;/span&gt;
    &lt;/label&gt;
    &lt;div class="form-datetime-range"&gt;
        &lt;div class="form-datetime-wrapper"&gt;
            &lt;input type="date" id="leave-start" name="leave-start" class="form-datetime-input" required&gt;
        &lt;/div&gt;
        &lt;div class="form-datetime-separator"&gt;至&lt;/div&gt;
        &lt;div class="form-datetime-wrapper"&gt;
            &lt;input type="date" id="leave-end" name="leave-end" class="form-datetime-input" required&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;!-- 带快捷选择 --&gt;
&lt;div class="form-datetime-shortcuts"&gt;
    &lt;button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('input-id', 0)"&gt;
        今天
    &lt;/button&gt;
    &lt;button type="button" class="form-datetime-shortcut" onclick="setDateShortcut('input-id', 1)"&gt;
        明天
    &lt;/button&gt;
&lt;/div&gt;

&lt;!-- 错误状态 --&gt;
&lt;input class="form-datetime-input error" ... &gt;
&lt;div class="form-datetime-message error"&gt;错误信息&lt;/div&gt;

&lt;!-- 成功状态 --&gt;
&lt;input class="form-datetime-input success" ... &gt;
&lt;div class="form-datetime-message success"&gt;成功信息&lt;/div&gt;</code></pre>
    </div>
</body>
</html>