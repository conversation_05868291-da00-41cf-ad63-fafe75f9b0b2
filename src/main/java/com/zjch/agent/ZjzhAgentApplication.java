package com.zjch.agent;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
@MapperScan("com.zjch.agent.mapper")
public class ZjzhAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZjzhAgentApplication.class, args);
    }

    /**
     * 探活接口
     *
     * @return
     */
    @RequestMapping("/inner/health/alive")
    public String alive() {
        return "ok";
    }
}
