package com.zjch.agent.controller;

import com.zjch.agent.common.Result;
import com.zjch.agent.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.Map;

/**
 * 登录控制器
 */
@Controller
public class LoginController {

    @Autowired
    private LoginService loginService;

    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage() {
        return "login";
    }

    /**
     * 处理登录请求
     */
    @PostMapping("/api/login")
    @ResponseBody
    public Map<String, Object> login(@RequestBody LoginRequest request, HttpSession session) {
        Result result = loginService.login(request.getPhone(), request.getPassword(), session);
        return result.toMap();
    }

    /**
     * 登出
     */
    @PostMapping("/api/logout")
    @ResponseBody
    public Map<String, Object> logout(HttpSession session) {
        Result result = loginService.logout(session);
        return result.toMap();
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/api/check-login")
    @ResponseBody
    public Map<String, Object> checkLogin(HttpSession session) {
        Result result = loginService.checkLogin(session);
        return result.toMap();
    }

    /**
     * 登录请求对象
     */
    public static class LoginRequest {
        private String phone;
        private String password;

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}
