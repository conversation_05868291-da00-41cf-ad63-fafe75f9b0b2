package com.zjch.agent.controller;

import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;
import com.zjch.agent.service.UserManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Controller
public class UserController {

    @Autowired
    private UserManagementService userManagementService;

    /**
     * 用户管理页面
     */
    @GetMapping("/user-management")
    public String userManagementPage(HttpSession session) {
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            return "redirect:/aitool/login";
        }
        return "user-management";
    }

    /**
     * 分页查询用户列表
     */
    @GetMapping("/api/users")
    @ResponseBody
    public Map<String, Object> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String organizationName,
            @RequestParam(required = false) Integer status,
            HttpSession session) {
        
        // 检查管理员权限
        Result permissionCheck = checkAdminPermission(session);
        if (!permissionCheck.isSuccess()) {
            return permissionCheck.toMap();
        }
        
        Result result = userManagementService.getUserList(page, size, phone, organizationName, status);
        return result.toMap();
    }

    /**
     * 创建用户
     */
    @PostMapping("/api/users")
    @ResponseBody
    public Map<String, Object> createUser(@RequestBody User user, HttpSession session) {
        // 检查管理员权限
        Result permissionCheck = checkAdminPermission(session);
        if (!permissionCheck.isSuccess()) {
            return permissionCheck.toMap();
        }
        
        Result result = userManagementService.createUser(user);
        return result.toMap();
    }

    /**
     * 更新用户
     */
    @PutMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> updateUser(@PathVariable Long id, @RequestBody User user, HttpSession session) {
        // 检查管理员权限
        Result permissionCheck = checkAdminPermission(session);
        if (!permissionCheck.isSuccess()) {
            return permissionCheck.toMap();
        }
        
        user.setId(id);
        Result result = userManagementService.updateUser(user);
        return result.toMap();
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> deleteUser(@PathVariable Long id, HttpSession session) {
        // 检查管理员权限并获取当前用户
        User currentUser = getCurrentUser(session);
        if (currentUser == null) {
            return Result.error("无权限访问").toMap();
        }
        
        Result result = userManagementService.deleteUser(id, currentUser);
        return result.toMap();
    }

    /**
     * 启用用户
     */
    @PutMapping("/api/users/{id}/enable")
    @ResponseBody
    public Map<String, Object> enableUser(@PathVariable Long id, HttpSession session) {
        // 检查管理员权限
        Result permissionCheck = checkAdminPermission(session);
        if (!permissionCheck.isSuccess()) {
            return permissionCheck.toMap();
        }
        
        Result result = userManagementService.enableUser(id);
        return result.toMap();
    }

    /**
     * 停用用户
     */
    @PutMapping("/api/users/{id}/disable")
    @ResponseBody
    public Map<String, Object> disableUser(@PathVariable Long id, HttpSession session) {
        // 检查管理员权限并获取当前用户
        User currentUser = getCurrentUser(session);
        if (currentUser == null) {
            return Result.error("无权限访问").toMap();
        }
        
        Result result = userManagementService.disableUser(id, currentUser);
        return result.toMap();
    }
    
    /**
     * 检查管理员权限
     */
    private Result checkAdminPermission(HttpSession session) {
        User currentUser = getCurrentUser(session);
        return currentUser != null ? Result.success() : Result.error("无权限访问");
    }
    
    /**
     * 获取当前用户（管理员）
     */
    private User getCurrentUser(HttpSession session) {
        User user = (User) session.getAttribute("user");
        return (user != null && user.getIsAdmin() == 1) ? user : null;
    }
}
