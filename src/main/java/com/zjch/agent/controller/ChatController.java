package com.zjch.agent.controller;

import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;
import com.zjch.agent.service.ChatService;
import com.zjch.agent.service.ChatHistoryService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.servlet.http.HttpSession;
import java.util.Map;

/**
 * 聊天控制器
 */
@Controller
public class ChatController {

    @Autowired
    private ChatService chatService;
    
    @Autowired
    private ChatHistoryService chatHistoryService;

    @GetMapping("/")
    public String index() {
        return "chat";
    }

    /**
     * 处理聊天请求
     */
    @PostMapping(value = "/api/chat")
    @ResponseBody
    public Map<String, Object> chat(@RequestBody ChatRequest request, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return Result.error("请先登录").toMap();
        }
        
        Result result = chatService.chat(request.getMessage(), request.getSessionId(), user.getId());
        return result.toMap();
    }

    /**
     * 处理流式聊天请求
     */
    @PostMapping(value = "/api/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ResponseBody
    public SseEmitter streamChat(@RequestBody ChatRequest request, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null) {
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send("请先登录");
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }
        
        return chatService.streamChat(request.getMessage(), request.getSessionId(), user.getId());
    }

    /**
     * 获取用户的聊天会话列表
     */
    @GetMapping("/api/chat/sessions")
    @ResponseBody
    public Map<String, Object> getChatSessions(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return Result.error("请先登录").toMap();
        }
        
        try {
            return Result.success(chatHistoryService.getChatSessions(user.getId(), page, size)).toMap();
        } catch (Exception e) {
            return Result.error("获取聊天会话失败: " + e.getMessage()).toMap();
        }
    }
    
    /**
     * 获取指定会话的聊天历史
     */
    @GetMapping("/api/chat/history/{sessionId}")
    @ResponseBody
    public Map<String, Object> getChatHistory(@PathVariable String sessionId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return Result.error("请先登录").toMap();
        }
        
        try {
            return Result.success(chatHistoryService.getChatHistory(user.getId(), sessionId)).toMap();
        } catch (Exception e) {
            return Result.error("获取聊天历史失败: " + e.getMessage()).toMap();
        }
    }
    
    /**
     * 删除聊天会话
     */
    @DeleteMapping("/api/chat/sessions/{sessionId}")
    @ResponseBody
    public Map<String, Object> deleteChatSession(@PathVariable String sessionId, HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return Result.error("请先登录").toMap();
        }
        
        try {
            chatHistoryService.deleteChatSession(user.getId(), sessionId);
            return Result.success("删除成功").toMap();
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage()).toMap();
        }
    }

    /**
     * 聊天请求对象
     */
    @Data
    public static class ChatRequest {
        private String message;
        private String sessionId;
    }
}