package com.zjch.agent.interceptor;

import com.zjch.agent.entity.User;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

/**
 * 权限拦截器
 */
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 检查是否需要登录验证
        if (needAuth(requestURI)) {
            HttpSession session = request.getSession();
            User user = (User) session.getAttribute("user");
            
            if (user == null) {
                // 未登录，重定向到登录页面
                if (isAjaxRequest(request)) {
                    // AJAX请求返回JSON
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"请先登录\",\"needLogin\":true}");
                } else {
                    // 普通请求重定向
                    response.sendRedirect("/login");
                }
                return false;
            }
            
            // 检查管理员权限
            if (needAdminAuth(requestURI) && user.getIsAdmin() != 1) {
                if (isAjaxRequest(request)) {
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"无权限访问\"}");
                } else {
                    response.sendRedirect("/login");
                }
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 判断是否需要登录验证
     */
    private boolean needAuth(String requestURI) {
        // 需要登录的路径
        return requestURI.equals("/") || 
               requestURI.startsWith("/api/chat") ||
               requestURI.startsWith("/user-management") ||
               requestURI.startsWith("/api/users") ||
               requestURI.startsWith("/api/logout");
    }
    
    /**
     * 判断是否需要管理员权限
     */
    private boolean needAdminAuth(String requestURI) {
        // 需要管理员权限的路径
        return requestURI.startsWith("/user-management") ||
               requestURI.startsWith("/api/users");
    }
    
    /**
     * 判断是否为AJAX请求
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String xRequestedWith = request.getHeader("X-Requested-With");
        String contentType = request.getContentType();
        return "XMLHttpRequest".equals(xRequestedWith) || 
               (contentType != null && contentType.contains("application/json"));
    }
}
