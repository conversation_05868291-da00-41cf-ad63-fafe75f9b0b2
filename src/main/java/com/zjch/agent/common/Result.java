package com.zjch.agent.common;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一返回结果类
 */
public class Result {
    
    private boolean success;
    private String message;
    private Object data;
    private Map<String, Object> extra;
    
    private Result() {
        this.extra = new HashMap<>();
    }
    
    public static Result success() {
        Result result = new Result();
        result.success = true;
        return result;
    }
    
    public static Result success(String message) {
        Result result = success();
        result.message = message;
        return result;
    }
    
    public static Result success(Object data) {
        Result result = success();
        result.data = data;
        return result;
    }
    
    public static Result success(String message, Object data) {
        Result result = success();
        result.message = message;
        result.data = data;
        return result;
    }
    
    public static Result error(String message) {
        Result result = new Result();
        result.success = false;
        result.message = message;
        return result;
    }
    
    public static Result error(String message, Object data) {
        Result result = error(message);
        result.data = data;
        return result;
    }
    
    public Result put(String key, Object value) {
        this.extra.put(key, value);
        return this;
    }
    
    /**
     * 转换为Map格式，用于Controller返回
     */
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        
        if (message != null) {
            result.put("message", message);
        }
        
        if (data != null) {
            result.put("data", data);
        }
        
        // 添加额外字段
        result.putAll(extra);
        
        return result;
    }
    
    // Getters
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public Object getData() {
        return data;
    }
    
    public Map<String, Object> getExtra() {
        return extra;
    }
}