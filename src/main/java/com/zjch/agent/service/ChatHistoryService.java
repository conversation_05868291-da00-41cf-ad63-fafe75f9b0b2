package com.zjch.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjch.agent.entity.ChatHistory;
import com.zjch.agent.entity.ChatSession;

import java.util.List;

/**
 * 聊天历史服务接口
 */
public interface ChatHistoryService {
    
    /**
     * 保存聊天记录
     */
    void saveChatHistory(Long userId, String question, String answer, String sessionId);
    
    /**
     * 获取用户的聊天会话列表
     */
    IPage<ChatSession> getChatSessions(Long userId, int page, int size);
    
    /**
     * 获取指定会话的聊天历史
     */
    List<ChatHistory> getChatHistory(Long userId, String sessionId);
    
    /**
     * 创建新的聊天会话
     */
    ChatSession createChatSession(Long userId, String sessionId, String firstMessage);
    
    /**
     * 更新聊天会话
     */
    void updateChatSession(String sessionId, String lastMessage);
    
    /**
     * 删除聊天会话及其历史记录（逻辑删除）
     */
    void deleteChatSession(Long userId, String sessionId);
    
    /**
     * 恢复已删除的聊天会话及其历史记录
     */
    void restoreChatSession(Long userId, String sessionId);
}