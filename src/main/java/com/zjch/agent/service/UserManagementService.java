package com.zjch.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;

/**
 * 用户管理业务接口
 */
public interface UserManagementService {
    
    /**
     * 获取用户分页列表
     */
    Result getUserList(int page, int size, String phone, String organizationName, Integer status);
    
    /**
     * 创建用户
     */
    Result createUser(User user);
    
    /**
     * 更新用户
     */
    Result updateUser(User user);
    
    /**
     * 删除用户
     */
    Result deleteUser(Long id, User currentUser);
    
    /**
     * 启用用户
     */
    Result enableUser(Long id);
    
    /**
     * 停用用户
     */
    Result disableUser(Long id, User currentUser);
}