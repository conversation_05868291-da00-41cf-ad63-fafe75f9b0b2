package com.zjch.agent.service;

import com.zjch.agent.common.Result;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 聊天业务接口
 */
public interface ChatService {
    
    /**
     * 处理聊天请求
     */
    Result chat(String message, String sessionId);
    
    /**
     * 处理聊天请求（带用户ID）
     */
    Result chat(String message, String sessionId, Long userId);
    
    /**
     * 处理流式聊天请求
     */
    SseEmitter streamChat(String message, String sessionId);
    
    /**
     * 处理流式聊天请求（带用户ID）
     */
    SseEmitter streamChat(String message, String sessionId, Long userId);
}