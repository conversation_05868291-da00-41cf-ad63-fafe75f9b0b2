package com.zjch.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjch.agent.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 用户登录
     * @param phone 手机号
     * @param password 密码
     * @return 用户信息，登录失败返回null
     */
    User login(String phone, String password);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);

    /**
     * 分页查询用户列表
     * @param page 分页参数
     * @param phone 手机号（可选）
     * @param organizationName 机构名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    IPage<User> getUserPage(Page<User> page, String phone, String organizationName, Integer status);

    /**
     * 创建用户
     * @param user 用户信息
     * @return 是否成功
     */
    boolean createUser(User user);

    /**
     * 更新用户
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUser(User user);

    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long id);

    /**
     * 检查手机号是否已存在
     * @param phone 手机号
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeId);

    /**
     * 更新用户状态
     * @param id 用户ID
     * @param status 状态：1-启用，0-停用
     * @return 是否成功
     */
    boolean updateUserStatus(Long id, Integer status);
}
