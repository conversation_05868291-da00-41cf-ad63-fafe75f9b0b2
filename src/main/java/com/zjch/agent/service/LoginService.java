package com.zjch.agent.service;

import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;
import jakarta.servlet.http.HttpSession;

/**
 * 登录业务接口
 */
public interface LoginService {
    
    /**
     * 用户登录
     */
    Result login(String phone, String password, HttpSession session);
    
    /**
     * 用户登出
     */
    Result logout(HttpSession session);
    
    /**
     * 检查登录状态
     */
    Result checkLogin(HttpSession session);
    
    /**
     * 验证手机号格式
     */
    boolean isValidPhone(String phone);
}