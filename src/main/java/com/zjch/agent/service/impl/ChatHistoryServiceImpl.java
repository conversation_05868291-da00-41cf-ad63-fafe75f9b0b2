package com.zjch.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjch.agent.entity.ChatHistory;
import com.zjch.agent.entity.ChatSession;
import com.zjch.agent.mapper.ChatHistoryMapper;
import com.zjch.agent.mapper.ChatSessionMapper;
import com.zjch.agent.service.ChatHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天历史服务实现
 */
@Service
public class ChatHistoryServiceImpl implements ChatHistoryService {
    
    @Autowired
    private ChatHistoryMapper chatHistoryMapper;
    
    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Override
    @Transactional
    public void saveChatHistory(Long userId, String question, String answer, String sessionId) {
        // 保存聊天历史记录
        ChatHistory chatHistory = new ChatHistory();
        chatHistory.setUserId(userId);
        chatHistory.setQuestion(question);
        chatHistory.setAnswer(answer);
        chatHistory.setSessionId(sessionId);
        chatHistory.setCreateTime(LocalDateTime.now());
        chatHistory.setUpdateTime(LocalDateTime.now());
        chatHistory.setDeleted(0); // 新记录默认未删除
        
        // 生成标题（取问题的前50个字符）
        String title = question.length() > 50 ? question.substring(0, 50) + "..." : question;
        chatHistory.setTitle(title);
        
        chatHistoryMapper.insert(chatHistory);
        
        // 更新或创建聊天会话
        LambdaQueryWrapper<ChatSession> sessionWrapper = new LambdaQueryWrapper<>();
        sessionWrapper.eq(ChatSession::getUserId, userId)
                     .eq(ChatSession::getSessionId, sessionId)
                     .eq(ChatSession::getDeleted, 0); // 只查询未删除的会话
        ChatSession existingSession = chatSessionMapper.selectOne(sessionWrapper);
        
        if (existingSession == null) {
            // 创建新会话
            createChatSession(userId, sessionId, question);
        } else {
            // 更新现有会话
            updateChatSession(sessionId, question);
        }
    }
    
    @Override
    public IPage<ChatSession> getChatSessions(Long userId, int page, int size) {
        Page<ChatSession> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatSession::getUserId, userId)
                   .eq(ChatSession::getDeleted, 0) // 只查询未删除的会话
                   .orderByDesc(ChatSession::getUpdateTime);
        return chatSessionMapper.selectPage(pageParam, queryWrapper);
    }
    
    @Override
    public List<ChatHistory> getChatHistory(Long userId, String sessionId) {
        LambdaQueryWrapper<ChatHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistory::getUserId, userId)
                   .eq(ChatHistory::getSessionId, sessionId)
                   .eq(ChatHistory::getDeleted, 0) // 只查询未删除的历史记录
                   .orderByAsc(ChatHistory::getCreateTime);
        return chatHistoryMapper.selectList(queryWrapper);
    }
    
    @Override
    @Transactional
    public ChatSession createChatSession(Long userId, String sessionId, String firstMessage) {
        ChatSession chatSession = new ChatSession();
        chatSession.setUserId(userId);
        chatSession.setSessionId(sessionId);
        
        // 生成会话标题（取第一条消息的前30个字符）
        String title = firstMessage.length() > 30 ? firstMessage.substring(0, 30) + "..." : firstMessage;
        chatSession.setTitle(title);
        chatSession.setLastMessage(firstMessage);
        chatSession.setMessageCount(1);
        chatSession.setCreateTime(LocalDateTime.now());
        chatSession.setUpdateTime(LocalDateTime.now());
        chatSession.setDeleted(0); // 新会话默认未删除
        
        chatSessionMapper.insert(chatSession);
        return chatSession;
    }
    
    @Override
    @Transactional
    public void updateChatSession(String sessionId, String lastMessage) {
        LambdaQueryWrapper<ChatSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatSession::getSessionId, sessionId)
                   .eq(ChatSession::getDeleted, 0); // 只更新未删除的会话
        
        ChatSession chatSession = chatSessionMapper.selectOne(queryWrapper);
        if (chatSession != null) {
            chatSession.setLastMessage(lastMessage);
            chatSession.setMessageCount(chatSession.getMessageCount() + 1);
            chatSession.setUpdateTime(LocalDateTime.now());
            chatSessionMapper.updateById(chatSession);
        }
    }
    
    @Override
    @Transactional
    public void deleteChatSession(Long userId, String sessionId) {
        // 逻辑删除聊天历史记录
        LambdaQueryWrapper<ChatHistory> historyQueryWrapper = new LambdaQueryWrapper<>();
        historyQueryWrapper.eq(ChatHistory::getUserId, userId)
                          .eq(ChatHistory::getSessionId, sessionId)
                          .eq(ChatHistory::getDeleted, 0); // 只删除未删除的记录
        
        List<ChatHistory> historyList = chatHistoryMapper.selectList(historyQueryWrapper);
        for (ChatHistory history : historyList) {
            history.setDeleted(1);
            history.setUpdateTime(LocalDateTime.now());
            chatHistoryMapper.updateById(history);
        }
        
        // 逻辑删除聊天会话
        LambdaQueryWrapper<ChatSession> sessionQueryWrapper = new LambdaQueryWrapper<>();
        sessionQueryWrapper.eq(ChatSession::getUserId, userId)
                          .eq(ChatSession::getSessionId, sessionId)
                          .eq(ChatSession::getDeleted, 0); // 只删除未删除的会话
        
        ChatSession chatSession = chatSessionMapper.selectOne(sessionQueryWrapper);
        if (chatSession != null) {
            chatSession.setDeleted(1);
            chatSession.setUpdateTime(LocalDateTime.now());
            chatSessionMapper.updateById(chatSession);
        }
    }
    
    @Override
    @Transactional
    public void restoreChatSession(Long userId, String sessionId) {
        // 恢复聊天历史记录
        LambdaQueryWrapper<ChatHistory> historyQueryWrapper = new LambdaQueryWrapper<>();
        historyQueryWrapper.eq(ChatHistory::getUserId, userId)
                          .eq(ChatHistory::getSessionId, sessionId)
                          .eq(ChatHistory::getDeleted, 1); // 只恢复已删除的记录
        
        List<ChatHistory> historyList = chatHistoryMapper.selectList(historyQueryWrapper);
        for (ChatHistory history : historyList) {
            history.setDeleted(0);
            history.setUpdateTime(LocalDateTime.now());
            chatHistoryMapper.updateById(history);
        }
        
        // 恢复聊天会话
        LambdaQueryWrapper<ChatSession> sessionQueryWrapper = new LambdaQueryWrapper<>();
        sessionQueryWrapper.eq(ChatSession::getUserId, userId)
                          .eq(ChatSession::getSessionId, sessionId)
                          .eq(ChatSession::getDeleted, 1); // 只恢复已删除的会话
        
        ChatSession chatSession = chatSessionMapper.selectOne(sessionQueryWrapper);
        if (chatSession != null) {
            chatSession.setDeleted(0);
            chatSession.setUpdateTime(LocalDateTime.now());
            chatSessionMapper.updateById(chatSession);
        }
    }
}