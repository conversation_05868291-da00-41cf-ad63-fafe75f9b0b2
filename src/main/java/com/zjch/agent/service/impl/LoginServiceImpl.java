package com.zjch.agent.service.impl;

import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;
import com.zjch.agent.service.LoginService;
import com.zjch.agent.service.UserService;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 登录业务实现类
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {

    @Autowired
    private UserService userService;

    @Override
    public Result login(String phone, String password, HttpSession session) {
        try {
            // 验证参数
            if (phone == null || phone.trim().isEmpty()) {
                return Result.error("手机号不能为空");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }
            
            // 验证手机号格式
            if (!isValidPhone(phone)) {
                return Result.error("手机号格式不正确");
            }
            
            // 登录验证
            User user = userService.login(phone, password);
            if (user != null) {
                // 登录成功，保存用户信息到session
                session.setAttribute("user", user);
                return Result.success("登录成功")
                        .put("isAdmin", user.getIsAdmin() == 1);
            } else {
                return Result.error("手机号或密码错误，或账户已被停用");
            }
        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    @Override
    public Result logout(HttpSession session) {
        try {
            session.invalidate();
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("登出失败", e);
            return Result.error("登出失败");
        }
    }

    @Override
    public Result checkLogin(HttpSession session) {
        User user = (User) session.getAttribute("user");
        if (user != null) {
            return Result.success("已登录")
                    .put("loggedIn", true)
                    .put("isAdmin", user.getIsAdmin() == 1)
                    .put("phone", user.getPhone())
                    .put("organizationName", user.getOrganizationName());
        } else {
            return Result.success("未登录")
                    .put("loggedIn", false);
        }
    }

    @Override
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        // 简单的手机号验证：11位数字，以1开头
        return phone.matches("^1[3-9]\\d{9}$");
    }
}