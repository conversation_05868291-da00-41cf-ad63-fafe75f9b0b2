package com.zjch.agent.service.impl;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.zjch.agent.common.Result;
import com.zjch.agent.service.ChatService;
import com.zjch.agent.service.ChatHistoryService;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天业务实现类
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Value("${dashscope.api-key}")
    private String apiKey;

    @Value("${dashscope.app-id}")
    private String appId;
    
    @Autowired
    private ChatHistoryService chatHistoryService;

    @Override
    public Result chat(String message, String sessionId) {
        return chat(message, sessionId, null);
    }
    
    @Override
    public Result chat(String message, String sessionId, Long userId) {
        try {
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt(message)
                    .build();
            
            // 如果传入了sessionId，则设置到参数中以保持会话上下文
            if (sessionId != null && !sessionId.trim().isEmpty()) {
                param.setSessionId(sessionId);
//                param.setPrompt(message);
            }

            Application application = new Application();
            ApplicationResult result = application.call(param);

            String responseMessage = result.getOutput().getText();
            String responseSessionId = result.getOutput().getSessionId();
            
            // 保存聊天历史记录
            if (userId != null) {
                try {
                    chatHistoryService.saveChatHistory(userId, message, responseMessage, responseSessionId);
                } catch (Exception e) {
                    log.error("保存聊天历史失败", e);
                }
            }

            // 返回响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("message", responseMessage);
            responseData.put("sessionId", responseSessionId);
            return Result.success("聊天成功", responseData);
            
        } catch (Exception e) {
            log.error("聊天请求失败", e);
            return Result.error("聊天请求失败: " + e.getMessage());
        }
    }

    @Override
    public SseEmitter streamChat(String message, String sessionId) {
        return streamChat(message, sessionId, null);
    }
    
    @Override
    public SseEmitter streamChat(String message, String sessionId, Long userId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        CompletableFuture.runAsync(() -> {
            try {
                ApplicationParam param = ApplicationParam.builder()
                        .apiKey(apiKey)
                        .appId(appId)
                        .prompt(message)
                        .incrementalOutput(true)
                        .build();
                
                // 如果传入了sessionId，则设置到参数中以保持会话上下文
                if (sessionId != null && !sessionId.trim().isEmpty()) {
                    param.setSessionId(sessionId);
                }
                
                Application application = new Application();
                Flowable<ApplicationResult> result = application.streamCall(param);
                
                StringBuilder fullResponse = new StringBuilder();
                final String[] responseSessionId = {null};
                
                result.blockingForEach(data -> {
                    try {
                        String text = data.getOutput().getText();
                        if (text != null && !text.isEmpty()) {
                            fullResponse.append(text);
                            log.debug("流式响应: {}", text);
                            emitter.send(SseEmitter.event()
                                    .name("message")
                                    .data(text));
                        }
                        
                        // 获取sessionId
                        if (data.getOutput().getSessionId() != null) {
                            responseSessionId[0] = data.getOutput().getSessionId();
                        }
                    } catch (IOException e) {
                        log.error("发送流式数据失败", e);
                        emitter.completeWithError(e);
                        throw new RuntimeException(e);
                    }
                });
                
                // 保存完整的聊天历史记录
                if (userId != null && fullResponse.length() > 0) {
                    try {
                        chatHistoryService.saveChatHistory(userId, message, fullResponse.toString(), 
                                responseSessionId[0] != null ? responseSessionId[0] : sessionId);
                    } catch (Exception e) {
                        log.error("保存聊天历史失败", e);
                    }
                }
                
                try {
                    emitter.send(SseEmitter.event()
                            .name("end")
                            .data(responseSessionId[0] != null ? responseSessionId[0] : sessionId));
                } catch (IOException e) {
                    log.error("发送结束信号失败", e);
                    emitter.completeWithError(e);
                    return;
                }
                emitter.complete();
                
            } catch (NoApiKeyException | InputRequiredException e) {
                log.error("流式聊天请求失败", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("Error: " + e.getMessage()));
                } catch (IOException ioException) {
                    emitter.completeWithError(ioException);
                }
                emitter.complete();
            }
        });
        
        return emitter;
    }
}