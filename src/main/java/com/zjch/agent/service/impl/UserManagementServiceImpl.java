package com.zjch.agent.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjch.agent.common.Result;
import com.zjch.agent.entity.User;
import com.zjch.agent.enums.UserStatus;
import com.zjch.agent.service.UserManagementService;
import com.zjch.agent.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户管理业务实现类
 */
@Slf4j
@Service
public class UserManagementServiceImpl implements UserManagementService {
    
    @Autowired
    private UserService userService;
    
    @Override
    public Result getUserList(int page, int size, String phone, String organizationName, Integer status) {
        try {
            Page<User> pageParam = new Page<>(page, size);
            IPage<User> userPage = userService.getUserPage(pageParam, phone, organizationName, status);
            
            return Result.success(userPage.getRecords())
                    .put("total", userPage.getTotal())
                    .put("current", userPage.getCurrent())
                    .put("size", userPage.getSize())
                    .put("pages", userPage.getPages());
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result createUser(User user) {
        try {
            // 验证必填字段
            if (!StringUtils.hasText(user.getPhone())) {
                return Result.error("手机号不能为空");
            }
            
            if (!StringUtils.hasText(user.getPassword())) {
                return Result.error("密码不能为空");
            }
            
            // 验证手机号格式
            if (!user.getPhone().matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }
            
            // 检查手机号是否已存在
            if (userService.isPhoneExists(user.getPhone(), null)) {
                return Result.error("手机号已存在");
            }
            
            // 设置默认值
            if (user.getStatus() == null) {
                user.setStatus(UserStatus.ENABLED.getCode());
            }
            if (user.getIsAdmin() == null) {
                user.setIsAdmin(0);
            }
            
            boolean success = userService.createUser(user);
            return success ? Result.success("创建成功") : Result.error("创建失败");
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result updateUser(User user) {
        try {
            // 验证必填字段
            if (!StringUtils.hasText(user.getPhone())) {
                return Result.error("手机号不能为空");
            }
            
            // 验证手机号格式
            if (!user.getPhone().matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }
            
            // 检查手机号是否已存在（排除当前用户）
            if (userService.isPhoneExists(user.getPhone(), user.getId())) {
                return Result.error("手机号已存在");
            }
            
            boolean success = userService.updateUser(user);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result deleteUser(Long id, User currentUser) {
        try {
            // 不能删除自己
            if (currentUser.getId().equals(id)) {
                return Result.error("不能删除自己");
            }
            
            boolean success = userService.deleteUser(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result enableUser(Long id) {
        try {
            boolean success = userService.updateUserStatus(id, UserStatus.ENABLED.getCode());
            if (success) {
                return Result.success("用户启用成功");
            } else {
                return Result.error("用户启用失败，可能用户不存在");
            }
        } catch (Exception e) {
            log.error("启用用户失败", e);
            return Result.error("用户启用失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result disableUser(Long id, User currentUser) {
        try {
            // 不能停用自己
            if (currentUser.getId().equals(id)) {
                return Result.error("不能停用自己");
            }
            
            boolean success = userService.updateUserStatus(id, UserStatus.DISABLED.getCode());
            if (success) {
                return Result.success("用户停用成功");
            } else {
                return Result.error("用户停用失败，可能用户不存在");
            }
        } catch (Exception e) {
            log.error("停用用户失败", e);
            return Result.error("用户停用失败：" + e.getMessage());
        }
    }
}