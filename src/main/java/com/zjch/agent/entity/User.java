package com.zjch.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.zjch.agent.enums.UserStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user")
public class User {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 机构名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 状态：1-启用，0-停用
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否为管理员：1-是，0-否
     */
    @TableField("is_admin")
    private Integer isAdmin;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：1-已删除，0-未删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
    
    /**
     * 判断用户是否启用
     */
    public boolean isEnabled() {
        return UserStatus.isEnabled(this.status);
    }
    
    /**
     * 判断用户是否停用
     */
    public boolean isDisabled() {
        return UserStatus.isDisabled(this.status);
    }
    
    /**
     * 启用用户
     */
    public void enable() {
        this.status = UserStatus.ENABLED.getCode();
    }
    
    /**
     * 停用用户
     */
    public void disable() {
        this.status = UserStatus.DISABLED.getCode();
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        UserStatus userStatus = UserStatus.getByCode(this.status);
        return userStatus != null ? userStatus.getDesc() : "未知";
    }
}
