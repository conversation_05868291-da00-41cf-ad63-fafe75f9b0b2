package com.zjch.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天历史记录实体类
 */
@Data
@TableName("chat_history")
public class ChatHistory {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户输入的问题
     */
    private String question;
    
    /**
     * AI返回的答案
     */
    private String answer;
    
    /**
     * 阿里云DashScope返回的sessionId
     */
    private String sessionId;
    
    /**
     * 聊天会话标题（可选，用于显示）
     */
    private String title;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}