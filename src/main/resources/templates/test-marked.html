<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marked.js 本地加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .markdown-output {
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
    <script src="/js/marked.min.js"></script>
</head>
<body>
    <h1>Marked.js 本地加载测试</h1>
    
    <div id="testResult" class="test-result">正在测试...</div>
    
    <h2>测试 Markdown 渲染：</h2>
    <div id="markdownTest">
        <h3>原始 Markdown 文本：</h3>
        <pre id="markdownSource"># 这是标题
## 这是二级标题
这是**粗体文字**，这是*斜体文字*。

- 列表项1
- 列表项2
- 列表项3

```javascript
console.log("Hello World!");
```

[这是链接](https://example.com)</pre>
        
        <h3>渲染结果：</h3>
        <div id="markdownOutput" class="markdown-output">等待渲染...</div>
    </div>

    <script>
        function testMarked() {
            const resultDiv = document.getElementById('testResult');
            const outputDiv = document.getElementById('markdownOutput');
            const sourceText = document.getElementById('markdownSource').textContent;
            
            try {
                // 检查 marked 是否已加载
                if (typeof marked === 'undefined') {
                    throw new Error('marked 库未加载');
                }
                
                // 测试渲染功能
                const rendered = marked.parse(sourceText);
                
                // 显示成功结果
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✅ marked.js 本地加载成功！版本: ' + (marked.version || '未知');
                
                // 显示渲染结果
                outputDiv.innerHTML = rendered;
                
            } catch (error) {
                // 显示错误结果
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ marked.js 加载失败: ' + error.message;
                
                outputDiv.innerHTML = '渲染失败：' + error.message;
            }
        }
        
        // 页面加载完成后测试
        document.addEventListener('DOMContentLoaded', function() {
            // 稍微延迟一下确保 marked.js 完全加载
            setTimeout(testMarked, 100);
        });
    </script>
</body>
</html>