<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .header-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .chat-title {
            flex: 1;
            text-align: center;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .user-menu {
            position: relative;
        }

        .user-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .user-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            min-width: 150px;
            z-index: 1000;
            display: none;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: #333;
            text-decoration: none;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message-content {
            display: inline-block;
            max-width: 80%;
            padding: 12px 25px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .typing-indicator {
            display: none;
            text-align: left;
            margin-bottom: 20px;
        }

        .typing-indicator .message-content {
            background: white;
            border: 1px solid #e1e8ed;
            padding: 12px 16px;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e8ed;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 22px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            line-height: 1.4;
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .chat-header {
                padding: 12px 15px;
                font-size: 16px;
            }

            .chat-messages {
                padding: 15px;
            }

            .message-content {
                max-width: 85%;
                font-size: 16px;
            }

            .chat-input-container {
                padding: 15px;
            }

            .chat-input {
                font-size: 16px;
                min-height: 40px;
            }

            .send-button {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        @media screen and (max-width: 768px) {
            .chat-input {
                font-size: 16px !important;
            }
        }

        .chat-messages::-webkit-scrollbar {
            width: 4px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.4);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .history-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .history-item:hover {
            border-color: #4facfe;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.2);
            transform: translateY(-1px);
        }

        .history-item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .history-item-preview {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .history-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #999;
        }

        .history-item-actions {
            display: flex;
            gap: 10px;
        }

        .history-action-btn {
            background: #4facfe;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            transition: background 0.2s ease;
        }

        .history-action-btn:hover {
            background: #3a8bfe;
        }

        .history-action-btn.delete {
            background: #ff4757;
        }

        .history-action-btn.delete:hover {
            background: #ff3742;
        }

        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }

        .empty-history {
            text-align: center;
            color: #999;
            padding: 40px 20px;
        }

        .empty-history-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
    </style>
    <script src="/aitool/js/marked.min.js"></script>
</head>
<body>
<div class="chat-container">
    <div class="chat-header">
        <div class="header-left">
            <button class="header-button" id="historyButton" onclick="showChatHistory()">
                📋 历史聊天
            </button>
            <button class="header-button" id="newChatButton" onclick="startNewChat()">
                ➕ 新建聊天
            </button>
        </div>
        <div class="chat-title">🤖 智能对话助手</div>
        <div class="user-info">
            <div class="user-menu">
                <button class="user-button" id="userButton">
                    <span id="userPhone">加载中...</span> ▼
                </button>
                <div class="dropdown-menu" id="dropdownMenu">
                    <a href="/aitool/user-management" class="dropdown-item" id="userManagementLink" style="display: none;">👥 用户管理</a>
                    <a href="#" class="dropdown-item" onclick="logout()">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="chat-messages" id="chatMessages">
        <div class="message assistant">
            <div class="message-content">
                你好！我是你的智能助手，有什么可以帮助你的吗？
            </div>
        </div>
    </div>

    <div class="typing-indicator" id="typingIndicator">
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </div>

    <div class="chat-input-container">
        <form class="chat-input-form" id="chatForm">
            <textarea
                    class="chat-input"
                    id="chatInput"
                    placeholder="输入你的问题..."
                    rows="1"
            ></textarea>
            <button type="submit" class="send-button" id="sendButton">
                ➤
            </button>
        </form>
    </div>
</div>

<!-- 历史聊天模态框 -->
<div id="historyModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📋 历史聊天</h3>
            <span class="close" onclick="closeHistoryModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="historyList" class="history-list">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>
</div>

<script>
    // API路径前缀配置
    const API_BASE = '/aitool';
    function apiUrl(path) {
        return API_BASE + path;
    }

    // 全局变量
    let currentSessionId = null;
    let chatMessages = [];

    // 页面加载时恢复聊天记录
    window.addEventListener('load', function() {
        restoreChatFromStorage();
    });

    // 页面卸载时保存聊天记录
    window.addEventListener('beforeunload', function() {
        saveChatToStorage();
    });

    // 保存聊天记录到localStorage
    function saveChatToStorage() {
        if (chatMessages.length > 0) {
            const chatData = {
                sessionId: currentSessionId,
                messages: chatMessages,
                timestamp: Date.now()
            };
            localStorage.setItem('currentChat', JSON.stringify(chatData));
        }
    }

    // 从localStorage恢复聊天记录
    function restoreChatFromStorage() {
        const savedChat = localStorage.getItem('currentChat');
        if (savedChat) {
            try {
                const chatData = JSON.parse(savedChat);
                currentSessionId = chatData.sessionId;
                chatMessages = chatData.messages || [];
                
                // 恢复聊天界面
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                
                chatMessages.forEach(msg => {
                    addMessageToChat(msg.type, msg.content, false);
                });
                
                // 滚动到底部
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            } catch (e) {
                console.error('恢复聊天记录失败:', e);
            }
        }
    }

    // 历史聊天功能
    function showChatHistory() {
        document.getElementById('historyModal').style.display = 'block';
        loadChatHistory();
    }

    function closeHistoryModal() {
        document.getElementById('historyModal').style.display = 'none';
    }

    // 加载聊天历史
    async function loadChatHistory() {
        const historyList = document.getElementById('historyList');
        historyList.innerHTML = '<div class="loading">加载中...</div>';
        
        try {
            const response = await fetch(apiUrl('/api/chat/sessions?page=1&size=20'));
            const result = await response.json();
            
            if (result.success && result.data.records && result.data.records.length > 0) {
                historyList.innerHTML = '';
                result.data.records.forEach(session => {
                    const historyItem = createHistoryItem(session);
                    historyList.appendChild(historyItem);
                });
            } else {
                historyList.innerHTML = `
                    <div class="empty-history">
                        <div class="empty-history-icon">💬</div>
                        <div>暂无聊天历史</div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('加载聊天历史失败:', error);
            historyList.innerHTML = '<div class="loading">加载失败，请重试</div>';
        }
    }

    // 创建历史记录项
    function createHistoryItem(session) {
        const item = document.createElement('div');
        item.className = 'history-item';
        
        const createTime = new Date(session.createTime).toLocaleString();
        const updateTime = new Date(session.updateTime).toLocaleString();
        
        item.innerHTML = `
            <div class="history-item-title">${session.title || '未命名对话'}</div>
            <div class="history-item-preview">${session.lastMessage || '暂无消息'}</div>
            <div class="history-item-meta">
                <span>消息数: ${session.messageCount || 0} | 更新: ${updateTime}</span>
                <div class="history-item-actions">
                    <button class="history-action-btn" onclick="continueChat('${session.sessionId}')">继续聊天</button>
                    <button class="history-action-btn delete" onclick="deleteChatSession('${session.sessionId}')">删除</button>
                </div>
            </div>
        `;
        
        return item;
    }

    // 继续聊天
    async function continueChat(sessionId) {
        try {
            // 获取聊天历史
            const response = await fetch(apiUrl(`/api/chat/history/${sessionId}`));
            const result = await response.json();
            
            if (result.success && result.data) {
                // 清空当前聊天
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                chatMessages = [];
                
                // 恢复历史消息
                result.data.forEach(history => {
                    addMessageToChat('user', history.question, false);
                    addMessageToChat('assistant', history.answer, false);
                    
                    chatMessages.push({type: 'user', content: history.question});
                    chatMessages.push({type: 'assistant', content: history.answer});
                });
                
                // 设置当前sessionId
                currentSessionId = sessionId;
                
                // 滚动到底部
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                // 关闭模态框
                closeHistoryModal();
                
                // 保存到localStorage
                saveChatToStorage();
            }
        } catch (error) {
            console.error('继续聊天失败:', error);
            alert('继续聊天失败，请重试');
        }
    }

    // 删除聊天会话
    async function deleteChatSession(sessionId) {
        if (!confirm('确定要删除这个聊天会话吗？此操作不可恢复。')) {
            return;
        }
        
        try {
            const response = await fetch(apiUrl(`/api/chat/sessions/${sessionId}`), {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                // 重新加载历史列表
                loadChatHistory();
            } else {
                alert('删除失败: ' + result.message);
            }
        } catch (error) {
            console.error('删除聊天会话失败:', error);
            alert('删除失败，请重试');
        }
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const modal = document.getElementById('historyModal');
        if (event.target === modal) {
            closeHistoryModal();
        }
    }

    // 新建聊天
    function startNewChat() {
        if (chatMessages.length > 0 && !confirm('确定要开始新的聊天吗？当前聊天记录将被保存。')) {
            return;
        }
        
        // 保存当前聊天
        saveChatToStorage();
        
        // 清空聊天界面
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.innerHTML = `
            <div class="message assistant">
                <div class="message-content">
                    你好！我是你的智能助手，有什么可以帮助你的吗？
                </div>
            </div>
        `;
        
        // 重置状态
        currentSessionId = null;
        chatMessages = [];
        
        // 清除localStorage中的当前聊天
        localStorage.removeItem('currentChat');
        
        // 聚焦输入框
        document.getElementById('messageInput').focus();
    }

    // 添加消息到聊天界面
    function addMessageToChat(type, content, saveToHistory = true) {
        const messagesContainer = document.getElementById('chatMessages');
        const div = document.createElement('div');
        div.className = `message ${type}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        if (type === 'assistant') {
            contentDiv.innerHTML = marked.parse(content);
        } else {
            contentDiv.textContent = content;
        }
        
        div.appendChild(contentDiv);
        messagesContainer.appendChild(div);
        
        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // 保存到消息历史
        if (saveToHistory) {
            chatMessages.push({type, content});
            saveChatToStorage();
        }
        
        return contentDiv;
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    class ChatApp {
        constructor() {
            this.chatMessages = document.getElementById('chatMessages');
            this.chatForm = document.getElementById('chatForm');
            this.chatInput = document.getElementById('chatInput');
            this.sendButton = document.getElementById('sendButton');
            this.typingIndicator = document.getElementById('typingIndicator');
            this.isStreaming = false;
            this.currentAssistantMessage = null;

            this.init();
        }

        init() {
            this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
            this.chatInput.addEventListener('input', () => this.autoResize());
            this.chatInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
            this.checkLoginStatus();
            this.initUserMenu();
            this.chatInput.focus();
        }

        async checkLoginStatus() {
            try {
                const response = await fetch(apiUrl('/api/check-login'));
                if (!response.ok) throw new Error(`HTTP 错误！状态码: ${response.status}`);
                const result = await response.json();
                if (result.loggedIn) {
                    document.getElementById('userPhone').textContent = result.phone;
                    if (result.isAdmin) {
                        document.getElementById('userManagementLink').style.display = 'block';
                    }
                } else {
                    window.location.href = '/aitool/login';
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                window.location.href = '/aitool/login';
            }
        }

        initUserMenu() {
            const userButton = document.getElementById('userButton');
            const dropdownMenu = document.getElementById('dropdownMenu');
            userButton.addEventListener('click', (e) => {
                e.stopPropagation();
                dropdownMenu.classList.toggle('show');
            });
            document.addEventListener('click', () => dropdownMenu.classList.remove('show'));
            dropdownMenu.addEventListener('click', (e) => e.stopPropagation());
        }

        handleKeyDown(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!this.isStreaming && this.chatInput.value.trim()) {
                    this.handleSubmit(e);
                }
            }
        }

        autoResize() {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
        }

        async handleSubmit(e) {
            e.preventDefault();
            const message = this.chatInput.value.trim();
            if (!message || this.isStreaming) return;

            // 使用全局的addMessageToChat函数
            addMessageToChat('user', message);
            this.chatInput.value = '';
            this.autoResize();
            this.setStreaming(true);
            this.showTypingIndicator();

            try {
                await this.sendMessage(message);
            } catch (error) {
                console.error('发送消息错误:', error);
                addMessageToChat('assistant', '抱歉，发生了错误，请稍后重试。');
            } finally {
                this.setStreaming(false);
                this.hideTypingIndicator();
            }
        }

        addMessage(content, sender) {
            const div = document.createElement('div');
            div.className = `message ${sender}`;
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            if (sender === 'assistant') {
                contentDiv.innerHTML = marked.parse(content);
            } else {
                contentDiv.textContent = content;
            }
            div.appendChild(contentDiv);
            this.chatMessages.appendChild(div);
            this.scrollToBottom();
            return contentDiv;
        }

        showTypingIndicator() {
            this.typingIndicator.style.display = 'block';
            this.scrollToBottom();
        }

        hideTypingIndicator() {
            this.typingIndicator.style.display = 'none';
        }

        async sendMessage(message) {
            this.hideTypingIndicator();
            this.currentAssistantMessage = addMessageToChat('assistant', '正在思考中请稍等...', false);

            const payload = { message };
            if (currentSessionId) {
                payload.sessionId = currentSessionId;
            }

            const response = await fetch(apiUrl('/api/chat'), {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP 错误！状态码: ${response.status}`);
            }

            const result = await response.json();

            if (result.data && result.data.sessionId) {
                currentSessionId = result.data.sessionId;
            }

            if (result.success) {
                // 替换加载消息为实际响应，支持 Markdown 渲染
                const responseMessage = result.data.message.replace(/\n+/g, '\n');
                this.currentAssistantMessage.innerHTML = marked.parse(responseMessage);
                
                // 更新消息历史（替换临时消息）
                if (chatMessages.length > 0 && chatMessages[chatMessages.length - 1].content === '正在思考中请稍等...') {
                    chatMessages[chatMessages.length - 1] = {type: 'assistant', content: responseMessage};
                } else {
                    chatMessages.push({type: 'assistant', content: responseMessage});
                }
                
                // 保存到localStorage
                saveChatToStorage();
            } else {
                // 显示错误消息
                const errorMessage = result.message || '抱歉，发生了错误，请稍后重试。';
                this.currentAssistantMessage.textContent = errorMessage;
                
                // 更新消息历史
                if (chatMessages.length > 0 && chatMessages[chatMessages.length - 1].content === '正在思考中请稍等...') {
                    chatMessages[chatMessages.length - 1] = {type: 'assistant', content: errorMessage};
                } else {
                    chatMessages.push({type: 'assistant', content: errorMessage});
                }
                
                saveChatToStorage();
            }

            this.scrollToBottom();
        }


        setStreaming(streaming) {
            this.isStreaming = streaming;
            this.sendButton.disabled = streaming;
            this.chatInput.disabled = streaming;
            this.sendButton.innerHTML = streaming ? '⏸' : '➤';
            if (!streaming) {
                this.chatInput.focus();
            }
        }

        scrollToBottom = debounce(() => {
            this.chatMessages.scrollTo({
                top: this.chatMessages.scrollHeight,
                behavior: 'smooth'
            });
        }, 50);
    }

    async function logout() {
        if (!confirm('确定要退出登录吗？')) return;
        try {
            const response = await fetch(apiUrl('/api/logout'), { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                window.location.href = '/aitool/login';
            } else {
                alert('退出失败');
            }
        } catch (error) {
            console.error('退出登录错误:', error);
            alert('网络错误');
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        new ChatApp();
    });

    document.addEventListener('touchmove', (e) => {
        if (e.target.closest('.chat-messages')) return;
        e.preventDefault();
    }, { passive: false });
</script>
</body>
</html>