<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>用户管理 - 智能对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 20px 30px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            border: 1px solid #e1e5e9;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .admin-badge {
            background: #fff3cd;
            color: #856404;
        }

        .user-badge {
            background: #d1ecf1;
            color: #0c5460;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e5e9;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .search-bar {
                flex-direction: column;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .table-container {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="header-title">👥 用户管理</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="showCreateModal()">➕ 新增用户</button>
                <a href="/aitool/" class="btn btn-secondary">🏠 返回首页</a>
                <button class="btn btn-danger" onclick="logout()">🚪 退出登录</button>
            </div>
        </div>

        <div class="content">
            <div class="search-bar">
                <input type="text" class="search-input" id="searchPhone" placeholder="搜索手机号...">
                <input type="text" class="search-input" id="searchOrg" placeholder="搜索机构名称...">
                <select class="search-input" id="searchStatus">
                    <option value="">全部状态</option>
                    <option value="1">启用</option>
                    <option value="0">停用</option>
                </select>
                <button class="btn btn-primary" onclick="searchUsers()">🔍 搜索</button>
                <button class="btn btn-secondary" onclick="resetSearch()">🔄 重置</button>
            </div>

            <div id="alertContainer"></div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>手机号</th>
                            <th>机构名称</th>
                            <th>状态</th>
                            <th>角色</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <tr>
                            <td colspan="7" class="loading">
                                <div class="loading-spinner"></div>
                                <span style="margin-left: 10px;">加载中...</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination" id="pagination"></div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增用户</h3>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalAlertContainer"></div>
            <form id="userForm">
                <input type="hidden" id="userId">
                <div class="form-group">
                    <label class="form-label">手机号 *</label>
                    <input type="tel" class="form-control" id="userPhone" maxlength="11" required>
                </div>
                <div class="form-group" id="passwordGroup">
                    <label class="form-label">密码 *</label>
                    <input type="password" class="form-control" id="userPassword">
                </div>
                <div class="form-group">
                    <label class="form-label">机构名称</label>
                    <input type="text" class="form-control" id="userOrg">
                </div>
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="userStatus">
                        <option value="1">启用</option>
                        <option value="0">停用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">角色</label>
                    <select class="form-select" id="userRole">
                        <option value="0">普通用户</option>
                        <option value="1">管理员</option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // API路径前缀配置
        const API_BASE = '/aitool';
        function apiUrl(path) {
            return API_BASE + path;
        }

        class UserManagement {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 10;
                this.isEditing = false;
                
                this.init();
            }
            
            init() {
                this.loadUsers();
                this.bindEvents();
            }
            
            bindEvents() {
                document.getElementById('userForm').addEventListener('submit', (e) => this.handleSubmit(e));
                
                // 搜索框回车事件
                ['searchPhone', 'searchOrg'].forEach(id => {
                    document.getElementById(id).addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.searchUsers();
                        }
                    });
                });
            }
            
            async loadUsers(page = 1) {
                this.currentPage = page;
                const phone = document.getElementById('searchPhone').value;
                const organizationName = document.getElementById('searchOrg').value;
                const status = document.getElementById('searchStatus').value;
                
                const params = new URLSearchParams({
                    page: page,
                    size: this.pageSize
                });
                
                if (phone) params.append('phone', phone);
                if (organizationName) params.append('organizationName', organizationName);
                if (status) params.append('status', status);
                
                try {
                    const response = await fetch(apiUrl(`/api/users?${params}`));
                    const result = await response.json();
                    
                    if (result.success) {
                        this.renderUsers(result.data);
                        this.renderPagination(result);
                    } else {
                        this.showAlert(result.message || '加载失败', 'danger');
                    }
                } catch (error) {
                    console.error('Load users error:', error);
                    this.showAlert('网络错误，请稍后重试', 'danger');
                }
            }
            
            renderUsers(users) {
                const tbody = document.getElementById('userTableBody');
                
                if (users.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">暂无数据</td></tr>';
                    return;
                }
                
                tbody.innerHTML = users.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.phone}</td>
                        <td>${user.organizationName || '-'}</td>
                        <td>
                            <span class="status-badge ${user.status === 1 ? 'status-active' : 'status-inactive'}">
                                ${user.status === 1 ? '✅ 启用' : '❌ 停用'}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge ${user.isAdmin === 1 ? 'admin-badge' : 'user-badge'}">
                                ${user.isAdmin === 1 ? '管理员' : '普通用户'}
                            </span>
                        </td>
                        <td>${this.formatDate(user.createTime)}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px; margin-right: 5px;" onclick="userManagement.editUser(${user.id})">编辑</button>
                            ${user.status === 1 ? 
                                `<button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px; margin-right: 5px;" onclick="userManagement.disableUser(${user.id})">停用</button>` :
                                `<button class="btn btn-info" style="padding: 5px 10px; font-size: 12px; margin-right: 5px;" onclick="userManagement.enableUser(${user.id})">启用</button>`
                            }
                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="userManagement.deleteUser(${user.id})">删除</button>
                        </td>
                    </tr>
                `).join('');
            }
            
            renderPagination(result) {
                const pagination = document.getElementById('pagination');
                const { current, pages, total } = result;
                
                if (pages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }
                
                let html = `
                    <button ${current <= 1 ? 'disabled' : ''} onclick="userManagement.loadUsers(${current - 1})">上一页</button>
                `;
                
                for (let i = 1; i <= pages; i++) {
                    if (i === current) {
                        html += `<button class="active">${i}</button>`;
                    } else if (i === 1 || i === pages || Math.abs(i - current) <= 2) {
                        html += `<button onclick="userManagement.loadUsers(${i})">${i}</button>`;
                    } else if (i === current - 3 || i === current + 3) {
                        html += `<span>...</span>`;
                    }
                }
                
                html += `
                    <button ${current >= pages ? 'disabled' : ''} onclick="userManagement.loadUsers(${current + 1})">下一页</button>
                    <span style="margin-left: 15px;">共 ${total} 条</span>
                `;
                
                pagination.innerHTML = html;
            }
            
            searchUsers() {
                this.loadUsers(1);
            }
            
            resetSearch() {
                document.getElementById('searchPhone').value = '';
                document.getElementById('searchOrg').value = '';
                document.getElementById('searchStatus').value = '';
                this.loadUsers(1);
            }
            
            showCreateModal() {
                this.isEditing = false;
                document.getElementById('modalTitle').textContent = '新增用户';
                document.getElementById('userForm').reset();
                document.getElementById('userId').value = '';
                document.getElementById('passwordGroup').style.display = 'block';
                document.getElementById('userPassword').required = true;
                document.getElementById('modalAlertContainer').innerHTML = '';
                document.getElementById('userModal').style.display = 'block';
            }
            
            async editUser(id) {
                this.isEditing = true;
                document.getElementById('modalTitle').textContent = '编辑用户';
                document.getElementById('passwordGroup').style.display = 'none';
                document.getElementById('userPassword').required = false;
                document.getElementById('modalAlertContainer').innerHTML = '';
                
                try {
                    // 从表格中获取用户数据
                    const users = Array.from(document.querySelectorAll('#userTableBody tr')).map(row => {
                        const cells = row.querySelectorAll('td');
                        if (cells.length > 1) {
                            return {
                                id: parseInt(cells[0].textContent),
                                phone: cells[1].textContent,
                                organizationName: cells[2].textContent === '-' ? '' : cells[2].textContent,
                                status: cells[3].textContent.includes('启用') ? 1 : 0,
                                isAdmin: cells[4].textContent.includes('管理员') ? 1 : 0
                            };
                        }
                        return null;
                    }).filter(user => user && user.id === id);
                    
                    if (users.length > 0) {
                        const user = users[0];
                        document.getElementById('userId').value = user.id;
                        document.getElementById('userPhone').value = user.phone;
                        document.getElementById('userOrg').value = user.organizationName;
                        document.getElementById('userStatus').value = user.status;
                        document.getElementById('userRole').value = user.isAdmin;
                        document.getElementById('userModal').style.display = 'block';
                    }
                } catch (error) {
                    console.error('Edit user error:', error);
                    this.showAlert('获取用户信息失败', 'danger');
                }
            }
            
            async enableUser(id) {
                if (!confirm('确定要启用这个用户吗？')) {
                    return;
                }
                
                try {
                    const response = await fetch(apiUrl(`/api/users/${id}/enable`), {
                        method: 'PUT'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert('用户启用成功', 'success');
                        this.loadUsers(this.currentPage);
                    } else {
                        this.showAlert(result.message || '启用失败', 'danger');
                    }
                } catch (error) {
                    console.error('Enable user error:', error);
                    this.showAlert('网络错误，请稍后重试', 'danger');
                }
            }
            
            async disableUser(id) {
                if (!confirm('确定要停用这个用户吗？停用后该用户将无法登录系统。')) {
                    return;
                }
                
                try {
                    const response = await fetch(apiUrl(`/api/users/${id}/disable`), {
                        method: 'PUT'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert('用户停用成功', 'success');
                        this.loadUsers(this.currentPage);
                    } else {
                        this.showAlert(result.message || '停用失败', 'danger');
                    }
                } catch (error) {
                    console.error('Disable user error:', error);
                    this.showAlert('网络错误，请稍后重试', 'danger');
                }
            }

            async deleteUser(id) {
                if (!confirm('确定要删除这个用户吗？')) {
                    return;
                }
                
                try {
                    const response = await fetch(apiUrl(`/api/users/${id}`), {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert('删除成功', 'success');
                        this.loadUsers(this.currentPage);
                    } else {
                        this.showAlert(result.message || '删除失败', 'danger');
                    }
                } catch (error) {
                    console.error('Delete user error:', error);
                    this.showAlert('网络错误，请稍后重试', 'danger');
                }
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const formData = {
                    phone: document.getElementById('userPhone').value.trim(),
                    organizationName: document.getElementById('userOrg').value.trim(),
                    status: parseInt(document.getElementById('userStatus').value),
                    isAdmin: parseInt(document.getElementById('userRole').value)
                };
                
                if (!this.isEditing) {
                    formData.password = document.getElementById('userPassword').value.trim();
                }
                
                // 验证手机号
                if (!formData.phone.match(/^1[3-9]\d{9}$/)) {
                    this.showModalAlert('请输入正确的手机号', 'danger');
                    return;
                }
                
                try {
                    let url = apiUrl('/api/users');
                    let method = 'POST';
                    
                    if (this.isEditing) {
                        const userId = document.getElementById('userId').value;
                        url = apiUrl(`/api/users/${userId}`);
                        method = 'PUT';
                    }
                    
                    const response = await fetch(url, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert(this.isEditing ? '更新成功' : '创建成功', 'success');
                        this.closeModal();
                        this.loadUsers(this.currentPage);
                    } else {
                        this.showModalAlert(result.message || '操作失败', 'danger');
                    }
                } catch (error) {
                    console.error('Submit error:', error);
                    this.showModalAlert('网络错误，请稍后重试', 'danger');
                }
            }
            
            closeModal() {
                document.getElementById('userModal').style.display = 'none';
            }
            
            showAlert(message, type) {
                const container = document.getElementById('alertContainer');
                container.innerHTML = `
                    <div class="alert alert-${type}">
                        ${message}
                    </div>
                `;
                
                setTimeout(() => {
                    container.innerHTML = '';
                }, 3000);
            }
            
            showModalAlert(message, type) {
                const container = document.getElementById('modalAlertContainer');
                container.innerHTML = `
                    <div class="alert alert-${type}">
                        ${message}
                    </div>
                `;
            }
            
            formatDate(dateString) {
                if (!dateString) return '-';
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }
        }
        
        // 全局函数
        let userManagement;
        
        function showCreateModal() {
            userManagement.showCreateModal();
        }
        
        function closeModal() {
            userManagement.closeModal();
        }
        
        function searchUsers() {
            userManagement.searchUsers();
        }
        
        function resetSearch() {
            userManagement.resetSearch();
        }
        
        async function logout() {
            if (!confirm('确定要退出登录吗？')) {
                return;
            }
            
            try {
                const response = await fetch(apiUrl('/api/logout'), {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = '/aitool/login';
                } else {
                    alert('退出失败');
                }
            } catch (error) {
                console.error('Logout error:', error);
                alert('网络错误');
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            userManagement = new UserManagement();
        });
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
