<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .chat-title {
            flex: 1;
            text-align: center;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message-content {
            display: inline-block;
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.6;
            word-wrap: break-word;
            position: relative;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e8ed;
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 22px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.4);
        }
    </style>
    <!-- 引入 marked.js 用于 Markdown 渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
<div class="chat-container">
    <div class="chat-header">
        <div class="chat-title">🤖 智能对话助手</div>
    </div>
    <div class="chat-messages" id="chatMessages">
        <div class="message assistant">
            <div class="message-content">你好！我是你的智能助手，有什么可以帮助你的吗？</div>
        </div>
    </div>
    <div class="chat-input-container">
        <form class="chat-input-form" id="chatForm">
            <textarea class="chat-input" id="chatInput" placeholder="输入你的问题..." rows="1"></textarea>
            <button type="submit" class="send-button" id="sendButton">➤</button>
        </form>
    </div>
</div>

<script>
    // API路径前缀配置
    const API_BASE = '/aitool';
    function apiUrl(path) {
        return API_BASE + path;
    }

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    class ChatApp {
        constructor() {
            this.chatMessages = document.getElementById('chatMessages');
            this.chatForm = document.getElementById('chatForm');
            this.chatInput = document.getElementById('chatInput');
            this.sendButton = document.getElementById('sendButton');
            this.isStreaming = false;
            this.currentAssistantMessage = null;
            this.messageBuffer = '';
            this.debounceUpdateMessage = debounce(this.updateCurrentMessage.bind(this), 50);
            this.init();
        }

        init() {
            this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
            this.chatInput.addEventListener('input', () => this.autoResize());
            this.chatInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        }

        handleKeyDown(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!this.isStreaming && this.chatInput.value.trim()) {
                    this.handleSubmit(e);
                }
            }
        }

        autoResize() {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
        }

        async handleSubmit(e) {
            e.preventDefault();
            const message = this.chatInput.value.trim();
            if (!message || this.isStreaming) return;
            this.addMessage(message, 'user');
            this.chatInput.value = '';
            this.autoResize();
            this.setStreaming(true);
            try {
                await this.streamResponse(message);
            } catch (err) {
                console.error(err);
                this.addMessage('抱歉，发生了错误，请稍后重试。', 'assistant');
            } finally {
                this.setStreaming(false);
                if (this.messageBuffer) {
                    this.updateCurrentMessage();
                }
            }
        }

        addMessage(content, sender) {
            const div = document.createElement('div');
            div.className = `message ${sender}`;
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            if (sender === 'assistant') {
                contentDiv.innerHTML = marked.parse(content);
            } else {
                contentDiv.textContent = content;
            }
            div.appendChild(contentDiv);
            this.chatMessages.appendChild(div);
            this.scrollToBottom();
            return contentDiv;
        }

        async streamResponse(message) {
            this.currentAssistantMessage = this.addMessage('', 'assistant');
            this.messageBuffer = '';
            const response = await fetch(apiUrl('/api/chat/stream'), {
                method: 'POST', headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message})
            });
            if (!response.ok) throw new Error(`HTTP 错误: ${response.status}`);
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let currentEvent = '', currentData = '';
            try {
                while (true) {
                    const {done, value} = await reader.read();
                    if (done) break;
                    buffer += decoder.decode(value, {stream: true});
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    for (const line of lines) {
                        const {newEvent, newData} = this.processLine(line, currentEvent, currentData);
                        currentEvent = newEvent;
                        currentData = newData;
                    }
                }
                if (currentEvent && currentData) {
                    this.processEvent(currentEvent, currentData);
                }
                this.updateCurrentMessage();
            } finally {
                reader.releaseLock();
            }
        }

        processLine(line, currentEvent, currentData) {
            line = line.replace(/\r$/, '');
            if (!line.trim()) {
                if (currentEvent && currentData) {
                    this.processEvent(currentEvent, currentData);
                }
                return {newEvent: '', newData: ''};
            }
            if (line.startsWith('event:')) {
                if (currentEvent && currentData) {
                    this.processEvent(currentEvent, currentData);
                }
                return {newEvent: line.slice(6).trim(), newData: ''};
            }
            if (line.startsWith('data:')) {
                const data = line.slice(5);
                return {newEvent: currentEvent, newData: currentData ? currentData + '\n' + data : data};
            }
            return {newEvent: currentEvent, newData: currentData};
        }

        processEvent(eventType, data) {
            if (eventType === 'message') {
                this.messageBuffer += (this.messageBuffer ? '\n' : '') + data;
                this.debounceUpdateMessage();
            }
            if (eventType === 'end') {
                this.updateCurrentMessage();
            }
        }

        updateCurrentMessage() {
            if (this.currentAssistantMessage) {
                this.currentAssistantMessage.innerHTML = marked.parse(this.messageBuffer);
                this.scrollToBottom();
            }
        }

        setStreaming(b) {
            this.isStreaming = b;
            this.sendButton.disabled = b;
            this.chatInput.disabled = b;
            this.sendButton.innerHTML = b ? '⏸' : '➤';
            if (!b) {
                this.chatInput.focus();
            }
        }

        scrollToBottom = debounce(() => {
            this.chatMessages.scrollTo({top: this.chatMessages.scrollHeight, behavior: 'smooth'});
        }, 50);
    }

    document.addEventListener('DOMContentLoaded', () => {
        new ChatApp();
    });
</script>
</body>
</html>
