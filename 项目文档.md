# ZJZH-Agent 项目文档

## 项目概述

**ZJZH-Agent** 是一个基于 Spring Boot 3.0 开发的智能对话代理应用，集成了阿里云 DashScope AI 服务，提供智能聊天功能和用户管理系统。

### 基本信息

- **项目名称**: zjzh-agent (智能代理应用)
- **版本**: 0.0.1-SNAPSHOT
- **技术栈**: Spring Boot 3.0.0 + Java 17 + MySQL + MyBatis Plus
- **服务端口**: 7011
- **数据库**: MySQL (ai_tools)

## 技术架构

### 核心技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Spring Boot | 3.0.0 | 主框架 |
| Java | 17 | 开发语言 |
| MySQL | 8.0+ | 数据库 |
| MyBatis Plus | 3.5.5 | ORM框架 |
| DashScope SDK | 2.21.7 | 阿里云AI服务 |
| Spring Security | 3.0.0 | 安全框架 |
| Thymeleaf | 3.0.0 | 模板引擎 |
| Lombok | 1.18.30 | 代码简化 |

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Spring Boot   │    │   阿里云AI      │
│   (Thymeleaf)   │◄──►│   应用服务      │◄──►│   DashScope     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   MySQL数据库   │
                       │   (用户管理)    │
                       └─────────────────┘
```

## 项目结构

```
zjzh-agent/
├── src/
│   ├── main/
│   │   ├── java/com/zjch/agent/
│   │   │   ├── config/                 # 配置类
│   │   │   │   ├── MybatisPlusConfig.java
│   │   │   │   ├── SecurityConfig.java
│   │   │   │   └── WebConfig.java
│   │   │   ├── controller/             # 控制器层
│   │   │   │   ├── ChatController.java
│   │   │   │   ├── LoginController.java
│   │   │   │   └── UserController.java
│   │   │   ├── service/                # 服务层
│   │   │   │   ├── ChatService.java
│   │   │   │   ├── LoginService.java
│   │   │   │   ├── UserService.java
│   │   │   │   └── impl/               # 服务实现
│   │   │   ├── entity/                 # 实体类
│   │   │   │   └── User.java
│   │   │   ├── mapper/                 # 数据访问层
│   │   │   │   └── UserMapper.java
│   │   │   ├── common/                 # 公共类
│   │   │   │   └── Result.java
│   │   │   ├── enums/                  # 枚举类
│   │   │   │   └── UserStatus.java
│   │   │   ├── interceptor/            # 拦截器
│   │   │   │   └── AuthInterceptor.java
│   │   │   └── ZjzhAgentApplication.java
│   │   └── resources/
│   │       ├── templates/              # 前端模板
│   │       │   ├── chat.html
│   │       │   ├── login.html
│   │       │   └── user-management.html
│   │       ├── static/js/              # 静态资源
│   │       ├── sql/                    # 数据库脚本
│   │       │   └── init.sql
│   │       └── application.yml         # 配置文件
│   └── test/                           # 测试代码
├── target/                             # 编译输出
├── pom.xml                            # Maven配置
├── mvnw                               # Maven Wrapper
├── 部署指南.md                        # 部署文档
└── HELP.md                            # 帮助文档
```

## 核心功能模块

### 1. 智能聊天模块

**位置**: `com.zjch.agent.controller.ChatController`

#### 功能特性
- 支持普通聊天和流式聊天
- 集成阿里云 DashScope AI 服务
- 会话管理和上下文保持
- 实时响应和 SSE 流式输出

#### 主要接口

| 接口 | 方法 | 功能 | 参数 |
|------|------|------|------|
| `/` | GET | 聊天主页 | - |
| `/api/chat` | POST | 普通聊天 | message, sessionId |
| `/api/chat/stream` | POST | 流式聊天 | message, sessionId |

#### 核心实现
```java
// 普通聊天
@PostMapping("/api/chat")
public Map<String, Object> chat(@RequestBody ChatRequest request)

// 流式聊天 (SSE)
@PostMapping("/api/chat/stream")
public SseEmitter streamChat(@RequestBody ChatRequest request)
```

### 2. 用户管理模块

**位置**: `com.zjch.agent.controller.UserController`

#### 用户实体 (`User.java`)
```java
@TableName("sys_user")
public class User {
    private Long id;                    // 主键ID
    private String phone;               // 手机号
    private String password;            // 密码
    private String organizationName;    // 机构名称
    private Integer status;             // 状态：1-启用，0-停用
    private Integer isAdmin;            // 是否管理员：1-是，0-否
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
    private Integer isDeleted;          // 逻辑删除标记
}
```

#### 用户状态枚举
```java
public enum UserStatus {
    DISABLED(0, "停用"),
    ENABLED(1, "启用");
}
```

### 3. 登录认证模块

**位置**: `com.zjch.agent.controller.LoginController`

#### 主要功能
- 用户登录/登出
- 会话管理
- 登录状态检查
- Spring Security 集成

#### 主要接口

| 接口 | 方法 | 功能 |
|------|------|------|
| `/login` | GET | 登录页面 |
| `/api/login` | POST | 用户登录 |
| `/api/logout` | POST | 用户登出 |
| `/api/check-login` | GET | 检查登录状态 |

### 4. 安全配置

**位置**: `com.zjch.agent.config.SecurityConfig`

- Spring Security 配置
- 认证拦截器
- 会话管理
- 权限控制

## 数据库设计

### 用户表 (sys_user)

```sql
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `organization_name` varchar(100) DEFAULT NULL COMMENT '机构名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-停用',
  `is_admin` tinyint NOT NULL DEFAULT '0' COMMENT '是否为管理员：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：1-已删除，0-未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 默认账户

| 账户类型 | 手机号 | 密码 | 角色 |
|----------|--------|------|------|
| 管理员 | 13800138000 | admin123 | 管理员 |
| 普通用户 | 13800138001 | user123 | 普通用户 |

## 配置说明

### 应用配置 (application.yml)

```yaml
server:
  port: 7011

spring:
  application:
    name: zjzh-agent
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: root
    password: root111111
  data:
    redis:
      host: 127.0.0.1
      port: 6379

# DashScope AI 配置
dashscope:
  api-key: sk-4dc7b44c2e6c4f88b9f5664d5a81708c
  app-id: 3a1d03791d204389b76431c36cbc5dff
```

### Maven 配置 (pom.xml)

#### 核心依赖
- `spring-boot-starter-web`: Web 开发
- `spring-boot-starter-thymeleaf`: 模板引擎
- `spring-boot-starter-security`: 安全框架
- `mybatis-plus-boot-starter`: ORM 框架
- `mysql-connector-java`: MySQL 驱动
- `dashscope-sdk-java`: 阿里云 AI SDK
- `lombok`: 代码简化

## API 接口文档

### 聊天接口

#### 1. 普通聊天
```http
POST /api/chat
Content-Type: application/json

{
    "message": "你好",
    "sessionId": "session_123"
}
```

**响应**:
```json
{
    "code": 200,
    "message": "聊天成功",
    "data": {
        "message": "AI回复内容",
        "sessionId": "session_123"
    }
}
```

#### 2. 流式聊天
```http
POST /api/chat/stream
Content-Type: application/json

{
    "message": "请介绍一下人工智能",
    "sessionId": "session_123"
}
```

**响应** (SSE 流):
```
event: message
data: 人工智能

event: message
data: 是一门

event: end
data: 
```

### 用户管理接口

#### 1. 用户登录
```http
POST /api/login
Content-Type: application/json

{
    "phone": "13800138000",
    "password": "admin123"
}
```

#### 2. 检查登录状态
```http
GET /api/check-login
```

#### 3. 用户登出
```http
POST /api/logout
```

### 系统接口

#### 健康检查
```http
GET /inner/health/alive
```
**响应**: `ok`

## 前端界面

### 1. 聊天界面 (chat.html)
- 响应式设计
- 实时消息显示
- 支持 Markdown 渲染
- 流式消息展示
- 用户信息显示

### 2. 登录界面 (login.html)
- 手机号登录
- 密码验证
- 会话管理

### 3. 用户管理界面 (user-management.html)
- 用户列表
- 用户状态管理
- 权限控制

## 部署指南

### 系统要求
- **Java**: JDK 17+
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **构建工具**: Maven 3.6+

### 部署步骤

1. **环境准备**
```bash
# 检查 Java 版本
java -version

# 创建数据库
mysql -u root -p
CREATE DATABASE ai_tools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **编译打包**
```bash
# 使用 Maven Wrapper 打包
./mvnw clean package -DskipTests
```

3. **运行应用**
```bash
# 运行 JAR 文件
java -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar

# 或使用 Maven 直接运行
./mvnw spring-boot:run
```

4. **验证部署**
```bash
# 健康检查
curl http://localhost:7011/inner/health/alive
```

### 生产环境配置

```bash
# 后台运行
nohup java -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar > app.log 2>&1 &

# JVM 参数优化
java -Xms512m -Xmx2g -XX:+UseG1GC \
     -jar target/zjzh-agent-0.0.1-SNAPSHOT.jar
```

## 开发指南

### 开发环境搭建

1. **克隆项目**
```bash
git clone <repository-url>
cd zjzh-agent
```

2. **配置数据库**
- 创建 `ai_tools` 数据库
- 执行 `src/main/resources/sql/init.sql` 初始化脚本

3. **配置 AI 服务**
- 申请阿里云 DashScope API Key
- 更新 `application.yml` 中的配置

4. **启动开发服务**
```bash
./mvnw spring-boot:run
```

### 代码规范

- 使用 Lombok 简化代码
- 遵循 Spring Boot 最佳实践
- 使用 MyBatis Plus 进行数据访问
- 统一异常处理和返回格式

### 扩展开发

#### 添加新的聊天功能
1. 在 `ChatService` 中添加新方法
2. 在 `ChatController` 中添加对应接口
3. 更新前端界面支持新功能

#### 添加新的用户功能
1. 扩展 `User` 实体类
2. 更新数据库表结构
3. 在 `UserService` 中实现业务逻辑

## 常见问题

### 1. 端口冲突
```bash
# 查看端口占用
lsof -i :7011

# 修改端口
--server.port=8080
```

### 2. 数据库连接失败
- 检查 MySQL 服务状态
- 验证连接参数
- 确认用户权限

### 3. AI 服务调用失败
- 检查 API Key 是否正确
- 验证网络连接
- 查看 DashScope 服务状态

### 4. 前端页面无法访问
- 检查 Thymeleaf 模板路径
- 验证静态资源配置
- 查看控制器映射

## 技术支持

### 相关文档
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [MyBatis Plus 文档](https://baomidou.com/)
- [阿里云 DashScope 文档](https://help.aliyun.com/zh/dashscope/)

### 联系方式
如遇到技术问题，请检查：
1. 日志错误信息
2. 配置文件设置
3. 数据库连接状态
4. 网络环境

---

**最后更新**: 2024年12月
**文档版本**: v1.0