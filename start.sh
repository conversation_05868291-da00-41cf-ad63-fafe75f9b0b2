#!/bin/bash

# 项目启动脚本
# 项目名称: zjzh-agent
# 描述: Spring Boot 应用启动脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
JAVA_HOME=${JAVA_HOME:-/opt/jdk-17}
PROJECT_NAME="zjzh-agent"
VERSION="0.0.1-SNAPSHOT"
JAR_FILE="target/${PROJECT_NAME}-${VERSION}.jar"
PROFILE=${SPRING_PROFILES_ACTIVE:-prod}
JVM_OPTS=${JVM_OPTS:-"-Xms512m -Xmx1024m -XX:+UseG1GC"}
SERVER_PORT=${SERVER_PORT:-8080}

# 检查并设置Java环境
setup_java() {
    log_info "设置Java环境..."
    
    if [ -d "$JAVA_HOME" ]; then
        export PATH=$JAVA_HOME/bin:$PATH
        log_success "Java路径已设置: $JAVA_HOME"
    else
        log_warn "指定的JAVA_HOME不存在: $JAVA_HOME"
        log_info "尝试使用系统默认Java..."
    fi
    
    # 检查Java版本
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$JAVA_VERSION" -ge 17 ]; then
            log_success "Java版本检查通过"
            java -version
        else
            log_error "需要Java 17或更高版本，当前版本: $JAVA_VERSION"
            exit 1
        fi
    else
        log_error "未找到Java命令"
        exit 1
    fi
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    if [ ! -f "pom.xml" ]; then
        log_error "未找到pom.xml文件，请确保在项目根目录执行"
        exit 1
    fi
    
    # 检查Maven
    if command -v mvn &> /dev/null; then
        log_info "使用系统Maven构建..."
        mvn clean package -DskipTests
    elif [ -f "./mvnw" ]; then
        log_info "使用项目Maven Wrapper构建..."
        chmod +x ./mvnw
        ./mvnw clean package -DskipTests
    else
        log_error "未找到Maven或Maven Wrapper"
        exit 1
    fi
    
    if [ $? -eq 0 ]; then
        log_success "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 检查JAR文件
check_jar() {
    log_info "检查JAR文件..."
    
    if [ ! -f "$JAR_FILE" ]; then
        log_warn "JAR文件不存在: $JAR_FILE"
        log_info "尝试构建项目..."
        build_project
    fi
    
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件仍然不存在: $JAR_FILE"
        exit 1
    fi
    
    log_success "JAR文件检查通过: $JAR_FILE"
}

# 启动应用
start_application() {
    log_info "启动Spring Boot应用..."
    log_info "配置文件: $PROFILE"
    log_info "JVM参数: $JVM_OPTS"
    log_info "服务端口: $SERVER_PORT"
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动命令
    java $JVM_OPTS \
        -Dspring.profiles.active=$PROFILE \
        -Dserver.port=$SERVER_PORT \
        -Dfile.encoding=UTF-8 \
        -Djava.awt.headless=true \
        -jar "$JAR_FILE" \
        --logging.file.name=logs/application.log
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -b, --build    强制重新构建项目"
    echo "  -p, --profile  指定Spring配置文件 (默认: prod)"
    echo "  --port         指定服务端口 (默认: 8080)"
    echo ""
    echo "环境变量:"
    echo "  JAVA_HOME              Java安装路径 (默认: /opt/jdk-17)"
    echo "  SPRING_PROFILES_ACTIVE Spring配置文件"
    echo "  JVM_OPTS              JVM启动参数"
    echo "  SERVER_PORT           服务端口"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认配置启动"
    echo "  $0 -p dev              # 使用dev配置启动"
    echo "  $0 --port 9090         # 指定端口9090启动"
    echo "  $0 -b                  # 强制重新构建并启动"
}

# 主函数
main() {
    local force_build=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                force_build=true
                shift
                ;;
            -p|--profile)
                PROFILE="$2"
                shift 2
                ;;
            --port)
                SERVER_PORT="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始启动 $PROJECT_NAME..."
    
    # 执行启动流程
    setup_java
    
    if [ "$force_build" = true ]; then
        build_project
    else
        check_jar
    fi
    
    start_application
}

# 信号处理
trap 'log_info "收到退出信号，正在关闭应用..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"